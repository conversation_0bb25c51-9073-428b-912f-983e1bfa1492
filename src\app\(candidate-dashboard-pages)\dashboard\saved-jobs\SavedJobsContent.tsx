"use client";

import { useSearchParams, useRouter } from "next/navigation";
import { Suspense, useState } from "react";
import { toast } from "sonner";

import JobShortCardDashboard from "@/components/Cards/JobShortCardDashboard";
import { Pagination } from "@/components/JobSearch/Pagination";
import JobNavigation from "@/components/Sections/JobNavigation";
import { DEFAULT_IMAGE } from "@/constants/app.constants";
import { useGetSavedJobs } from "@/hooks/useQuery";
import { formatDate } from "@/utils/formatDate";
// import { formatDate } from "@/lib/utils";

const navItems = [
  { href: "/dashboard/applied-jobs", label: "Applied Jobs" },
  { href: "/dashboard/shortlisted-jobs", label: "Shortlisted Jobs" },
  { href: "/dashboard/saved-jobs", label: "Saved Jobs" },
];

export default function SavedJobsContent() {
  const searchParams = useSearchParams();
  const router = useRouter();

  // Get initial page and limit from URL or use defaults
  const initialPage = searchParams.get("page") ? parseInt(searchParams.get("page")!) : 1;
  const initialLimit = searchParams.get("limit") ? parseInt(searchParams.get("limit")!) : 10;

  // State for pagination
  const [page, setPage] = useState(initialPage);
  const [limit, setLimit] = useState(initialLimit);

  // Use the hook to fetch saved jobs
  const { data, isLoading, isError } = useGetSavedJobs(
    { page, limit }
    // { refetchOnWindowFocus: false }
  );

  // Extract data from the response
  const savedJobs = data?.data?.savedJobs || [];
  const pagination = data?.data?.pagination;

  // Update URL when page or limit changes
  const updateUrl = (page: number, limit: number) => {
    const params = new URLSearchParams();
    params.set("page", page.toString());
    params.set("limit", limit.toString());
    router.push(`/dashboard/saved-jobs?${params.toString()}`, { scroll: false });
  };

  // Handle page change
  const onPageChange = (page: number) => {
    setPage(page);
    updateUrl(page, limit);
  };

  // Handle limit change
  const onLimitChange = (newLimit: number) => {
    setLimit(newLimit);
    setPage(1); // Reset to page 1 when changing limit
    updateUrl(1, newLimit);
  };

  // Format salary range
  const formatSalaryRange = (job: { salaryRangeStart?: number; salaryRangeEnd?: number }) => {
    if (!job?.salaryRangeStart && !job?.salaryRangeEnd) {
      return "Not specified";
    }

    const start = job.salaryRangeStart ? `$${job.salaryRangeStart.toLocaleString()}` : "";

    const end = job.salaryRangeEnd ? `$${job.salaryRangeEnd.toLocaleString()}` : "";

    return start && end ? `${start} - ${end}` : start || end;
  };

  return (
    <div className="space-y-6">
      <JobNavigation navItems={navItems} />

      {isLoading ? (
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-orange-100"></div>
        </div>
      ) : isError ? (
        <div className="text-center py-8 text-red-500">
          Error loading saved jobs. Please try again later.
        </div>
      ) : savedJobs.length === 0 ? (
        <div className="text-center py-8">You haven&apos;t saved any jobs yet.</div>
      ) : (
        <div className="space-y-6">
          <Suspense fallback={<div>Loading...</div>}>
            {savedJobs.map((savedJob) => {
              const job = savedJob.job;
              return (
                <JobShortCardDashboard
                  key={savedJob?._id}
                  jobId={job?._id}
                  imageUrl={job?.recruiterProfile?.companyProfile?.profilePicture || DEFAULT_IMAGE}
                  jobTitle={job?.jobTitle}
                  companyName={job?.recruiterProfile.companyProfile.companyName}
                  category={job?.jobCategory.replace(/_/g, " ")}
                  jobType={job?.jobType.replace(/_/g, " ")}
                  cityName={job?.location.city}
                  salaryRange={formatSalaryRange(job)}
                  deadline={formatDate(job?.applicationDeadline)}
                  savedJobs
                  onApplySuccess={() => {
                    toast.success("Application submitted successfully");
                  }}
                  isSaved={true}
                  alreadyApplied={savedJob.alreadyApplied}
                  isDeleted={job.isDeleted}
                />
              );
            })}
          </Suspense>

          {pagination && pagination.pages > 1 && (
            <Pagination
              pagination={{
                currentPage: pagination.currentPage,
                pages: pagination.pages,
                hasNextPage: pagination.currentPage < pagination.pages,
                totalRecords: pagination.totalRecords,
                skip: (pagination.currentPage - 1) * pagination.pageSize,
                limit: pagination.pageSize,
                pageSize: pagination.pageSize,
              }}
              onPageChange={onPageChange}
              onLimitChange={onLimitChange}
            />
          )}
        </div>
      )}
    </div>
  );
}
