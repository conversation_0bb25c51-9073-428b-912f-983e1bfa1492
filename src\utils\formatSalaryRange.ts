export function formatSalaryRange(
  salaryType: string,
  salaryRangeStart?: number,
  salaryRangeEnd?: number
): string {
  if (!salaryRangeStart && !salaryRangeEnd) {
    return "Negotiable";
  }

  const formatSalary = (amount: number): string => {
    if (amount >= 1000) {
      return `$${(amount / 1000).toFixed(0)}k`;
    }
    return `$${amount}`;
  };

  const start = salaryRangeStart ? formatSalary(salaryRangeStart) : "";
  const end = salaryRangeEnd ? formatSalary(salaryRangeEnd) : "";

  if (start && end) {
    let period = "";

    switch (salaryType) {
      case "HOURLY":
        period = "/ hour";
        break;
      case "DAILY":
        period = "/ day";
        break;
      case "WEEKLY":
        period = "/ week";
        break;
      case "MONTHLY":
        period = "/ month";
        break;
      case "ANNUAL":
        period = "/ year";
        break;
      case "PROJECT_BASED":
        period = "/ project";
        break;
      default:
        period = "";
    }

    return `${start} - ${end} ${period}`;
  }

  return start || end || "Negotiable";
}
