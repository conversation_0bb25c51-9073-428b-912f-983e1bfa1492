"use client";

import { useQueryClient } from "@tanstack/react-query";
import { useRouter } from "next/navigation";
import React, { useEffect, useState } from "react";
import { useForm, type SubmitHandler } from "react-hook-form";
import { toast } from "sonner";
import AchievementInfo from "../../Cards/AchievementInfo";
import { AchievementIcon } from "../../Icons";
import { Input } from "../../ui/input";
import { Label } from "../../ui/label";
import { Textarea } from "../../ui/textarea";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { useUpdateCompanyProfile } from "@/hooks/useMutation";
import { useGetCompanyProfile } from "@/hooks/useQuery";
import { ICompany, ICompanyAchievement } from "@/types/query.types";

const inputClasses = "h-12 px-4 rounded-full border border-gray-300 w-full";

const AchivementsCompanyForm = () => {
  const router = useRouter();
  const queryClient = useQueryClient();
  const { data: companyData, isLoading: isProfileLoading } = useGetCompanyProfile();
  const companyProfile = companyData?.data;

  const [isOpen, setIsOpen] = useState(false);
  const [achievements, setAchievements] = useState<ICompanyAchievement[]>([]);
  const [editingAchievement, setEditingAchievement] = useState<ICompanyAchievement | null>(null);

  // Synchronize achievements state with companyProfile data
  useEffect(() => {
    if (companyProfile?.companyAchievements) {
      setAchievements(companyProfile.companyAchievements);
    }
  }, [companyProfile]);

  const {
    register,
    handleSubmit,
    reset,
    setValue,
    formState: { errors },
  } = useForm<Omit<ICompanyAchievement, "_id">>({
    defaultValues: {
      title: "",
      date: "",
      eventOrInstitute: "",
      detail: "",
    },
  });

  const { mutate: updateProfile, isPending } = useUpdateCompanyProfile({
    onSuccess: () => {
      toast.success("Company achievements updated successfully");
      queryClient.invalidateQueries({ queryKey: ["get-company-profile"] });
      setIsOpen(false);
      reset();
      setEditingAchievement(null);
    },
    onError: (error) => {
      toast.error(error.response?.data?.message || "Failed to update achievements");
    },
  });

  const onSubmit: SubmitHandler<Omit<ICompanyAchievement, "_id">> = async (formData) => {
    if (!companyProfile?._id) {
      toast.error("Company profile not found");
      return;
    }

    if (editingAchievement) {
      // Update existing achievement
      const updatedAchievements = achievements.map((achievement) =>
        achievement._id === editingAchievement._id
          ? {
              ...achievement,
              ...formData,
              date: new Date(formData.date).toISOString(),
            }
          : achievement
      );
      setAchievements(updatedAchievements);

      const updatedProfile: Partial<ICompany> = {
        companyAchievements: updatedAchievements,
      };

      updateProfile(updatedProfile);
    } else {
      // Add new achievement
      const newAchievement: ICompanyAchievement = {
        ...formData,
        date: new Date(formData.date).toISOString(),
        _id: Date.now().toString(), // Temporary ID for new achievements
      };

      const updatedAchievements = [...achievements, newAchievement];
      setAchievements(updatedAchievements);

      const updatedProfile = {
        companyAchievements: updatedAchievements,
      };

      updateProfile(updatedProfile);
    }
  };

  const handleDelete = (achievementId: string) => {
    if (!companyProfile?._id) {
      toast.error("Company profile not found");
      return;
    }

    const updatedAchievements = achievements.filter(
      (achievement) => achievement._id !== achievementId
    );
    setAchievements(updatedAchievements);

    const updatedProfile = {
      companyAchievements: updatedAchievements,
    };

    updateProfile(updatedProfile);
  };

  const handleEdit = (achievement: ICompanyAchievement) => {
    setEditingAchievement(achievement);
    setValue("title", achievement.title);
    setValue(
      "date",
      new Date(achievement.date).toISOString().split("T")[0] || new Date().toISOString()
    );
    setValue("eventOrInstitute", achievement.eventOrInstitute);
    setValue("detail", achievement.detail || "");
    setIsOpen(true);
  };

  const formatDate = (date: string) => {
    return new Intl.DateTimeFormat("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    }).format(new Date(date));
  };

  if (isProfileLoading) {
    return (
      <div className="text-center py-8">
        <div className="animate-spin rounded-full h-10 w-10 border-b-2 border-gray-900 mx-auto"></div>
        <p className="text-gray-500 mt-4">Loading achievements...</p>
      </div>
    );
  }

  return (
    <div>
      <div className="md:flex items-center justify-between mb-10">
        <div className="mb-4 md:mb-0">
          <h2 className="text-blue-200 text-2xl font-bold">Achievements</h2>
          <h3 className="text-4xl font-medium text-black-100 mt-3">
            Tell us about your Achievements
          </h3>
        </div>
        <div>
          <Dialog open={isOpen} onOpenChange={setIsOpen}>
            <DialogTrigger className="border-orange-100 border rounded-full px-3 py-3 flex items-center gap-x-3 font-medium text-orange-100">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="30"
                height="30"
                fill="none"
                viewBox="0 0 30 30"
              >
                <path
                  stroke="#EC761E"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M15 29c7.732 0 14-6.268 14-14S22.732 1 15 1 1 7.268 1 15s6.268 14 14 14M10.333 15h9.334M15 10.333v9.333"
                ></path>
              </svg>
              {editingAchievement ? "Edit Achievement" : "Add Achievement"}
            </DialogTrigger>
            <DialogContent className="md:max-w-[920px] max-h-[80vh] overflow-y-auto">
              <DialogHeader className="text-left">
                <DialogTitle className="text-2xl text-orange-100 font-bold mb-5">
                  {editingAchievement ? "Edit Achievement" : "Add Achievement"}
                </DialogTitle>
                <form onSubmit={handleSubmit(onSubmit)}>
                  <div className="mb-6">
                    <Label htmlFor="title" className="mb-3 block">
                      Title
                    </Label>
                    <Input
                      className={inputClasses}
                      id="title"
                      {...register("title", {
                        required: "Title is required",
                      })}
                    />
                    {errors.title && <p className="text-red-500 text-sm">{errors.title.message}</p>}
                  </div>

                  <div className="grid sm:grid-cols-2 gap-6 mb-6">
                    <div>
                      <Label htmlFor="date" className="mb-3 block">
                        Date
                      </Label>
                      <Input
                        className={`${inputClasses} w-full block text-gray-100`}
                        type="date"
                        id="date"
                        {...register("date", {
                          required: "Date is required",
                        })}
                      />
                      {errors.date && <p className="text-red-500 text-sm">{errors.date.message}</p>}
                    </div>
                    <div>
                      <Label htmlFor="eventOrInstitute" className="mb-3 block">
                        Event/Institute
                      </Label>
                      <Input
                        className={inputClasses}
                        id="eventOrInstitute"
                        {...register("eventOrInstitute", {
                          required: "Event/Institute is required",
                        })}
                      />
                      {errors.eventOrInstitute && (
                        <p className="text-red-500 text-sm">{errors.eventOrInstitute.message}</p>
                      )}
                    </div>
                  </div>
                  <div className="mb-6">
                    <Label htmlFor="detail" className="mb-3 block">
                      Add Some detail <span className="text-[#8B8B8D] text-sm">(optional)</span>
                    </Label>
                    <Textarea
                      id="detail"
                      className="min-h-[195px] px-4 py-2 rounded-3xl border border-gray-300"
                      placeholder="Add description..."
                      {...register("detail")}
                    />
                  </div>

                  <div className="flex flex-wrap gap-x-5 mt-10">
                    <button
                      type="button"
                      onClick={() => {
                        setIsOpen(false);
                        setEditingAchievement(null);
                        reset();
                      }}
                      className="font-bold py-4 px-10 font-base rounded-full inline-flex items-center justify-center space-x-2 bg-gray-300 text-gray-100"
                    >
                      Cancel
                    </button>
                    <button
                      disabled={isPending}
                      type="submit"
                      className="font-bold py-4 px-10 font-base rounded-full inline-flex items-center justify-center space-x-2 bg-orange-100 text-white"
                    >
                      {isPending ? "Loading..." : "Save Details"}
                    </button>
                  </div>
                </form>
              </DialogHeader>
            </DialogContent>
          </Dialog>
        </div>
      </div>
      <div className="space-y-6">
        {achievements.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            No achievements added yet. Click &quot;Add Achievement&quot; to add one.
          </div>
        ) : (
          achievements.map((achievement) => (
            <AchievementInfo
              detail={achievement.detail}
              key={achievement._id}
              icon={<AchievementIcon />}
              date={formatDate(achievement.date)} // Format the date
              title={achievement.title}
              eventOrInstitute={achievement.eventOrInstitute}
              onEdit={() => handleEdit(achievement)}
              onDelete={() => handleDelete(achievement._id)}
            />
          ))
        )}
      </div>

      <div className="flex gap-5 mt-10">
        <button
          onClick={() => router.back()}
          className="font-bold py-4 px-10 font-base rounded-full inline-flex items-center justify-center space-x-2 bg-gray-300 text-gray-100"
        >
          Go Back
        </button>
        <button
          disabled={isPending}
          onClick={() => router.push("/company-dashboard/all-jobs")}
          className="font-bold py-4 px-10 font-base rounded-full inline-flex items-center justify-center space-x-2 bg-orange-100 text-white"
        >
          {isPending ? "Loading..." : "Save Profile"}
        </button>
      </div>
    </div>
  );
};

export default AchivementsCompanyForm;
