"use client";

import React, { useState } from "react";
import { toast } from "sonner";
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { useLoader } from "@/context/LoaderContext";
import { useDeactivateAccount, useLogout } from "@/hooks/useMutation";

const DeactivateAccountButton = () => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const { showLoader, hideLoader } = useLoader();

  const { mutateAsync: deactivateAccount, isPending: isDeactivating } = useDeactivateAccount({
    onSuccess: () => {
      toast.success("Account Deactivated", {
        description: "Your account has been successfully deactivated.",
      });
      // Show loader before logging out
      showLoader("Logging you out...");
      logout(); // Log the user out after deactivation
    },
    onError: (error) => {
      hideLoader(); // Hide loader if deactivation fails
      toast.error("Failed to Deactivate Account", {
        description: error.response?.data?.message || "Something went wrong.",
      });
    },
  });

  const { mutate: logout, isPending: isLoggingOut } = useLogout({
    onSuccess: () => {
      toast.success("Logged out successfully", {
        description: "Your account has been deactivated and you have been logged out.",
      });

      // Add a small delay before redirecting to show the loader
      setTimeout(() => {
        hideLoader();
        window.location.href = "/"; // Redirect to the home page after logout
      }, 1500); // 1.5 seconds delay for better user experience
    },
    onError: (error) => {
      hideLoader();
      toast.error("Logout failed", {
        description: error.response?.data?.message || "Something went wrong. Please try again.",
      });
    },
  });

  const handleDeactivate = async () => {
    try {
      showLoader("Deactivating your account...");
      await deactivateAccount();
      setIsModalOpen(false);
      // The logout process will be handled by the deactivateAccount onSuccess callback
    } catch {
      // Hide loader if deactivation fails
      hideLoader();
      // Error is already handled by the onError callback
    }
  };

  return (
    <>
      {/* Deactivate Account Button */}
      <Button
        onClick={() => setIsModalOpen(true)}
        className="px-5 py-2 rounded-full text-gray-100 bg-gray-300 inline-flex items-center justify-end gap-x-2"
      >
        Deactivate
        <span>
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="16"
            height="17"
            fill="none"
            viewBox="0 0 16 17"
          >
            <path
              fill="#737373"
              d="m12.39 4.414.98-1.078a.5.5 0 0 0-.74-.673l-.98 1.078a5.997 5.997 0 0 0-8.04 8.844l-.98 1.078a.5.5 0 1 0 .74.673l.98-1.078a5.997 5.997 0 0 0 8.04-8.844M3 8.5a5 5 0 0 1 7.975-4.016l-6.688 7.358A4.98 4.98 0 0 1 3 8.5m5 5a4.97 4.97 0 0 1-2.975-.985l6.687-7.358A4.997 4.997 0 0 1 8 13.5"
            ></path>
          </svg>
        </span>
      </Button>

      {/* Warning Modal */}
      <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Deactivate Account</DialogTitle>
          </DialogHeader>
          <p className="text-gray-600">
            Are you sure you want to deactivate your account? This action cannot be undone.
          </p>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsModalOpen(false)}
              disabled={isDeactivating || isLoggingOut}
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleDeactivate}
              disabled={isDeactivating || isLoggingOut}
            >
              {isDeactivating ? "Deactivating..." : "Confirm"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default DeactivateAccountButton;
