import { Loader2 } from "lucide-react";
import React from "react";
import { XCircleIcon } from "../Icons";

interface ICVShowCard {
  docName: string;
  date: string;
  onDelete: () => void;
  isDeleting: boolean;
  isActive: boolean;
  isActivating?: boolean;
  onToggleActive: () => void;
}

const CVShowCard = ({
  docName,
  date,
  onDelete,
  isDeleting,
  isActive,
  isActivating = false,
  onToggleActive,
}: ICVShowCard) => {
  return (
    <div className="border border-gray-300 p-6 flex flex-col gap-y-3 sm:gap-y-0 rounded-2xl sm:flex-row justify-between sm:items-center sm:self-stretch sm:rounded-full">
      <div>
        <p className="text-base text-orange-100 font-medium">{docName}</p>
      </div>
      <div>
        <p className="text-gray-100 font-medium text-base">Uploaded on: {date}</p>
      </div>
      <div className="flex items-center gap-4">
        <div className="flex items-center gap-2">
          <span
            className={`text-sm font-medium flex items-center gap-1 ${isActive ? "text-green-500" : "text-gray-500"}`}
          >
            {isActivating ? <Loader2 className="h-3 w-3 animate-spin" /> : null}
            {isActive ? "Active" : "Inactive"}
          </span>
          <button
            onClick={onToggleActive}
            disabled={isActivating}
            className={`px-3 py-1 rounded-md text-sm font-medium flex items-center gap-1 transition-colors
              ${isActivating ? "bg-gray-200 text-gray-500 cursor-not-allowed" : ""}
              ${
                isActive
                  ? "bg-gray-200 hover:bg-gray-300 text-gray-700"
                  : "bg-green-100 hover:bg-green-200 text-green-700"
              }`}
          >
            {isActivating && <Loader2 className="h-3 w-3 animate-spin" />}
            {isActivating
              ? isActive
                ? "Deactivating..."
                : "Activating..."
              : isActive
                ? "Deactivate"
                : "Activate"}
          </button>
        </div>
        <button
          onClick={onDelete}
          disabled={isDeleting}
          className="text-red-500 hover:text-red-700 transition-colors flex items-center gap-1"
          aria-label="Delete CV"
        >
          {isDeleting ? (
            <>
              <Loader2 className="h-4 w-4 animate-spin" />
              <span className="text-sm">Deleting...</span>
            </>
          ) : (
            <XCircleIcon />
          )}
        </button>
      </div>
    </div>
  );
};

export default CVShowCard;
