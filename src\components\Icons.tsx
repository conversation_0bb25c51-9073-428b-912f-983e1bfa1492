import React from "react";

export const ArrowIcon: React.FC = () => {
  return (
    <svg width="30" height="30" viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg">
      <circle cx="15" cy="15" r="15" fill="white" />
      <path
        d="M16.25 18.75L15.375 17.8438L17.5938 15.625H10V14.375H17.5938L15.375 12.1562L16.25 11.25L20 15L16.25 18.75Z"
        fill="#EC761E"
      />
    </svg>
  );
};

export const GlobeIcon: React.FC = () => {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="29" height="29" fill="none" viewBox="0 0 29 29">
      <path
        stroke="#EC761E"
        d="M26.167 14.5a11.668 11.668 0 1 1-23.337 0 11.668 11.668 0 0 1 23.337 0Z"
      ></path>
      <path
        stroke="#EC761E"
        d="M19.167 14.5c0 1.532-.121 3.05-.356 4.465-.233 1.415-.577 2.7-1.011 3.785-.433 1.083-.948 1.942-1.514 2.529-.567.585-1.173.888-1.786.888-.612 0-1.219-.303-1.785-.888-.567-.587-1.081-1.447-1.514-2.53s-.778-2.368-1.013-3.784a27.5 27.5 0 0 1-.354-4.465c0-1.532.12-3.05.354-4.465.235-1.415.579-2.7 1.013-3.785.433-1.083.947-1.942 1.513-2.529.567-.584 1.174-.888 1.786-.888s1.22.302 1.785.888c.567.587 1.082 1.447 1.515 2.53.434 1.083.778 2.368 1.011 3.784a27.4 27.4 0 0 1 .356 4.465Z"
      ></path>
      <path stroke="#EC761E" strokeLinecap="round" d="M2.834 14.5h23.333"></path>
    </svg>
  );
};

export const BagIcon: React.FC = () => {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="29" height="29" fill="none" viewBox="0 0 29 29">
      <path
        stroke="#EC761E"
        d="M4 14.5c0 4.4 0 8.933 1.538 10.3 1.537 1.367 4.013 1.367 8.962 1.367s7.425 0 8.962-1.368C25 23.432 25 18.9 25 14.5"
      ></path>
      <path
        stroke="#EC761E"
        d="m17.603 17.069 7.231-2.17c.479-.143.719-.215.896-.357.155-.125.276-.287.35-.471.087-.21.087-.461.087-.962 0-1.969 0-2.953-.385-3.704a3.5 3.5 0 0 0-1.52-1.52C23.51 7.5 22.526 7.5 20.556 7.5H8.443c-1.97 0-2.953 0-3.705.385a3.5 3.5 0 0 0-1.52 1.52c-.385.752-.385 1.735-.385 3.704 0 .5 0 .75.085.962.075.184.195.346.35.471.179.142.418.213.899.358l7.23 2.169"
      ></path>
      <path
        stroke="#EC761E"
        strokeLinecap="round"
        d="M11.198 5.167a3.502 3.502 0 0 1 6.604 0m-.969 9.916h-4.666a.583.583 0 0 0-.584.584v2.522a.58.58 0 0 0 .367.541l.816.327a4.67 4.67 0 0 0 3.468 0l.816-.327a.58.58 0 0 0 .367-.541v-2.522a.583.583 0 0 0-.584-.584Z"
      ></path>
    </svg>
  );
};

export const CalenderIcon: React.FC = () => {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="60" height="60" fill="none" viewBox="0 0 60 60">
      <rect width="60" height="60" fill="#FEF7EE" rx="30"></rect>
      <path
        fill="#EC761E"
        d="M37.5 21h-2.25v-.75a.75.75 0 1 0-1.5 0V21h-7.5v-.75a.75.75 0 1 0-1.5 0V21H22.5a1.5 1.5 0 0 0-1.5 1.5v15a1.5 1.5 0 0 0 1.5 1.5h15a1.5 1.5 0 0 0 1.5-1.5v-15a1.5 1.5 0 0 0-1.5-1.5m-12.75 1.5v.75a.75.75 0 1 0 1.5 0v-.75h7.5v.75a.75.75 0 1 0 1.5 0v-.75h2.25v3h-15v-3zm12.75 15h-15V27h15z"
      ></path>
    </svg>
  );
};

export const LocationIcon: React.FC = () => {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="60" height="60" fill="none" viewBox="0 0 60 60">
      <rect width="60" height="60" fill="#FEF7EE" rx="30"></rect>
      <path
        fill="#EC761E"
        d="M30 24a3.75 3.75 0 1 0 0 7.5 3.75 3.75 0 0 0 0-7.5m0 6a2.25 2.25 0 1 1 0-4.5 2.25 2.25 0 0 1 0 4.5m0-10.5a8.26 8.26 0 0 0-8.25 8.25c0 2.944 1.36 6.064 3.938 9.023a23.8 23.8 0 0 0 3.885 3.591.75.75 0 0 0 .861 0 23.8 23.8 0 0 0 3.878-3.59c2.574-2.96 3.938-6.08 3.938-9.024A8.26 8.26 0 0 0 30 19.5m0 19.313c-1.55-1.22-6.75-5.696-6.75-11.063a6.75 6.75 0 0 1 13.5 0c0 5.365-5.2 9.844-6.75 11.063"
      ></path>
    </svg>
  );
};

export const PayIcon: React.FC = () => {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="60" height="60" fill="none" viewBox="0 0 60 60">
      <rect width="60" height="60" fill="#FEF7EE" rx="30"></rect>
      <path
        fill="#EC761E"
        d="M40.898 23.625a.75.75 0 0 0-.727-.038c-4.025 1.97-6.9 1.047-9.938.074-3.187-1.021-6.492-2.076-11.057.153a.75.75 0 0 0-.426.677v11.244a.75.75 0 0 0 1.08.674c4.024-1.969 6.898-1.047 9.941-.073 1.804.576 3.642 1.164 5.719 1.164 1.602 0 3.35-.349 5.335-1.318a.75.75 0 0 0 .421-.673V24.265a.75.75 0 0 0-.349-.64M39.75 35.032c-3.806 1.703-6.586.814-9.521-.124-1.804-.577-3.642-1.165-5.719-1.165-1.46.008-2.904.294-4.256.844v-9.619c3.806-1.703 6.586-.814 9.521.124 2.935.939 5.957 1.908 9.975.323zM30 27a3 3 0 1 0 0 5.999A3 3 0 0 0 30 27m0 4.5a1.5 1.5 0 1 1 0-3 1.5 1.5 0 0 1 0 3M23.25 27v4.5a.75.75 0 1 1-1.5 0V27a.75.75 0 1 1 1.5 0m13.5 6v-4.5a.75.75 0 1 1 1.5 0V33a.75.75 0 1 1-1.5 0"
      ></path>
    </svg>
  );
};

export const ExperienceIcon: React.FC = () => {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="60" height="60" fill="none" viewBox="0 0 60 60">
      <rect width="60" height="60" fill="#FEF7EE" rx="30"></rect>
      <path
        fill="#EC761E"
        d="M38.725 31.539a9.06 9.06 0 0 0-3.756-5.289H40.5a.75.75 0 1 0 0-1.5h-7.594a3 3 0 0 0-5.812 0H19.5a.75.75 0 1 0 0 1.5h5.531a9.06 9.06 0 0 0-3.756 5.288 3 3 0 1 0 1.514.15 7.56 7.56 0 0 1 4.492-4.928 3 3 0 0 0 5.443 0 7.56 7.56 0 0 1 4.487 4.927 3 3 0 1 0 1.514-.147zM23.25 34.5a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0M30 27a1.5 1.5 0 1 1 0-3 1.5 1.5 0 0 1 0 3m8.25 9a1.5 1.5 0 1 1 0-3 1.5 1.5 0 0 1 0 3"
      ></path>
    </svg>
  );
};

export const QualificationIcon: React.FC = () => {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="60" height="60" fill="none" viewBox="0 0 60 60">
      <rect width="60" height="60" fill="#FEF7EE" rx="30"></rect>
      <path
        fill="#EC761E"
        d="m41.603 26.338-11.25-6a.75.75 0 0 0-.706 0l-11.25 6a.75.75 0 0 0 0 1.324L21 29.05v4.54c0 .368.135.724.38.998 1.229 1.368 3.98 3.662 8.62 3.662a12.2 12.2 0 0 0 4.5-.821V40.5a.75.75 0 1 0 1.5 0v-3.796a10.8 10.8 0 0 0 2.62-2.116c.245-.274.38-.63.38-.998v-4.54l2.602-1.388a.75.75 0 0 0 0-1.324M30 36.75c-4.057 0-6.442-1.982-7.5-3.16v-3.74l7.148 3.812a.75.75 0 0 0 .704 0L34.5 31.45v4.345c-1.181.55-2.67.955-4.5.955m7.5-3.164c-.45.499-.953.947-1.5 1.336V30.65l1.5-.8zm-1.875-4.435-.02-.013-5.25-2.8a.75.75 0 0 0-.706 1.324L34.031 30 30 32.15 20.344 27 30 21.85 39.656 27z"
      ></path>
    </svg>
  );
};

export const UserRoundIcon: React.FC = () => {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="60" height="60" fill="none" viewBox="0 0 60 60">
      <rect width="60" height="60" fill="#FEF7EE" rx="30"></rect>
      <path
        fill="#EC761E"
        d="M27.024 21.469a.75.75 0 0 1 .538-.91 9.76 9.76 0 0 1 4.875 0 .75.75 0 1 1-.374 1.452 8.25 8.25 0 0 0-4.126 0 .75.75 0 0 1-.913-.542m-5.897 6.843a.753.753 0 0 0 .923-.522 8.24 8.24 0 0 1 2.063-3.57.75.75 0 0 0-1.07-1.05 9.74 9.74 0 0 0-2.437 4.218.75.75 0 0 0 .52.924m10.936 9.677a8.25 8.25 0 0 1-4.126 0 .75.75 0 0 0-.375 1.453 9.76 9.76 0 0 0 4.875 0 .75.75 0 0 0-.374-1.453m5.887-10.197a.75.75 0 1 0 1.446-.401 9.75 9.75 0 0 0-2.438-4.219.75.75 0 1 0-1.07 1.051 8.26 8.26 0 0 1 2.061 3.57zm1.447 4.818a9.7 9.7 0 0 1-2.876 4.638.75.75 0 0 1-1.17-.217 6 6 0 0 0-10.701 0 .75.75 0 0 1-1.17.218 9.73 9.73 0 0 1-2.877-4.64.75.75 0 0 1 1.446-.4 8.2 8.2 0 0 0 1.781 3.27 7.46 7.46 0 0 1 3.385-2.697 4.5 4.5 0 1 1 5.567 0 7.46 7.46 0 0 1 3.384 2.697 8.2 8.2 0 0 0 1.781-3.27.749.749 0 0 1 1.376-.169.75.75 0 0 1 .07.57zM30 32.25a3 3 0 1 0 0-6 3 3 0 0 0 0 6"
      ></path>
    </svg>
  );
};

export const LocationIcon1: React.FC = () => {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="25" height="24" fill="none" viewBox="0 0 25 24">
      <path
        fill="#EC761E"
        d="M12.667 6a3.75 3.75 0 1 0 0 7.5 3.75 3.75 0 0 0 0-7.5m0 6a2.25 2.25 0 1 1 0-4.5 2.25 2.25 0 0 1 0 4.5m0-10.5a8.26 8.26 0 0 0-8.25 8.25c0 2.944 1.36 6.064 3.937 9.023a23.8 23.8 0 0 0 3.886 3.591.75.75 0 0 0 .86 0 23.8 23.8 0 0 0 3.88-3.59c2.573-2.96 3.937-6.08 3.937-9.024a8.26 8.26 0 0 0-8.25-8.25m0 19.313c-1.55-1.22-6.75-5.696-6.75-11.063a6.75 6.75 0 1 1 13.5 0c0 5.365-5.2 9.844-6.75 11.063"
      ></path>
    </svg>
  );
};

export const CurrencyIcon: React.FC = () => {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="25" height="24" fill="none" viewBox="0 0 25 24">
      <path
        fill="#EC761E"
        d="M23.564 5.625a.75.75 0 0 0-.726-.038c-4.025 1.97-6.9 1.047-9.938.074-3.187-1.021-6.492-2.076-11.058.153a.75.75 0 0 0-.425.677v11.244a.75.75 0 0 0 1.079.674c4.024-1.969 6.899-1.046 9.942-.073 1.804.576 3.641 1.164 5.719 1.164 1.602 0 3.35-.349 5.335-1.318a.75.75 0 0 0 .42-.673V6.265a.75.75 0 0 0-.348-.64m-1.147 11.407c-3.807 1.703-6.586.814-9.522-.124-1.803-.577-3.64-1.165-5.718-1.165-1.46.008-2.905.294-4.257.844V6.968c3.807-1.703 6.586-.814 9.522.124 2.935.939 5.956 1.908 9.975.323zM12.667 9a3 3 0 1 0 0 6 3 3 0 0 0 0-6m0 4.5a1.5 1.5 0 1 1 0-3 1.5 1.5 0 0 1 0 3M5.917 9v4.5a.75.75 0 1 1-1.5 0V9a.75.75 0 1 1 1.5 0m13.5 6v-4.5a.75.75 0 1 1 1.5 0V15a.75.75 0 1 1-1.5 0"
      ></path>
    </svg>
  );
};

export const LeftIconTop: React.FC = () => {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="21" height="20" fill="none" viewBox="0 0 21 20">
      <path
        fill="currentColor"
        d="M16.27 5v8.125a.938.938 0 0 1-1.874 0v-5.86l-8.4 8.398a.94.94 0 0 1-1.328-1.328l8.4-8.397h-5.86a.937.937 0 1 1 0-1.875h8.125a.937.937 0 0 1 .938.937"
      ></path>
    </svg>
  );
};

export const BuildingIcon: React.FC = () => {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" fill="none" viewBox="0 0 48 48">
      <path
        fill="currentColor"
        d="M44.95 39H42V18a3 3 0 0 0-3-3h-8.25a.75.75 0 0 0-.75.75V39h-3V6.077a3.08 3.08 0 0 0-1.155-2.438 3 3 0 0 0-3.51-.129l-15 9.99A3 3 0 0 0 6 16.001V39H3.05a1.533 1.533 0 0 0-1.55 1.4A1.5 1.5 0 0 0 3 42h42a1.5 1.5 0 0 0 1.5-1.6 1.534 1.534 0 0 0-1.55-1.4m-30.7-4.5a1.5 1.5 0 0 1-1.6 1.5 1.534 1.534 0 0 1-1.4-1.553v-2.896A1.535 1.535 0 0 1 12.65 30a1.5 1.5 0 0 1 1.6 1.5zm0-10.5a1.5 1.5 0 0 1-1.6 1.5 1.536 1.536 0 0 1-1.4-1.552V21.05a1.535 1.535 0 0 1 1.4-1.551 1.5 1.5 0 0 1 1.6 1.5zm7.5 10.5a1.5 1.5 0 0 1-1.6 1.5 1.534 1.534 0 0 1-1.4-1.549v-2.9a1.536 1.536 0 0 1 1.4-1.55 1.5 1.5 0 0 1 1.6 1.5zm0-10.5a1.5 1.5 0 0 1-1.6 1.5 1.536 1.536 0 0 1-1.4-1.549v-2.9a1.536 1.536 0 0 1 1.4-1.55 1.5 1.5 0 0 1 1.6 1.5z"
      ></path>
    </svg>
  );
};

export const BellIcon: React.FC = () => {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" fill="none" viewBox="0 0 48 48">
      <path
        fill="currentColor"
        d="M31.5 42a1.5 1.5 0 0 1-1.5 1.5H18a1.5 1.5 0 0 1 0-3h12a1.5 1.5 0 0 1 1.5 1.5m11.136-30.69A20.9 20.9 0 0 0 35.3 3.232a1.5 1.5 0 1 0-1.602 2.537 17.65 17.65 0 0 1 6.274 6.921 1.5 1.5 0 0 0 2.663-1.38M6.696 13.5a1.5 1.5 0 0 0 1.33-.81 17.65 17.65 0 0 1 6.275-6.92 1.5 1.5 0 1 0-1.602-2.538 20.9 20.9 0 0 0-7.335 8.078 1.5 1.5 0 0 0 1.332 2.19M39 21a15 15 0 1 0-30 0c0 4.927-.896 9.073-2.59 11.989A3 3 0 0 0 9 37.5h30a3 3 0 0 0 2.586-4.511C39.896 30.07 39 25.926 39 21"
      ></path>
    </svg>
  );
};

export const FollowerLutos: React.FC = () => {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" fill="none" viewBox="0 0 48 48">
      <path
        fill="currentColor"
        d="M46.093 22.806a2.92 2.92 0 0 0-1.785-1.375 13.8 13.8 0 0 0-4.157-.416c.75-3.722.188-6.666-.375-8.411a3.03 3.03 0 0 0-3.525-2.04 16 16 0 0 0-5.353 2.272 17.7 17.7 0 0 0-5.083-6.234 3.01 3.01 0 0 0-3.611 0 17.7 17.7 0 0 0-5.093 6.234 16 16 0 0 0-5.353-2.272 3.026 3.026 0 0 0-3.523 2.04c-.563 1.745-1.125 4.687-.386 8.411a13.8 13.8 0 0 0-4.157.416 2.9 2.9 0 0 0-1.785 1.375 3 3 0 0 0-.3 2.298c.635 2.36 2.587 6.842 8.5 10.375C16.017 39.01 21.211 39 24.008 39c2.798 0 8 0 13.875-3.521 5.912-3.533 7.864-8.016 8.5-10.375a3 3 0 0 0-.291-2.298m-34.45 10.098C6.65 29.92 5.03 26.26 4.5 24.32c1.38-.34 2.811-.415 4.219-.22a24 24 0 0 0 1.715 3.64 26.7 26.7 0 0 0 6.375 7.417 21.6 21.6 0 0 1-5.165-2.252M24 35.7c-1.75-1.301-6-5.293-6-13.356C18 14.381 22.196 10.357 24 9c1.804 1.361 6 5.385 6 13.348 0 8.059-4.25 12.05-6 13.352m19.5-11.378c-.52 1.92-2.138 5.59-7.142 8.582a21.6 21.6 0 0 1-5.166 2.25 26.7 26.7 0 0 0 6.375-7.417 24 24 0 0 0 1.716-3.64 11.2 11.2 0 0 1 4.217.224z"
      ></path>
    </svg>
  );
};

export const HeadsetIcon: React.FC = () => {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" fill="none" viewBox="0 0 48 48">
      <path
        fill="currentColor"
        d="M43.5 24v15a7.5 7.5 0 0 1-7.5 7.5H25.5a1.5 1.5 0 1 1 0-3H36a4.5 4.5 0 0 0 4.5-4.5H36a4.5 4.5 0 0 1-4.5-4.5V27a4.5 4.5 0 0 1 4.5-4.5h4.434a16.5 16.5 0 0 0-28.059-10.211A16.37 16.37 0 0 0 7.568 22.5H12a4.5 4.5 0 0 1 4.5 4.5v7.5A4.5 4.5 0 0 1 12 39H9a4.5 4.5 0 0 1-4.5-4.5V24a19.52 19.52 0 0 1 33.354-13.751A19.4 19.4 0 0 1 43.5 24"
      ></path>
    </svg>
  );
};

export const YesJobIcon: React.FC = () => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="140"
      height="226"
      fill="none"
      viewBox="0 0 140 226"
    >
      <g fill="#fff" opacity="0.1">
        <path d="M162.157 20.669C145.093 3.605 121.473-7 95.443-7h-1.06C42.323-7 0 35.226 0 87.383c0 26.03 10.605 49.649 27.669 66.713 6.17 6.17 13.208 11.569 20.92 15.811l4.435-24.969.578-3.471c-16.389-12.34-26.994-32.007-26.994-54.084 0-18.703 7.617-35.671 19.86-47.915a68.5 68.5 0 0 1 23.234-15.232l13.208 22.27-9.544 54.084 39.045-23.04-5.495-31.044 13.207-22.27c25.259 9.833 43.094 34.417 43.094 63.147 0 22.077-10.508 41.647-26.801 53.988l5.013 28.44c28.826-16.1 48.397-46.951 48.397-82.428 0-26.127-10.605-49.65-27.669-66.714"></path>
        <path d="m117.714 107.243-50.517 28.633 3.278-18.511 44.251-26.704zM132.27 188.996 94.864 225.92l-37.02-36.731 6.362-36.346 55.916-32.2z"></path>
      </g>
    </svg>
  );
};

export const EmailIcon: React.FC = () => {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="48" height="49" fill="none" viewBox="0 0 48 49">
      <path
        fill="currentColor"
        d="m42.833 17.251-18-12a1.5 1.5 0 0 0-1.665 0l-18 12A1.5 1.5 0 0 0 4.5 18.5V38a3 3 0 0 0 3 3h33a3 3 0 0 0 3-3V18.5a1.5 1.5 0 0 0-.667-1.249M18.135 29 7.5 36.5V21.412zm3.07 1.5h5.59L37.415 38H10.586zm8.66-1.5L40.5 21.412V36.5z"
      ></path>
    </svg>
  );
};

export const MapTrifold: React.FC = () => {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="49" height="49" fill="none" viewBox="0 0 49 49">
      <path
        fill="currentColor"
        d="M43.59 9.817a1.5 1.5 0 0 0-1.287-.272L30.841 12.41 19.338 6.658a1.5 1.5 0 0 0-1.035-.113l-12 3A1.5 1.5 0 0 0 5.167 11v27a1.5 1.5 0 0 0 1.864 1.455l11.462-2.865 11.503 5.752a1.53 1.53 0 0 0 1.035.113l12-3A1.5 1.5 0 0 0 44.167 38V11a1.5 1.5 0 0 0-.578-1.183M18.667 33.5q-.185 0-.364.045L8.167 36.078V12.172L18.493 9.59l.174.086zm22.5 3.328L30.841 39.41l-.174-.086V15.5q.184 0 .364-.043l10.136-2.535z"
      ></path>
    </svg>
  );
};

export const UserIcon: React.FC = () => {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="25" height="25" fill="none" viewBox="0 0 25 25">
      <path
        fill="currentColor"
        d="M22.15 21.125a.75.75 0 0 1-.65.375h-18a.75.75 0 0 1-.649-1.125c1.428-2.468 3.629-4.238 6.196-5.078a6.75 6.75 0 1 1 6.906 0c2.568.84 4.768 2.61 6.196 5.078a.75.75 0 0 1 0 .75"
      ></path>
    </svg>
  );
};

export const BriefcaseIcon: React.FC = () => {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="25" fill="none" viewBox="0 0 24 25">
      <path
        fill="currentColor"
        d="M14.25 11a.75.75 0 0 1-.75.75h-3a.75.75 0 1 1 0-1.5h3a.75.75 0 0 1 .75.75m7.5-3.75v12a1.5 1.5 0 0 1-1.5 1.5H3.75a1.5 1.5 0 0 1-1.5-1.5v-12a1.5 1.5 0 0 1 1.5-1.5H7.5V5a2.25 2.25 0 0 1 2.25-2.25h4.5A2.25 2.25 0 0 1 16.5 5v.75h3.75a1.5 1.5 0 0 1 1.5 1.5M9 5.75h6V5a.75.75 0 0 0-.75-.75h-4.5A.75.75 0 0 0 9 5zm11.25 5.4v-3.9H3.75v3.9a17.25 17.25 0 0 0 8.25 2.1 17.25 17.25 0 0 0 8.25-2.1"
      ></path>
    </svg>
  );
};

export const MortarboardIcon: React.FC = () => {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="54" height="52" fill="none" viewBox="0 0 54 52">
      <g clipPath="url(#clip0_346_1092)">
        <path
          fill="url(#paint0_linear_346_1092)"
          d="M7.442 13.773v19.96c0 1.759.99 3.362 2.54 4.082 10.968 5.093 23.042 5.093 34.01 0 1.55-.72 2.54-2.323 2.54-4.081v-19.96z"
        ></path>
        <path
          fill="url(#paint1_linear_346_1092)"
          d="M7.442 31.719v2.015c0 1.758.99 3.362 2.54 4.081 10.968 5.094 23.042 5.094 34.01 0 1.55-.72 2.54-2.323 2.54-4.08v-2.016z"
        ></path>
        <path
          fill="url(#paint2_linear_346_1092)"
          d="M13.377 39.216c10.029 3.638 20.767 3.172 30.614-1.401 1.55-.72 2.54-2.323 2.54-4.081v-19.96H13.377z"
        ></path>
        <path
          fill="url(#paint3_linear_346_1092)"
          d="M7.442 13.773v7.622l19.56 20.24c5.756-.003 11.51-1.276 16.99-3.82 1.55-.72 2.54-2.323 2.54-4.081v-19.96z"
        ></path>
        <path
          fill="url(#paint4_linear_346_1092)"
          d="m23.603 5.382-21.6 9.97c-1.48.683-1.48 2.857 0 3.54l21.6 9.97a8.05 8.05 0 0 0 6.768 0l21.6-9.97c1.48-.683 1.48-2.857 0-3.54l-21.6-9.97a8.05 8.05 0 0 0-6.768 0"
        ></path>
        <path
          fill="url(#paint5_linear_346_1092)"
          d="M35.075 26.69 30.37 28.86a8.06 8.06 0 0 1-6.77 0l-21.598-9.97c-1.48-.683-1.48-2.857 0-3.54l4.247-1.96s28.722 13.253 28.825 13.298"
        ></path>
        <path
          fill="url(#paint6_linear_346_1092)"
          d="m18.898 26.69 4.704 2.171a8.06 8.06 0 0 0 6.77 0l21.598-9.97c1.48-.683 1.48-2.857 0-3.54l-4.247-1.96S19 26.644 18.898 26.689"
        ></path>
        <path
          fill="url(#paint7_linear_346_1092)"
          d="m28.155 16.621-15.763 7.067 11.21 5.175a8.05 8.05 0 0 0 6.769 0l6.649-3.07z"
        ></path>
        <path
          fill="url(#paint8_linear_346_1092)"
          d="M12.614 37.383c-.706 0-1.279-.592-1.279-1.323V25.64c0-1.644.937-3.138 2.387-3.808l12.744-5.88c.645-.299 1.4.001 1.689.669.287.667-.002 1.45-.647 1.748L14.765 24.25c-.53.244-.872.79-.872 1.39v10.42c0 .73-.572 1.323-1.279 1.323"
        ></path>
        <path
          fill="url(#paint9_linear_346_1092)"
          d="M13.107 49.364H12.01c-.827 0-1.497-.694-1.497-1.55v-5.58h4.093v5.58c0 .856-.67 1.55-1.498 1.55"
        ></path>
        <path
          fill="url(#paint10_linear_346_1092)"
          d="M14.605 47.814v-5.58h-4.093v1.924l4.013 4.152q.078-.234.08-.496"
        ></path>
        <path
          fill="url(#paint11_linear_346_1092)"
          d="M12.558 44.706c2.505 0 4.535-2.101 4.535-4.693s-2.03-4.693-4.535-4.693-4.535 2.101-4.535 4.693 2.03 4.693 4.535 4.693"
        ></path>
        <path
          fill="url(#paint12_linear_346_1092)"
          d="M15.065 37.508c0 .682-.47 1.268-1.12 1.393l-.829.158a.16.16 0 0 0-.127.158v1.92c.016.5-.253 1.04-.745 1.39-.706.503-1.602.423-2.002-.178-.4-.6-.151-1.495.554-1.998.41-.292.884-.387 1.288-.299v-2.148c0-.136.093-.252.222-.277l2.372-.453a.332.332 0 0 1 .387.334"
        ></path>
        <path
          fill="url(#paint13_linear_346_1092)"
          d="M8.088 40.797c.363 2.214 2.229 3.906 4.47 3.906 2.242 0 4.108-1.692 4.47-3.906z"
        ></path>
      </g>
      <defs>
        <linearGradient
          id="paint0_linear_346_1092"
          x1="24.128"
          x2="37.944"
          y1="19.248"
          y2="42.313"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#5A5A5A"></stop>
          <stop offset="1" stopColor="#464646"></stop>
        </linearGradient>
        <linearGradient
          id="paint1_linear_346_1092"
          x1="26.986"
          x2="26.986"
          y1="33.272"
          y2="42.411"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#464646" stopOpacity="0"></stop>
          <stop offset="1" stopColor="#202020"></stop>
        </linearGradient>
        <linearGradient
          id="paint2_linear_346_1092"
          x1="23.186"
          x2="14.79"
          y1="26.433"
          y2="25.641"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#464646" stopOpacity="0"></stop>
          <stop offset="1" stopColor="#202020"></stop>
        </linearGradient>
        <linearGradient
          id="paint3_linear_346_1092"
          x1="26.986"
          x2="26.986"
          y1="42.677"
          y2="20.358"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#464646" stopOpacity="0"></stop>
          <stop offset="0.027" stopColor="#454545" stopOpacity="0.027"></stop>
          <stop offset="0.639" stopColor="#2A2A2A" stopOpacity="0.639"></stop>
          <stop offset="1" stopColor="#202020"></stop>
        </linearGradient>
        <linearGradient
          id="paint4_linear_346_1092"
          x1="24.692"
          x2="34.763"
          y1="13.021"
          y2="29.832"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#5A5A5A"></stop>
          <stop offset="1" stopColor="#464646"></stop>
        </linearGradient>
        <linearGradient
          id="paint5_linear_346_1092"
          x1="17.897"
          x2="15.108"
          y1="21.916"
          y2="28.302"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#464646" stopOpacity="0"></stop>
          <stop offset="1" stopColor="#202020"></stop>
        </linearGradient>
        <linearGradient
          id="paint6_linear_346_1092"
          x1="36.076"
          x2="38.865"
          y1="21.916"
          y2="28.302"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#464646" stopOpacity="0"></stop>
          <stop offset="1" stopColor="#202020"></stop>
        </linearGradient>
        <linearGradient
          id="paint7_linear_346_1092"
          x1="27.48"
          x2="21.751"
          y1="28.873"
          y2="20.903"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#464646" stopOpacity="0"></stop>
          <stop offset="0.027" stopColor="#454545" stopOpacity="0.027"></stop>
          <stop offset="0.639" stopColor="#2A2A2A" stopOpacity="0.639"></stop>
          <stop offset="1" stopColor="#202020"></stop>
        </linearGradient>
        <linearGradient
          id="paint8_linear_346_1092"
          x1="9.181"
          x2="26.93"
          y1="26.61"
          y2="26.61"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#F7E07D"></stop>
          <stop offset="1" stopColor="#E69642"></stop>
        </linearGradient>
        <linearGradient
          id="paint9_linear_346_1092"
          x1="10.239"
          x2="15.14"
          y1="45.799"
          y2="45.799"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#F7E07D"></stop>
          <stop offset="1" stopColor="#E69642"></stop>
        </linearGradient>
        <linearGradient
          id="paint10_linear_346_1092"
          x1="12.558"
          x2="12.558"
          y1="48.155"
          y2="42.614"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#D52C1C" stopOpacity="0"></stop>
          <stop offset="0.28" stopColor="#D12C1E" stopOpacity="0.28"></stop>
          <stop offset="0.574" stopColor="#C42C23" stopOpacity="0.574"></stop>
          <stop offset="0.872" stopColor="#AF2B2C" stopOpacity="0.872"></stop>
          <stop offset="1" stopColor="#A42B31"></stop>
        </linearGradient>
        <linearGradient
          id="paint11_linear_346_1092"
          x1="10.393"
          x2="14.743"
          y1="37.773"
          y2="41.977"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#F7E07D"></stop>
          <stop offset="1" stopColor="#E69642"></stop>
        </linearGradient>
        <linearGradient
          id="paint12_linear_346_1092"
          x1="14.628"
          x2="10.293"
          y1="41.914"
          y2="37.724"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#F7E07D"></stop>
          <stop offset="1" stopColor="#E69642"></stop>
        </linearGradient>
        <linearGradient
          id="paint13_linear_346_1092"
          x1="12.558"
          x2="12.558"
          y1="41.088"
          y2="45.181"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#D52C1C" stopOpacity="0"></stop>
          <stop offset="0.28" stopColor="#D12C1E" stopOpacity="0.28"></stop>
          <stop offset="0.574" stopColor="#C42C23" stopOpacity="0.574"></stop>
          <stop offset="0.872" stopColor="#AF2B2C" stopOpacity="0.872"></stop>
          <stop offset="1" stopColor="#A42B31"></stop>
        </linearGradient>
        <clipPath id="clip0_346_1092">
          <path fill="#fff" d="M.893 0H53.08v52H.893z"></path>
        </clipPath>
      </defs>
    </svg>
  );
};

export const EditIcon: React.FC = () => {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 40 40" fill="none">
      <path
        d="M16.5638 13.75H12.9397C12.2989 13.75 11.6843 14.0134 11.2312 14.4822C10.7781 14.9511 10.5236 15.587 10.5236 16.25V27.5C10.5236 28.163 10.7781 28.7989 11.2312 29.2678C11.6843 29.7366 12.2989 30 12.9397 30H23.8122C24.4529 30 25.0675 29.7366 25.5206 29.2678C25.9737 28.7989 26.2283 28.163 26.2283 27.5V23.75"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M16.5637 23.7493H20.1879L30.4563 13.1243C30.9369 12.627 31.2069 11.9526 31.2069 11.2493C31.2069 10.546 30.9369 9.87159 30.4563 9.37431C29.9757 8.87703 29.3239 8.59766 28.6443 8.59766C27.9646 8.59766 27.3128 8.87703 26.8322 9.37431L16.5637 19.9993V23.7493Z"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M25.0201 11.25L28.6443 15"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export const DeleteIcon: React.FC = () => {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="28" height="30" viewBox="0 0 28 30" fill="none">
      <path
        d="M14.0421 3.24219C16.0053 3.24219 17.6088 4.8358 17.7101 6.84101L17.7152 7.04286H23.1025C23.5082 7.04286 23.8371 7.38319 23.8371 7.803C23.8371 8.18783 23.5607 8.50586 23.2022 8.55619L23.1025 8.56313H22.0652L20.8703 23.8197C20.7157 25.7931 19.1228 27.3131 17.2092 27.3131H10.875C8.96147 27.3131 7.3685 25.7931 7.21386 23.8197L6.01801 8.56313H4.9817C4.60978 8.56313 4.30242 8.27717 4.25378 7.90614L4.24707 7.803C4.24707 7.41817 4.52344 7.10014 4.88201 7.04981L4.9817 7.04286H10.369C10.369 4.94381 12.0135 3.24219 14.0421 3.24219ZM20.5917 8.56313H7.49243L8.6783 23.6967C8.77109 24.8808 9.72687 25.7929 10.875 25.7929H17.2092C18.3573 25.7929 19.3131 24.8808 19.4059 23.6967L20.5917 8.56313ZM11.8382 12.3638C12.2101 12.3638 12.5175 12.6498 12.5662 13.0208L12.5728 13.1239V21.2321C12.5728 21.6518 12.2439 21.9922 11.8382 21.9922C11.4663 21.9922 11.1589 21.7062 11.1103 21.3352L11.1036 21.2321V13.1239C11.1036 12.7041 11.4325 12.3638 11.8382 12.3638ZM16.246 12.3638C16.6179 12.3638 16.9253 12.6498 16.9739 13.0208L16.9806 13.1239V21.2321C16.9806 21.6518 16.6517 21.9922 16.246 21.9922C15.8741 21.9922 15.5667 21.7062 15.518 21.3352L15.5114 21.2321V13.1239C15.5114 12.7041 15.8403 12.3638 16.246 12.3638ZM14.0421 4.76246C12.8756 4.76246 11.9209 5.70012 11.8433 6.88673L11.8382 7.04286H16.246L16.2409 6.88673C16.1633 5.70012 15.2086 4.76246 14.0421 4.76246Z"
        fill="currentColor"
        stroke="currentColor"
        strokeWidth="0.25"
      />
    </svg>
  );
};

export const BriefcaseIcon2: React.FC = () => {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="50" height="50" fill="none" viewBox="0 0 50 50">
      <g clipPath="url(#clip0_352_2723)">
        <path
          fill="url(#paint0_linear_352_2723)"
          d="M35.57 6.52c-.218-1-.896-1.754-1.736-1.92a52.4 52.4 0 0 0-20.36 0c-.84.166-1.517.92-1.736 1.92a42 42 0 0 0-.981 8.989h4.323q0-3.239.651-6.47c.146-.72.597-1.263 1.156-1.38a32.2 32.2 0 0 1 13.535 0c.558.117 1.01.66 1.155 1.38q.652 3.231.652 6.47h4.323q0-4.501-.982-8.99"
        ></path>
        <path
          fill="url(#paint1_linear_352_2723)"
          d="M33.834 4.6a53 53 0 0 0-9.123-.987v3.344q2.86.095 5.71.701c.56.118 1.01.661 1.156 1.381q.652 3.232.652 6.47h4.323q0-4.501-.982-8.99c-.218-.998-.896-1.753-1.736-1.918"
        ></path>
        <path
          fill="url(#paint2_linear_352_2723)"
          d="M33.834 4.6a53 53 0 0 0-9.123-.987v3.344q2.86.095 5.71.701c.56.118 1.01.661 1.156 1.381q.652 3.232.652 6.47h4.323q0-4.501-.982-8.99c-.218-.998-.896-1.753-1.736-1.918"
        ></path>
        <path
          fill="url(#paint3_linear_352_2723)"
          d="M31.577 9.04a32.6 32.6 0 0 1 .653 6.47h4.322q0-3.692-.661-7.377h-4.73c.198.24.344.551.416.907"
        ></path>
        <path
          fill="url(#paint4_linear_352_2723)"
          d="M15.731 9.04c.072-.356.219-.668.416-.907h-4.73a41.7 41.7 0 0 0-.66 7.376h4.323q0-3.238.651-6.47"
        ></path>
        <path
          fill="url(#paint5_linear_352_2723)"
          d="M15.896 15.903a22.5 22.5 0 0 1-6.507 0 .67.67 0 0 1-.556-.454 7.4 7.4 0 0 1 0-4.26.67.67 0 0 1 .556-.454 22.5 22.5 0 0 1 6.507 0c.268.039.485.218.555.454a7.4 7.4 0 0 1 0 4.26.67.67 0 0 1-.555.454"
        ></path>
        <path
          fill="url(#paint6_linear_352_2723)"
          d="M15.896 15.903a22.5 22.5 0 0 1-6.507 0 .67.67 0 0 1-.556-.454 7.4 7.4 0 0 1 0-4.26.67.67 0 0 1 .556-.454 22.5 22.5 0 0 1 6.507 0c.268.039.485.218.555.454a7.4 7.4 0 0 1 0 4.26.67.67 0 0 1-.555.454"
        ></path>
        <path
          fill="url(#paint7_linear_352_2723)"
          d="M16.45 11.19a.67.67 0 0 0-.554-.455 22.5 22.5 0 0 0-3.868-.228q1.32.036 2.64.228c.268.039.485.218.555.454a7.4 7.4 0 0 1 0 4.26.67.67 0 0 1-.555.454q-1.318.192-2.64.228 1.936.054 3.868-.228a.67.67 0 0 0 .555-.455 7.4 7.4 0 0 0 0-4.259"
        ></path>
        <path
          fill="url(#paint8_linear_352_2723)"
          d="M15.896 10.735a23 23 0 0 0-1.368-.157v.137l.14.02c.268.04.485.218.555.455a7.4 7.4 0 0 1 0 4.259.67.67 0 0 1-.555.454l-.14.02v.138q.684-.058 1.368-.157a.67.67 0 0 0 .555-.455 7.4 7.4 0 0 0 0-4.26.67.67 0 0 0-.555-.454"
        ></path>
        <path
          fill="url(#paint9_linear_352_2723)"
          d="M8.833 15.448c.07.237.287.415.556.454a22.5 22.5 0 0 0 6.507 0 .67.67 0 0 0 .555-.454q.314-1.064.314-2.13H8.52q0 1.066.313 2.13"
        ></path>
        <path
          fill="url(#paint10_linear_352_2723)"
          d="M37.92 15.903a22.5 22.5 0 0 1-6.507 0 .67.67 0 0 1-.555-.454 7.4 7.4 0 0 1 0-4.26.67.67 0 0 1 .555-.454 22.5 22.5 0 0 1 6.507 0c.269.039.486.218.555.454a7.4 7.4 0 0 1 0 4.26.67.67 0 0 1-.555.454"
        ></path>
        <path
          fill="url(#paint11_linear_352_2723)"
          d="M37.92 15.903a22.5 22.5 0 0 1-6.507 0 .67.67 0 0 1-.555-.454 7.4 7.4 0 0 1 0-4.26.67.67 0 0 1 .555-.454 22.5 22.5 0 0 1 6.507 0c.269.039.486.218.555.454a7.4 7.4 0 0 1 0 4.26.67.67 0 0 1-.555.454"
        ></path>
        <path
          fill="url(#paint12_linear_352_2723)"
          d="M38.475 11.19a.67.67 0 0 0-.555-.455 22.5 22.5 0 0 0-3.867-.228q1.32.036 2.64.228c.268.039.485.218.554.454a7.4 7.4 0 0 1 0 4.26.67.67 0 0 1-.555.454q-1.318.192-2.64.228c1.29.036 2.582-.04 3.868-.228a.67.67 0 0 0 .555-.455 7.4 7.4 0 0 0 0-4.259"
        ></path>
        <path
          fill="url(#paint13_linear_352_2723)"
          d="M37.92 10.735a23 23 0 0 0-1.368-.157v.137l.14.02c.269.04.486.218.555.455a7.4 7.4 0 0 1 0 4.259.67.67 0 0 1-.555.454l-.14.02v.138q.685-.058 1.368-.157a.67.67 0 0 0 .555-.455 7.4 7.4 0 0 0 0-4.26.67.67 0 0 0-.555-.454"
        ></path>
        <path
          fill="url(#paint14_linear_352_2723)"
          d="M30.858 15.448c.07.237.287.415.555.454a22.5 22.5 0 0 0 6.507 0 .67.67 0 0 0 .555-.454q.315-1.064.314-2.13h-8.245q0 1.066.314 2.13"
        ></path>
        <path
          fill="url(#paint15_linear_352_2723)"
          d="M45.866 47.277h-5.239V13.02h5.239A4.134 4.134 0 0 1 50 17.154v25.989a4.134 4.134 0 0 1-4.134 4.134"
        ></path>
        <path
          fill="url(#paint16_linear_352_2723)"
          d="M40.627 26.94v20.336h5.239A4.134 4.134 0 0 0 50 43.142V26.939z"
        ></path>
        <path
          fill="url(#paint17_linear_352_2723)"
          d="M45.866 13.02h-5.239v34.257h5.239A4.134 4.134 0 0 0 50 43.142V17.155a4.134 4.134 0 0 0-4.134-4.134"
        ></path>
        <path
          fill="url(#paint18_linear_352_2723)"
          d="M45.866 13.02h-5.239v34.257h5.239A4.134 4.134 0 0 0 50 43.142V17.155a4.134 4.134 0 0 0-4.134-4.134"
        ></path>
        <path
          fill="url(#paint19_linear_352_2723)"
          d="M42.295 47.277H5.014A4.134 4.134 0 0 1 .88 43.142V17.153a4.134 4.134 0 0 1 4.134-4.133h37.281a4.134 4.134 0 0 1 4.134 4.134v25.989a4.134 4.134 0 0 1-4.134 4.133"
        ></path>
        <path
          fill="url(#paint20_linear_352_2723)"
          d="M.88 26.94v16.202a4.134 4.134 0 0 0 4.133 4.134h37.282a4.134 4.134 0 0 0 4.134-4.134V26.939z"
        ></path>
        <path
          fill="url(#paint21_linear_352_2723)"
          d="M.88 26.94v16.202a4.134 4.134 0 0 0 4.133 4.134h37.282a4.134 4.134 0 0 0 4.134-4.134V26.939z"
        ></path>
        <path
          fill="url(#paint22_linear_352_2723)"
          d="M44.025 33.802H3.18A3.18 3.18 0 0 1 0 30.624V16.156a4.275 4.275 0 0 1 4.275-4.275h41.45A4.275 4.275 0 0 1 50 16.156v2.102c0-.33-.117-.65-.33-.902-.841-.995-2.466-.4-2.466.902v12.366a3.18 3.18 0 0 1-3.179 3.178"
        ></path>
        <path
          fill="url(#paint23_linear_352_2723)"
          d="M45.725 11.88h-8.054v21.922h6.354a3.18 3.18 0 0 0 3.179-3.178V18.258c0-1.303 1.625-1.897 2.466-.902.213.252.33.572.33.902v-2.102a4.275 4.275 0 0 0-4.275-4.275"
        ></path>
        <path
          fill="url(#paint24_linear_352_2723)"
          d="M14.08 31.322 6.1 36.075 17.305 47.28h12.731z"
        ></path>
        <path
          fill="url(#paint25_linear_352_2723)"
          d="M46.43 43.145v-3.468l-8.355-8.355-7.969 4.755 11.202 11.202h.987a4.134 4.134 0 0 0 4.134-4.135"
        ></path>
        <path
          fill="url(#paint26_linear_352_2723)"
          d="M13.6 36.249a26.7 26.7 0 0 1-7.089 0c-.292-.04-.529-.218-.605-.455a6.8 6.8 0 0 1 0-4.26c.076-.236.313-.415.605-.454a26.7 26.7 0 0 1 7.09 0c.292.04.528.218.604.455a6.8 6.8 0 0 1 0 4.26c-.076.236-.312.415-.605.454"
        ></path>
        <path
          fill="url(#paint27_linear_352_2723)"
          d="m10.722 32.304-.184.038a7.1 7.1 0 0 1-2.85 0l-.183-.038a.425.425 0 0 0-.404.698l1.694 1.918c.169.191.468.191.637 0l1.694-1.918a.425.425 0 0 0-.404-.698"
        ></path>
        <path
          fill="url(#paint28_linear_352_2723)"
          d="M14.205 31.535c-.076-.237-.312-.416-.605-.455a27 27 0 0 0-4.213-.228q1.44.036 2.875.228c.293.04.53.218.605.455a6.8 6.8 0 0 1 0 4.26c-.076.236-.312.415-.605.454q-1.434.192-2.875.228c1.405.035 2.812-.04 4.213-.228.293-.04.53-.218.605-.455a6.8 6.8 0 0 0 0-4.26"
        ></path>
        <path
          fill="url(#paint29_linear_352_2723)"
          d="M13.6 31.08a27 27 0 0 0-1.49-.156v.137l.152.02c.293.039.53.218.605.454a6.8 6.8 0 0 1 0 4.26c-.076.236-.312.415-.605.454l-.152.02v.137a27 27 0 0 0 1.49-.157c.293-.039.53-.218.605-.454a6.8 6.8 0 0 0 0-4.26c-.076-.236-.312-.415-.605-.454"
        ></path>
        <path
          fill="url(#paint30_linear_352_2723)"
          d="M5.906 35.794c.076.236.313.415.605.454a26.7 26.7 0 0 0 7.09 0c.292-.039.528-.218.604-.454q.342-1.064.342-2.13H5.564q0 1.066.342 2.13"
        ></path>
        <path
          fill="url(#paint31_linear_352_2723)"
          d="M37.595 36.249a26.7 26.7 0 0 1-7.09 0c-.292-.04-.528-.218-.604-.455a6.8 6.8 0 0 1 0-4.26c.076-.236.312-.415.604-.454a26.7 26.7 0 0 1 7.09 0c.292.04.529.218.605.455a6.8 6.8 0 0 1 0 4.26c-.077.236-.313.415-.605.454"
        ></path>
        <path
          fill="url(#paint32_linear_352_2723)"
          d="m34.716 32.304-.183.038a7.1 7.1 0 0 1-2.85 0l-.183-.038a.425.425 0 0 0-.405.698l1.694 1.918c.17.191.468.191.637 0l1.694-1.918a.425.425 0 0 0-.404-.698"
        ></path>
        <path
          fill="url(#paint33_linear_352_2723)"
          d="M38.2 31.535c-.076-.237-.313-.416-.605-.455a27 27 0 0 0-4.214-.228q1.44.036 2.876.228c.292.04.529.218.605.455a6.8 6.8 0 0 1 0 4.26c-.076.236-.313.415-.605.454q-1.436.192-2.876.228 2.11.053 4.214-.228c.292-.04.528-.218.605-.455a6.8 6.8 0 0 0 0-4.26"
        ></path>
        <path
          fill="url(#paint34_linear_352_2723)"
          d="M37.595 31.08a27 27 0 0 0-1.49-.156v.137l.152.02c.292.039.529.218.605.454a6.8 6.8 0 0 1 0 4.26c-.076.236-.313.415-.605.454l-.153.02v.137q.747-.057 1.49-.157c.293-.039.53-.218.606-.454a6.8 6.8 0 0 0 0-4.26c-.077-.236-.313-.415-.605-.454"
        ></path>
        <path
          fill="url(#paint35_linear_352_2723)"
          d="M29.9 35.794c.077.236.313.415.605.454a26.7 26.7 0 0 0 7.09 0c.292-.039.528-.218.605-.454q.34-1.064.341-2.13H29.56q0 1.066.341 2.13"
        ></path>
        <path
          fill="url(#paint36_linear_352_2723)"
          d="M16.94 8.192a52.1 52.1 0 0 1 13.429 0 .8.8 0 0 0 .902-.793V3.978c0-.403-.3-.741-.698-.794a52.2 52.2 0 0 0-13.837 0 .8.8 0 0 0-.698.794v3.421a.8.8 0 0 0 .902.793"
        ></path>
        <path
          fill="url(#paint37_linear_352_2723)"
          d="M16.94 8.192a52.1 52.1 0 0 1 13.429 0 .8.8 0 0 0 .902-.793V3.978c0-.403-.3-.741-.698-.794a52.2 52.2 0 0 0-13.837 0 .8.8 0 0 0-.698.794v3.421a.8.8 0 0 0 .902.793"
        ></path>
        <path
          fill="url(#paint38_linear_352_2723)"
          d="M30.573 3.183a52 52 0 0 0-5.363-.437v5.035q2.58.077 5.159.41a.8.8 0 0 0 .902-.793V3.977c0-.403-.3-.741-.698-.794"
        ></path>
      </g>
      <defs>
        <linearGradient
          id="paint0_linear_352_2723"
          x1="22.28"
          x2="23.881"
          y1="4.162"
          y2="15.718"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#EAF9FA"></stop>
          <stop offset="1" stopColor="#B3DAFE"></stop>
        </linearGradient>
        <linearGradient
          id="paint1_linear_352_2723"
          x1="32.983"
          x2="35.698"
          y1="9.26"
          y2="8.912"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#7BACDF" stopOpacity="0"></stop>
          <stop offset="1" stopColor="#7BACDF"></stop>
        </linearGradient>
        <linearGradient
          id="paint2_linear_352_2723"
          x1="33.213"
          x2="29.245"
          y1="10.107"
          y2="8.785"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#7BACDF" stopOpacity="0"></stop>
          <stop offset="1" stopColor="#7BACDF"></stop>
        </linearGradient>
        <linearGradient
          id="paint3_linear_352_2723"
          x1="33.857"
          x2="33.857"
          y1="8.926"
          y2="10.841"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#7BACDF" stopOpacity="0"></stop>
          <stop offset="1" stopColor="#7BACDF"></stop>
        </linearGradient>
        <linearGradient
          id="paint4_linear_352_2723"
          x1="13.452"
          x2="13.452"
          y1="8.926"
          y2="10.841"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#7BACDF" stopOpacity="0"></stop>
          <stop offset="1" stopColor="#7BACDF"></stop>
        </linearGradient>
        <linearGradient
          id="paint5_linear_352_2723"
          x1="11.209"
          x2="14.069"
          y1="10.933"
          y2="15.694"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#FEF0AE"></stop>
          <stop offset="1" stopColor="#FBC56D"></stop>
        </linearGradient>
        <linearGradient
          id="paint6_linear_352_2723"
          x1="12.642"
          x2="12.642"
          y1="10.805"
          y2="12.06"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#DC8758" stopOpacity="0"></stop>
          <stop offset="0.215" stopColor="#DD8654" stopOpacity="0.215"></stop>
          <stop offset="0.429" stopColor="#E28448" stopOpacity="0.429"></stop>
          <stop offset="0.642" stopColor="#EA8034" stopOpacity="0.642"></stop>
          <stop offset="0.854" stopColor="#F47B18" stopOpacity="0.854"></stop>
          <stop offset="1" stopColor="#FE7701"></stop>
        </linearGradient>
        <linearGradient
          id="paint7_linear_352_2723"
          x1="9.359"
          x2="18.174"
          y1="8.978"
          y2="17.334"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#DC8758" stopOpacity="0"></stop>
          <stop offset="0.215" stopColor="#DD8654" stopOpacity="0.215"></stop>
          <stop offset="0.429" stopColor="#E28448" stopOpacity="0.429"></stop>
          <stop offset="0.642" stopColor="#EA8034" stopOpacity="0.642"></stop>
          <stop offset="0.854" stopColor="#F47B18" stopOpacity="0.854"></stop>
          <stop offset="1" stopColor="#FE7701"></stop>
        </linearGradient>
        <linearGradient
          id="paint8_linear_352_2723"
          x1="15.666"
          x2="16.613"
          y1="13.319"
          y2="13.319"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#DC8758" stopOpacity="0"></stop>
          <stop offset="0.215" stopColor="#DD8654" stopOpacity="0.215"></stop>
          <stop offset="0.429" stopColor="#E28448" stopOpacity="0.429"></stop>
          <stop offset="0.642" stopColor="#EA8034" stopOpacity="0.642"></stop>
          <stop offset="0.854" stopColor="#F47B18" stopOpacity="0.854"></stop>
          <stop offset="1" stopColor="#FE7701"></stop>
        </linearGradient>
        <linearGradient
          id="paint9_linear_352_2723"
          x1="12.642"
          x2="12.642"
          y1="14.529"
          y2="16.176"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#DC8758" stopOpacity="0"></stop>
          <stop offset="0.215" stopColor="#DD8654" stopOpacity="0.215"></stop>
          <stop offset="0.429" stopColor="#E28448" stopOpacity="0.429"></stop>
          <stop offset="0.642" stopColor="#EA8034" stopOpacity="0.642"></stop>
          <stop offset="0.854" stopColor="#F47B18" stopOpacity="0.854"></stop>
          <stop offset="1" stopColor="#FE7701"></stop>
        </linearGradient>
        <linearGradient
          id="paint10_linear_352_2723"
          x1="33.233"
          x2="36.093"
          y1="10.933"
          y2="15.694"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#FEF0AE"></stop>
          <stop offset="1" stopColor="#FBC56D"></stop>
        </linearGradient>
        <linearGradient
          id="paint11_linear_352_2723"
          x1="34.667"
          x2="34.667"
          y1="10.805"
          y2="12.06"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#DC8758" stopOpacity="0"></stop>
          <stop offset="0.215" stopColor="#DD8654" stopOpacity="0.215"></stop>
          <stop offset="0.429" stopColor="#E28448" stopOpacity="0.429"></stop>
          <stop offset="0.642" stopColor="#EA8034" stopOpacity="0.642"></stop>
          <stop offset="0.854" stopColor="#F47B18" stopOpacity="0.854"></stop>
          <stop offset="1" stopColor="#FE7701"></stop>
        </linearGradient>
        <linearGradient
          id="paint12_linear_352_2723"
          x1="31.383"
          x2="40.199"
          y1="8.978"
          y2="17.334"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#DC8758" stopOpacity="0"></stop>
          <stop offset="0.215" stopColor="#DD8654" stopOpacity="0.215"></stop>
          <stop offset="0.429" stopColor="#E28448" stopOpacity="0.429"></stop>
          <stop offset="0.642" stopColor="#EA8034" stopOpacity="0.642"></stop>
          <stop offset="0.854" stopColor="#F47B18" stopOpacity="0.854"></stop>
          <stop offset="1" stopColor="#FE7701"></stop>
        </linearGradient>
        <linearGradient
          id="paint13_linear_352_2723"
          x1="37.69"
          x2="38.637"
          y1="13.319"
          y2="13.319"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#DC8758" stopOpacity="0"></stop>
          <stop offset="0.215" stopColor="#DD8654" stopOpacity="0.215"></stop>
          <stop offset="0.429" stopColor="#E28448" stopOpacity="0.429"></stop>
          <stop offset="0.642" stopColor="#EA8034" stopOpacity="0.642"></stop>
          <stop offset="0.854" stopColor="#F47B18" stopOpacity="0.854"></stop>
          <stop offset="1" stopColor="#FE7701"></stop>
        </linearGradient>
        <linearGradient
          id="paint14_linear_352_2723"
          x1="34.667"
          x2="34.667"
          y1="14.529"
          y2="16.176"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#DC8758" stopOpacity="0"></stop>
          <stop offset="0.215" stopColor="#DD8654" stopOpacity="0.215"></stop>
          <stop offset="0.429" stopColor="#E28448" stopOpacity="0.429"></stop>
          <stop offset="0.642" stopColor="#EA8034" stopOpacity="0.642"></stop>
          <stop offset="0.854" stopColor="#F47B18" stopOpacity="0.854"></stop>
          <stop offset="1" stopColor="#FE7701"></stop>
        </linearGradient>
        <linearGradient
          id="paint15_linear_352_2723"
          x1="41.053"
          x2="48.772"
          y1="15.413"
          y2="45.376"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#C86D36"></stop>
          <stop offset="0.201" stopColor="#C46B35"></stop>
          <stop offset="0.414" stopColor="#B76233"></stop>
          <stop offset="0.633" stopColor="#A2552F"></stop>
          <stop offset="0.855" stopColor="#85422A"></stop>
          <stop offset="1" stopColor="#6D3326"></stop>
        </linearGradient>
        <linearGradient
          id="paint16_linear_352_2723"
          x1="45.314"
          x2="45.314"
          y1="44.002"
          y2="47.002"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#3A0F13" stopOpacity="0"></stop>
          <stop offset="0.272" stopColor="#3E1215" stopOpacity="0.272"></stop>
          <stop offset="0.561" stopColor="#4B1B19" stopOpacity="0.561"></stop>
          <stop offset="0.857" stopColor="#602A21" stopOpacity="0.857"></stop>
          <stop offset="1" stopColor="#6D3326"></stop>
        </linearGradient>
        <linearGradient
          id="paint17_linear_352_2723"
          x1="17.861"
          x2="63.184"
          y1="7.797"
          y2="46.538"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#3A0F13" stopOpacity="0"></stop>
          <stop offset="0.272" stopColor="#3E1215" stopOpacity="0.272"></stop>
          <stop offset="0.561" stopColor="#4B1B19" stopOpacity="0.561"></stop>
          <stop offset="0.857" stopColor="#602A21" stopOpacity="0.857"></stop>
          <stop offset="1" stopColor="#6D3326"></stop>
        </linearGradient>
        <linearGradient
          id="paint18_linear_352_2723"
          x1="39.813"
          x2="50.287"
          y1="29.491"
          y2="31.391"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#3A0F13" stopOpacity="0"></stop>
          <stop offset="0.272" stopColor="#3E1215" stopOpacity="0.272"></stop>
          <stop offset="0.561" stopColor="#4B1B19" stopOpacity="0.561"></stop>
          <stop offset="0.857" stopColor="#602A21" stopOpacity="0.857"></stop>
          <stop offset="1" stopColor="#6D3326"></stop>
        </linearGradient>
        <linearGradient
          id="paint19_linear_352_2723"
          x1="19.07"
          x2="28.602"
          y1="12.353"
          y2="49.35"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#C86D36"></stop>
          <stop offset="0.201" stopColor="#C46B35"></stop>
          <stop offset="0.414" stopColor="#B76233"></stop>
          <stop offset="0.633" stopColor="#A2552F"></stop>
          <stop offset="0.855" stopColor="#85422A"></stop>
          <stop offset="1" stopColor="#6D3326"></stop>
        </linearGradient>
        <linearGradient
          id="paint20_linear_352_2723"
          x1="23.654"
          x2="23.654"
          y1="44.002"
          y2="47.002"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#3A0F13" stopOpacity="0"></stop>
          <stop offset="0.272" stopColor="#3E1215" stopOpacity="0.272"></stop>
          <stop offset="0.561" stopColor="#4B1B19" stopOpacity="0.561"></stop>
          <stop offset="0.857" stopColor="#602A21" stopOpacity="0.857"></stop>
          <stop offset="1" stopColor="#6D3326"></stop>
        </linearGradient>
        <linearGradient
          id="paint21_linear_352_2723"
          x1="23.654"
          x2="23.654"
          y1="40.304"
          y2="32.072"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#3A0F13" stopOpacity="0"></stop>
          <stop offset="0.272" stopColor="#3E1215" stopOpacity="0.272"></stop>
          <stop offset="0.561" stopColor="#4B1B19" stopOpacity="0.561"></stop>
          <stop offset="0.857" stopColor="#602A21" stopOpacity="0.857"></stop>
          <stop offset="1" stopColor="#6D3326"></stop>
        </linearGradient>
        <linearGradient
          id="paint22_linear_352_2723"
          x1="23.023"
          x2="26.969"
          y1="7.924"
          y2="38.723"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#C86D36"></stop>
          <stop offset="0.201" stopColor="#C46B35"></stop>
          <stop offset="0.414" stopColor="#B76233"></stop>
          <stop offset="0.633" stopColor="#A2552F"></stop>
          <stop offset="0.855" stopColor="#85422A"></stop>
          <stop offset="1" stopColor="#6D3326"></stop>
        </linearGradient>
        <linearGradient
          id="paint23_linear_352_2723"
          x1="44.866"
          x2="50.427"
          y1="22.841"
          y2="22.841"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#3A0F13" stopOpacity="0"></stop>
          <stop offset="0.272" stopColor="#3E1215" stopOpacity="0.272"></stop>
          <stop offset="0.561" stopColor="#4B1B19" stopOpacity="0.561"></stop>
          <stop offset="0.857" stopColor="#602A21" stopOpacity="0.857"></stop>
          <stop offset="1" stopColor="#6D3326"></stop>
        </linearGradient>
        <linearGradient
          id="paint24_linear_352_2723"
          x1="15.268"
          x2="11.323"
          y1="37.36"
          y2="31.28"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#3A0F13" stopOpacity="0"></stop>
          <stop offset="0.272" stopColor="#3E1215" stopOpacity="0.272"></stop>
          <stop offset="0.561" stopColor="#4B1B19" stopOpacity="0.561"></stop>
          <stop offset="0.857" stopColor="#602A21" stopOpacity="0.857"></stop>
          <stop offset="1" stopColor="#6D3326"></stop>
        </linearGradient>
        <linearGradient
          id="paint25_linear_352_2723"
          x1="39.144"
          x2="34.34"
          y1="39.299"
          y2="31.502"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#3A0F13" stopOpacity="0"></stop>
          <stop offset="0.272" stopColor="#3E1215" stopOpacity="0.272"></stop>
          <stop offset="0.561" stopColor="#4B1B19" stopOpacity="0.561"></stop>
          <stop offset="0.857" stopColor="#602A21" stopOpacity="0.857"></stop>
          <stop offset="1" stopColor="#6D3326"></stop>
        </linearGradient>
        <linearGradient
          id="paint26_linear_352_2723"
          x1="8.563"
          x2="11.541"
          y1="31.18"
          y2="36.138"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#FEF0AE"></stop>
          <stop offset="1" stopColor="#FBC56D"></stop>
        </linearGradient>
        <linearGradient
          id="paint27_linear_352_2723"
          x1="9.113"
          x2="9.113"
          y1="34.353"
          y2="32.09"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#FEF0AE"></stop>
          <stop offset="1" stopColor="#FBC56D"></stop>
        </linearGradient>
        <linearGradient
          id="paint28_linear_352_2723"
          x1="6.717"
          x2="15.9"
          y1="29.142"
          y2="37.848"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#DC8758" stopOpacity="0"></stop>
          <stop offset="0.215" stopColor="#DD8654" stopOpacity="0.215"></stop>
          <stop offset="0.429" stopColor="#E28448" stopOpacity="0.429"></stop>
          <stop offset="0.642" stopColor="#EA8034" stopOpacity="0.642"></stop>
          <stop offset="0.854" stopColor="#F47B18" stopOpacity="0.854"></stop>
          <stop offset="1" stopColor="#FE7701"></stop>
        </linearGradient>
        <linearGradient
          id="paint29_linear_352_2723"
          x1="13.35"
          x2="14.382"
          y1="33.665"
          y2="33.665"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#DC8758" stopOpacity="0"></stop>
          <stop offset="0.215" stopColor="#DD8654" stopOpacity="0.215"></stop>
          <stop offset="0.429" stopColor="#E28448" stopOpacity="0.429"></stop>
          <stop offset="0.642" stopColor="#EA8034" stopOpacity="0.642"></stop>
          <stop offset="0.854" stopColor="#F47B18" stopOpacity="0.854"></stop>
          <stop offset="1" stopColor="#FE7701"></stop>
        </linearGradient>
        <linearGradient
          id="paint30_linear_352_2723"
          x1="10.056"
          x2="10.056"
          y1="34.874"
          y2="36.522"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#DC8758" stopOpacity="0"></stop>
          <stop offset="0.215" stopColor="#DD8654" stopOpacity="0.215"></stop>
          <stop offset="0.429" stopColor="#E28448" stopOpacity="0.429"></stop>
          <stop offset="0.642" stopColor="#EA8034" stopOpacity="0.642"></stop>
          <stop offset="0.854" stopColor="#F47B18" stopOpacity="0.854"></stop>
          <stop offset="1" stopColor="#FE7701"></stop>
        </linearGradient>
        <linearGradient
          id="paint31_linear_352_2723"
          x1="32.558"
          x2="35.536"
          y1="31.18"
          y2="36.138"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#FEF0AE"></stop>
          <stop offset="1" stopColor="#FBC56D"></stop>
        </linearGradient>
        <linearGradient
          id="paint32_linear_352_2723"
          x1="33.108"
          x2="33.108"
          y1="34.353"
          y2="32.09"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#FEF0AE"></stop>
          <stop offset="1" stopColor="#FBC56D"></stop>
        </linearGradient>
        <linearGradient
          id="paint33_linear_352_2723"
          x1="30.712"
          x2="39.895"
          y1="29.142"
          y2="37.848"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#DC8758" stopOpacity="0"></stop>
          <stop offset="0.215" stopColor="#DD8654" stopOpacity="0.215"></stop>
          <stop offset="0.429" stopColor="#E28448" stopOpacity="0.429"></stop>
          <stop offset="0.642" stopColor="#EA8034" stopOpacity="0.642"></stop>
          <stop offset="0.854" stopColor="#F47B18" stopOpacity="0.854"></stop>
          <stop offset="1" stopColor="#FE7701"></stop>
        </linearGradient>
        <linearGradient
          id="paint34_linear_352_2723"
          x1="37.344"
          x2="38.376"
          y1="33.665"
          y2="33.665"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#DC8758" stopOpacity="0"></stop>
          <stop offset="0.215" stopColor="#DD8654" stopOpacity="0.215"></stop>
          <stop offset="0.429" stopColor="#E28448" stopOpacity="0.429"></stop>
          <stop offset="0.642" stopColor="#EA8034" stopOpacity="0.642"></stop>
          <stop offset="0.854" stopColor="#F47B18" stopOpacity="0.854"></stop>
          <stop offset="1" stopColor="#FE7701"></stop>
        </linearGradient>
        <linearGradient
          id="paint35_linear_352_2723"
          x1="34.05"
          x2="34.05"
          y1="34.874"
          y2="36.522"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#DC8758" stopOpacity="0"></stop>
          <stop offset="0.215" stopColor="#DD8654" stopOpacity="0.215"></stop>
          <stop offset="0.429" stopColor="#E28448" stopOpacity="0.429"></stop>
          <stop offset="0.642" stopColor="#EA8034" stopOpacity="0.642"></stop>
          <stop offset="0.854" stopColor="#F47B18" stopOpacity="0.854"></stop>
          <stop offset="1" stopColor="#FE7701"></stop>
        </linearGradient>
        <linearGradient
          id="paint36_linear_352_2723"
          x1="23.654"
          x2="23.654"
          y1="4.287"
          y2="6.933"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#5A5A5A"></stop>
          <stop offset="1" stopColor="#444"></stop>
        </linearGradient>
        <linearGradient
          id="paint37_linear_352_2723"
          x1="23.654"
          x2="23.654"
          y1="4.914"
          y2="7.769"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#433F43" stopOpacity="0"></stop>
          <stop offset="1" stopColor="#1A1A1A"></stop>
        </linearGradient>
        <linearGradient
          id="paint38_linear_352_2723"
          x1="27.672"
          x2="30.039"
          y1="5.421"
          y2="6.047"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#433F43" stopOpacity="0"></stop>
          <stop offset="1" stopColor="#1A1A1A"></stop>
        </linearGradient>
        <clipPath id="clip0_352_2723">
          <path fill="#fff" d="M0 0h50v50H0z"></path>
        </clipPath>
      </defs>
    </svg>
  );
};

export const CheckIconRounded: React.FC = () => {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 14 14" fill="none">
      <path
        d="M7 0C3.14024 0 0 3.14024 0 7C0 10.8598 3.14024 14 7 14C10.8598 14 14 10.8598 14 7C14 3.14024 10.8598 0 7 0ZM10.643 4.65399L6.11995 10.0386C6.07034 10.0977 6.0086 10.1454 5.93892 10.1786C5.86925 10.2118 5.79326 10.2295 5.71611 10.2308H5.70702C5.63155 10.2307 5.55692 10.2148 5.48799 10.1841C5.41905 10.1534 5.35735 10.1085 5.30688 10.0524L3.36841 7.89856C3.31918 7.84634 3.28089 7.78481 3.25578 7.71759C3.23067 7.65036 3.21925 7.57879 3.22219 7.50709C3.22513 7.43539 3.24238 7.36499 3.27292 7.30005C3.30345 7.23511 3.34666 7.17692 3.40001 7.12892C3.45335 7.08091 3.51575 7.04406 3.58354 7.02051C3.65133 6.99697 3.72315 6.98721 3.79476 6.99182C3.86638 6.99643 3.93635 7.0153 4.00057 7.04734C4.06478 7.07937 4.12195 7.12392 4.1687 7.17837L5.69288 8.87183L9.81851 3.96139C9.91105 3.8544 10.042 3.78812 10.183 3.77689C10.324 3.76565 10.4638 3.81036 10.5721 3.90134C10.6804 3.99233 10.7486 4.12229 10.7619 4.26313C10.7751 4.40397 10.7324 4.54437 10.643 4.65399Z"
        fill="currentColor"
      />
    </svg>
  );
};

export const CloseRounded: React.FC = () => {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" fill="none" viewBox="0 0 14 14">
      <path fill="#EC761E" d="M7 14A7 7 0 1 0 7 0a7 7 0 0 0 0 14"></path>
      <path
        stroke="#FEF7EE"
        strokeLinecap="round"
        strokeLinejoin="round"
        d="m4.375 4.375 5.25 5.25M9.625 4.375l-5.25 5.25"
      ></path>
    </svg>
  );
};

export const AchievementIcon: React.FC = () => {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="49" height="49" fill="none" viewBox="0 0 49 49">
      <path
        fill="url(#paint0_radial_371_4419)"
        d="M42.12 24.845a2 2 0 0 1-.22.059l-.122.025a2.14 2.14 0 0 1-1.625-.302 2.15 2.15 0 0 1-.936-1.362 2.875 2.875 0 0 1 1.293-3.032l5.285-3.281a2.94 2.94 0 0 0 1.32-3.096l-.173-.814a2.93 2.93 0 0 0-3.473-2.258 2.9 2.9 0 0 0-1.847 1.27 2.9 2.9 0 0 0-.41 2.203l.143.672a.87.87 0 0 0 1.698-.36.911.911 0 0 1 1.783-.378 2.693 2.693 0 0 1-4.098 2.814 2.67 2.67 0 0 1-1.166-1.697l-.142-.673a4.72 4.72 0 0 1 .664-3.575 4.72 4.72 0 0 1 2.998-2.058 4.756 4.756 0 0 1 5.633 3.662l.172.814a4.76 4.76 0 0 1-2.141 5.022l-5.285 3.282A1.05 1.05 0 0 0 41 22.887a.337.337 0 0 0 .4.26l.123-.026a.336.336 0 0 0 .26-.4.91.91 0 1 1 1.782-.378c.12.564.013 1.142-.302 1.625-.273.42-.674.726-1.144.877"
      ></path>
      <path
        fill="url(#paint1_radial_371_4419)"
        d="M6.88 24.845q.108.035.22.059l.122.025c.564.12 1.141.013 1.625-.302.484-.314.816-.798.936-1.362a2.875 2.875 0 0 0-1.293-3.032l-5.285-3.281a2.94 2.94 0 0 1-1.32-3.096l.173-.814a2.93 2.93 0 0 1 3.473-2.258 2.9 2.9 0 0 1 1.847 1.27 2.9 2.9 0 0 1 .41 2.203l-.143.672a.87.87 0 0 1-1.698-.36.911.911 0 0 0-1.783-.378 2.693 2.693 0 0 0 4.098 2.814 2.67 2.67 0 0 0 1.166-1.697l.142-.673a4.72 4.72 0 0 0-.664-3.575 4.72 4.72 0 0 0-2.998-2.058 4.756 4.756 0 0 0-5.633 3.662l-.172.814A4.76 4.76 0 0 0 2.244 18.5L7.53 21.78c.373.232.562.676.471 1.106a.336.336 0 0 1-.4.26l-.123-.026a.336.336 0 0 1-.26-.4.911.911 0 0 0-1.782-.378 2.15 2.15 0 0 0 .302 1.625c.273.42.674.726 1.144.877"
      ></path>
      <path
        fill="url(#paint2_linear_371_4419)"
        d="m39.43 14.636.142.672c.15.703.563 1.306 1.166 1.697a2.692 2.692 0 0 0 4.098-2.814.911.911 0 1 0-1.783.378.86.86 0 0 1-.121.654.87.87 0 0 1-1.577-.293l-.143-.672a2.9 2.9 0 0 1 .218-1.861l-1.336-1.337a4.72 4.72 0 0 0-.664 3.576"
      ></path>
      <path
        fill="url(#paint3_linear_371_4419)"
        d="M43.263 23.968c.315-.483.422-1.06.302-1.625a.911.911 0 0 0-1.782.378.337.337 0 0 1-.26.4l-.123.027a.337.337 0 0 1-.4-.26 1.05 1.05 0 0 1 .299-.971l-1.288-1.288a2.88 2.88 0 0 0-.794 2.636c.12.565.453 1.049.936 1.363a2.15 2.15 0 0 0 1.625.302l.123-.026q.111-.024.218-.058c.47-.152.871-.458 1.144-.878"
      ></path>
      <path
        fill="url(#paint4_radial_371_4419)"
        d="M35.983 42.571c0-1.615-5.141-2.924-11.483-2.924-6.341 0-11.482 1.309-11.482 2.924 0 1.616 5.14 2.925 11.482 2.925s11.483-1.31 11.483-2.925"
      ></path>
      <path
        fill="url(#paint5_linear_371_4419)"
        d="M33.945 44.235c-2.073.761-5.53 1.26-9.445 1.26q-1.075 0-2.096-.048l-3.975-3.975a.6.6 0 0 1-.162-.19l-.012-.03-.008-.023a.24.24 0 0 1-.002-.124h-.007l.008-.005c.04-.17.246-.332.588-.479l.218-.094q.201-.088.399-.181l.032-.015q.29-.137.574-.283.162-.084.32-.173l.076-.042h.002a43 43 0 0 1 4.045-.186c1.424 0 2.787.065 4.045.186.524.296 1.068.551 1.621.788q.227.098.373.204v.004z"
      ></path>
      <path
        fill="url(#paint6_linear_371_4419)"
        d="m39.92 10.331-.962-3.96H10.043l-.963 3.96a22.55 22.55 0 0 0-.292 9.282c1.066 6 5.451 10.69 11.05 12.391a4.33 4.33 0 0 1 3.08 4.14v.001a3.93 3.93 0 0 1-1.976 3.41l-.406.232c-.549.315-1.12.584-1.702.834-.342.146-.549.308-.588.48l-.008.004h.007a.3.3 0 0 0-.007.055c0 .7 2.804 1.267 6.262 1.267s6.262-.567 6.262-1.267a.3.3 0 0 0-.007-.055h.007l-.008-.005c-.04-.17-.246-.333-.588-.48a17 17 0 0 1-1.702-.833l-.406-.233a3.93 3.93 0 0 1-1.975-3.409 4.33 4.33 0 0 1 3.083-4.141c5.575-1.693 9.952-6.349 11.033-12.317a22.5 22.5 0 0 0-.279-9.356"
      ></path>
      <path
        fill="url(#paint7_linear_371_4419)"
        d="m39.92 10.331-.962-3.96H10.043l-.963 3.96a22.55 22.55 0 0 0-.292 9.282c1.066 6 5.451 10.69 11.05 12.391a4.34 4.34 0 0 1 2.123 1.432l4.62 4.619a3.9 3.9 0 0 1-.498-1.91 4.33 4.33 0 0 1 3.083-4.141c5.575-1.693 9.952-6.349 11.033-12.317a22.5 22.5 0 0 0-.279-9.356"
      ></path>
      <path
        fill="url(#paint8_radial_371_4419)"
        d="M40.561 15.667c0 1.355-.122 2.7-.361 4.02-1.37 7.557-8.02 13.01-15.7 13.01-7.707 0-14.363-5.495-15.712-13.084a22.5 22.5 0 0 1 .292-9.281l.963-3.96h28.915l.962 3.96c.431 1.77.641 3.56.641 5.335"
      ></path>
      <path
        fill="url(#paint9_linear_371_4419)"
        d="M38.957 6.37c0-1.615-6.473-2.925-14.457-2.925S10.043 4.755 10.043 6.37c0 1.616 6.473 2.925 14.457 2.925s14.457-1.31 14.457-2.925"
      ></path>
      <path
        fill="url(#paint10_linear_371_4419)"
        d="M37.569 6.125c0-1.206-5.851-2.184-13.07-2.184-7.217 0-13.068.978-13.068 2.184S17.282 8.31 24.5 8.31s13.069-.978 13.069-2.184"
      ></path>
      <path
        fill="#FFB211"
        d="M21 16.667q0-.627.507-.99l2.508-2.412a.89.89 0 0 1 .652-.265q.434 0 .784.23.35.228.35.614v11.97q-.001.387-.386.616-.386.23-.893.23-.53 0-.904-.23-.375-.23-.375-.615v-9.413l-.844 1.06q-.241.242-.53.242-.363 0-.616-.326a1.13 1.13 0 0 1-.253-.711"
      ></path>
      <defs>
        <linearGradient
          id="paint2_linear_371_4419"
          x1="42.934"
          x2="37.603"
          y1="14.456"
          y2="13.869"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#FFB211" stopOpacity="0"></stop>
          <stop offset="0.228" stopColor="#FF9F19" stopOpacity="0.227"></stop>
          <stop offset="0.686" stopColor="#FF6F2E" stopOpacity="0.686"></stop>
          <stop offset="1" stopColor="#FF4B3E"></stop>
        </linearGradient>
        <linearGradient
          id="paint3_linear_371_4419"
          x1="42.031"
          x2="37.849"
          y1="22.96"
          y2="22.499"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#FFB211" stopOpacity="0"></stop>
          <stop offset="0.228" stopColor="#FF9F19" stopOpacity="0.227"></stop>
          <stop offset="0.686" stopColor="#FF6F2E" stopOpacity="0.686"></stop>
          <stop offset="1" stopColor="#FF4B3E"></stop>
        </linearGradient>
        <linearGradient
          id="paint5_linear_371_4419"
          x1="26.316"
          x2="25.163"
          y1="44.127"
          y2="36.818"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#FFB211" stopOpacity="0"></stop>
          <stop offset="0.228" stopColor="#FF9F19" stopOpacity="0.227"></stop>
          <stop offset="0.686" stopColor="#FF6F2E" stopOpacity="0.686"></stop>
          <stop offset="1" stopColor="#FF4B3E"></stop>
        </linearGradient>
        <linearGradient
          id="paint6_linear_371_4419"
          x1="23.627"
          x2="27.764"
          y1="24.399"
          y2="24.399"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#FFE548"></stop>
          <stop offset="1" stopColor="#FFB211"></stop>
        </linearGradient>
        <linearGradient
          id="paint7_linear_371_4419"
          x1="29.699"
          x2="26.583"
          y1="35.743"
          y2="23.622"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#FFB211" stopOpacity="0"></stop>
          <stop offset="0.181" stopColor="#FF931F" stopOpacity="0.18"></stop>
          <stop offset="0.401" stopColor="#FF732C" stopOpacity="0.4"></stop>
          <stop offset="0.615" stopColor="#FF5D36" stopOpacity="0.616"></stop>
          <stop offset="0.818" stopColor="#FF503C" stopOpacity="0.82"></stop>
          <stop offset="1" stopColor="#FF4B3E"></stop>
        </linearGradient>
        <linearGradient
          id="paint9_linear_371_4419"
          x1="16.797"
          x2="32.872"
          y1="1.494"
          y2="11.671"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#FFE548"></stop>
          <stop offset="1" stopColor="#FFB211"></stop>
        </linearGradient>
        <linearGradient
          id="paint10_linear_371_4419"
          x1="24.5"
          x2="24.5"
          y1="11.666"
          y2="0.235"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#FFB211" stopOpacity="0"></stop>
          <stop offset="0.228" stopColor="#FF9F19" stopOpacity="0.227"></stop>
          <stop offset="0.686" stopColor="#FF6F2E" stopOpacity="0.686"></stop>
          <stop offset="1" stopColor="#FF4B3E"></stop>
        </linearGradient>
        <radialGradient
          id="paint0_radial_371_4419"
          cx="0"
          cy="0"
          r="1"
          gradientTransform="rotate(-17.879 65.03 -115.393)scale(10.2833)"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#FFE548"></stop>
          <stop offset="0.431" stopColor="#FFE346"></stop>
          <stop offset="0.636" stopColor="#FFDC3E"></stop>
          <stop offset="0.793" stopColor="#FFD031"></stop>
          <stop offset="0.926" stopColor="#FFBF1F"></stop>
          <stop offset="1" stopColor="#FFB211"></stop>
        </radialGradient>
        <radialGradient
          id="paint1_radial_371_4419"
          cx="0"
          cy="0"
          r="1"
          gradientTransform="rotate(-162.121 6.349 6.375)scale(10.2833)"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#FFE548"></stop>
          <stop offset="0.431" stopColor="#FFE346"></stop>
          <stop offset="0.636" stopColor="#FFDC3E"></stop>
          <stop offset="0.793" stopColor="#FFD031"></stop>
          <stop offset="0.926" stopColor="#FFBF1F"></stop>
          <stop offset="1" stopColor="#FFB211"></stop>
        </radialGradient>
        <radialGradient
          id="paint4_radial_371_4419"
          cx="0"
          cy="0"
          r="1"
          gradientTransform="matrix(13.9705 0 0 6.17636 23.233 39.328)"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#FFE548"></stop>
          <stop offset="0.441" stopColor="#FFE346"></stop>
          <stop offset="0.643" stopColor="#FFDC3E"></stop>
          <stop offset="0.797" stopColor="#FFD032"></stop>
          <stop offset="0.925" stopColor="#FFC020"></stop>
          <stop offset="1" stopColor="#FFB211"></stop>
        </radialGradient>
        <radialGradient
          id="paint8_radial_371_4419"
          cx="0"
          cy="0"
          r="1"
          gradientTransform="translate(14.366 8.95)scale(28.5964)"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#FFE548"></stop>
          <stop offset="0.431" stopColor="#FFE346"></stop>
          <stop offset="0.636" stopColor="#FFDC3E"></stop>
          <stop offset="0.793" stopColor="#FFD031"></stop>
          <stop offset="0.926" stopColor="#FFBF1F"></stop>
          <stop offset="1" stopColor="#FFB211"></stop>
        </radialGradient>
      </defs>
    </svg>
  );
};

export const CrownIcon: React.FC = () => {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="none" viewBox="0 0 20 20">
      <path
        fill="#007AB5"
        d="M18.73 7.094q.001.013-.005.025l-1.772 8.115a1.25 1.25 0 0 1-1.228 1.016H4.275a1.25 1.25 0 0 1-1.228-1.016L1.275 7.12q-.002-.012-.006-.025a1.25 1.25 0 0 1 2.168-1.048l2.63 2.835 2.798-6.273v-.007a1.25 1.25 0 0 1 2.27 0v.007l2.797 6.273 2.63-2.835a1.25 1.25 0 0 1 2.165 1.048z"
      ></path>
    </svg>
  );
};

export const XCircleIcon: React.FC = () => {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
      <path
        fill="#737373"
        d="M15.796 9.796 13.594 12l2.205 2.204a1.127 1.127 0 0 1-1.594 1.594L12 13.594l-2.204 2.205a1.127 1.127 0 1 1-1.594-1.594L10.406 12 8.204 9.796a1.127 1.127 0 1 1 1.594-1.594L12 10.406l2.204-2.205a1.127 1.127 0 1 1 1.594 1.594zM22.125 12A10.124 10.124 0 1 1 12 1.875 10.136 10.136 0 0 1 22.125 12m-2.25 0A7.875 7.875 0 1 0 12 19.875 7.883 7.883 0 0 0 19.875 12"
      ></path>
    </svg>
  );
};

export const PlusIcon: React.FC<{ isCenter?: boolean }> = ({ isCenter = false }) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="44"
      height="44"
      fill="none"
      viewBox="0 0 44 44"
      className={`${isCenter ? "mx-auto" : ""} `}
    >
      <path
        fill="#EC761E"
        fillRule="evenodd"
        d="M22 0C9.85 0 0 9.85 0 22s9.85 22 22 22 22-9.85 22-22S34.15 0 22 0m2 30a2 2 0 1 1-4 0v-6h-6a2 2 0 1 1 0-4h6v-6a2 2 0 1 1 4 0v6h6a2 2 0 1 1 0 4h-6z"
        clipRule="evenodd"
      ></path>
    </svg>
  );
};

export const HandShakeIcon: React.FC = () => {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="11" height="11" fill="none" viewBox="0 0 11 11">
      <g clipPath="url(#clip0_455_602)">
        <path
          fill="currentColor"
          d="m9.106 1.498 1.3.65.428-.02v5.551h-.81l-3.67-3.067.74-.71-.59-.617-1.782 1.707a.426.426 0 0 1-.652-.058c-.117-.162-.081-.416.077-.575L6.84 1.732a2.16 2.16 0 0 1 2.267-.234m-3.374 3.71-.413.394a1.27 1.27 0 0 1-.898.369c-.42 0-.822-.229-1.044-.538-.361-.5-.288-1.224.17-1.682l1.711-1.67-.263-.253a2.13 2.13 0 0 0-1.448-.555c-.33 0-.658.078-.951.225l-.832.63H.584V7.68h1.139l3.986 2.99 3.456-2.594z"
        ></path>
      </g>
      <defs>
        <clipPath id="clip0_455_602">
          <path fill="currentColor" d="M.583.418h10.25v10.25H.583z"></path>
        </clipPath>
      </defs>
    </svg>
  );
};

export const CheckeDouble: React.FC = () => {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
      <path
        fill="currentColor"
        d="m14.289 8.303-8.4 8.25a1.125 1.125 0 0 1-1.577 0l-3.6-3.536a1.125 1.125 0 1 1 1.577-1.605L5.1 14.173l7.612-7.475a1.125 1.125 0 0 1 1.577 1.605zm9.014-1.594a1.124 1.124 0 0 0-1.594-.014L14.1 14.173l-.738-.725a1.125 1.125 0 1 0-1.577 1.605l1.527 1.5a1.125 1.125 0 0 0 1.577 0l8.4-8.25a1.126 1.126 0 0 0 .014-1.591z"
      ></path>
    </svg>
  );
};

export const CrossRounded: React.FC = () => {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="25" fill="none" viewBox="0 0 24 25">
      <path
        fill="#FF4B3E"
        d="m15.796 9.962-2.202 2.204 2.205 2.204a1.127 1.127 0 0 1-1.594 1.594L12 13.76l-2.204 2.205a1.127 1.127 0 0 1-1.594-1.594l2.204-2.205-2.202-2.204a1.127 1.127 0 1 1 1.594-1.594L12 10.572l2.204-2.205a1.127 1.127 0 1 1 1.594 1.594zm6.329 2.204A10.125 10.125 0 1 1 12 2.041a10.136 10.136 0 0 1 10.125 10.125m-2.25 0A7.875 7.875 0 1 0 12 20.041a7.883 7.883 0 0 0 7.875-7.875"
      ></path>
    </svg>
  );
};

export const FileArrowDown: React.FC = () => {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
      <path
        fill="currentColor"
        d="m20.296 7.455-5.25-5.25a1.12 1.12 0 0 0-.796-.33h-9A1.875 1.875 0 0 0 3.375 3.75v16.5a1.875 1.875 0 0 0 1.875 1.875h13.5a1.875 1.875 0 0 0 1.875-1.875v-12c0-.298-.118-.584-.33-.795M15 5.344 17.156 7.5H15zM5.625 19.875V4.125h7.125v4.5a1.125 1.125 0 0 0 1.125 1.125h4.5v10.125zm9.42-5.67a1.125 1.125 0 0 1 0 1.593l-2.25 2.25a1.125 1.125 0 0 1-1.593 0l-2.25-2.25a1.127 1.127 0 1 1 1.594-1.594l.329.327v-2.906a1.125 1.125 0 1 1 2.25 0v2.906l.33-.33a1.124 1.124 0 0 1 1.59.003"
      ></path>
    </svg>
  );
};

export const ChatDots: React.FC = () => {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
      <path
        fill="currentColor"
        d="M8.25 12a1.5 1.5 0 1 1 3 0 1.5 1.5 0 0 1-3 0m6 1.5a1.5 1.5 0 1 0 0-3 1.5 1.5 0 0 0 0 3M22.125 6v12a1.875 1.875 0 0 1-1.875 1.875H7.92l-2.951 2.547-.011.01a1.87 1.87 0 0 1-2 .265A1.86 1.86 0 0 1 1.876 21V6A1.875 1.875 0 0 1 3.75 4.125h16.5A1.875 1.875 0 0 1 22.125 6m-2.25.375H4.125v13.803l2.64-2.28c.204-.177.465-.274.735-.273h12.375z"
      ></path>
    </svg>
  );
};

export const Prohibitlnst: React.FC = () => {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
      <path
        fill="currentColor"
        d="M15.796 14.204a1.127 1.127 0 1 1-1.594 1.594l-6-6a1.127 1.127 0 0 1 1.594-1.594zM22.125 12A10.124 10.124 0 1 1 12 1.875 10.136 10.136 0 0 1 22.125 12m-2.25 0A7.875 7.875 0 1 0 12 19.875 7.883 7.883 0 0 0 19.875 12"
      ></path>
    </svg>
  );
};

export const DiplomaIcon: React.FC = () => {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="36" height="36" fill="none" viewBox="0 0 36 36">
      <g clipPath="url(#clip0_4406_1457)">
        <path
          fill="url(#paint0_linear_4406_1457)"
          d="M33.754 1.038v30.558H4.148V1.038C4.148.465 4.613 0 5.187 0h27.529c.573 0 1.038.465 1.038 1.038"
        ></path>
        <path
          fill="url(#paint1_linear_4406_1457)"
          d="M4.141 27.309c-1.05.044-1.895 1.971-1.895 4.344s.845 4.3 1.895 4.344v.002h27.683c1.066 0 1.93-1.946 1.93-4.346s-.864-4.346-1.93-4.346H4.14z"
        ></path>
        <path
          fill="url(#paint2_linear_4406_1457)"
          d="M31.824 27.307c1.066 0 1.93 1.946 1.93 4.346s-.864 4.346-1.93 4.346c-1.068 0-1.932-1.946-1.932-4.346s.864-4.346 1.931-4.346"
        ></path>
        <path
          fill="url(#paint3_linear_4406_1457)"
          d="M31.823 27.307c1.067 0 1.931 1.946 1.931 4.346q0 .41-.032.8a3.6 3.6 0 0 1-.149.59l-.025.071c-.122.323-.285.567-.47.693a.53.53 0 0 1-.29.097H30.17a8.8 8.8 0 0 1-.279-2.31c.014-2.373.873-4.287 1.931-4.287"
        ></path>
        <path
          fill="url(#paint4_linear_4406_1457)"
          d="M29.893 31.596h1.894v.058c0 1.243.447 2.251 1 2.251h-2.616a8.8 8.8 0 0 1-.28-2.251q0-.03.002-.058"
        ></path>
        <path
          fill="#BEC3D2"
          d="M19.805 25.766H9.64a.283.283 0 0 1-.283-.283v-.987c0-.156.127-.283.283-.283h10.164c.156 0 .283.127.283.283v.987a.283.283 0 0 1-.283.283M24.834 25.766h-3.193a.283.283 0 0 1-.283-.283v-.987c0-.156.127-.283.283-.283h3.193c.157 0 .283.127.283.283v.987a.283.283 0 0 1-.283.283M28.261 25.766H26.57a.283.283 0 0 1-.283-.283v-.987c0-.156.127-.283.283-.283h1.691c.157 0 .283.127.283.283v.987a.283.283 0 0 1-.283.283M24.81 16.324H14.646a.283.283 0 0 1-.283-.283v-.987c0-.156.127-.283.283-.283H24.81c.156 0 .283.127.283.283v.987a.283.283 0 0 1-.283.283M12.834 16.324H9.641a.283.283 0 0 1-.283-.283v-.987c0-.156.127-.283.283-.283h3.193c.157 0 .283.127.283.283v.987a.283.283 0 0 1-.283.283M28.261 16.324H26.57a.283.283 0 0 1-.283-.283v-.987c0-.156.127-.283.283-.283h1.691c.157 0 .283.127.283.283v.987a.283.283 0 0 1-.283.283M18.097 10.052h10.164c.156 0 .283.127.283.283v.987a.283.283 0 0 1-.283.283H18.097a.283.283 0 0 1-.283-.283v-.987c0-.157.127-.283.283-.283M13.068 10.052h3.193c.157 0 .283.127.283.283v.987a.283.283 0 0 1-.283.283h-3.193a.283.283 0 0 1-.283-.283v-.987c0-.157.127-.283.283-.283M9.641 10.052h1.691c.157 0 .283.127.283.283v.987a.283.283 0 0 1-.283.283h-1.69a.283.283 0 0 1-.284-.283v-.987c0-.157.127-.283.283-.283M13.093 19.49h10.163c.156 0 .283.127.283.284v.987a.283.283 0 0 1-.283.283H13.093a.283.283 0 0 1-.283-.283v-.987c0-.157.126-.283.282-.283M25.068 19.49h3.193c.157 0 .283.127.283.284v.987a.283.283 0 0 1-.283.283h-3.193a.283.283 0 0 1-.283-.283v-.987c0-.157.127-.283.283-.283M9.641 19.49h1.691c.157 0 .283.127.283.284v.987a.283.283 0 0 1-.283.283h-1.69a.283.283 0 0 1-.284-.283v-.987c0-.157.127-.283.283-.283"
        ></path>
        <path
          fill="url(#paint5_linear_4406_1457)"
          d="M32.316 2.847a1.2 1.2 0 0 0-.484-.405l-1.402-.641-.033-.017-.041-.033-1.35-1.363C28.723.08 28.51.083 28.1.166l-1.51.307a1.2 1.2 0 0 1-.474 0L24.608.166a1.2 1.2 0 0 0-1.117.363L22.45 1.664a1.2 1.2 0 0 1-.383.278l-1.402.641c-.38.174-.641.534-.69.948l-.177 1.532q-.027.239-.145.449l-.757 1.344a1.2 1.2 0 0 0 0 1.171l.757 1.344c.078.138.127.291.145.449l.178 1.39c.04.344.015.65.294.843.125.077.899-.173 1.017-.092.034.05-.34 1.238-.34 1.444v6.908c0 .169.151.281.3.258v.043L32.23 31.597h1.525V4.287z"
        ></path>
        <path
          fill="url(#paint6_linear_4406_1457)"
          d="M26.124 8.15c-2.872.01-5.177 2.381-5.177 5.253v6.907c0 .21.236.335.409.214l4.294-2.992a.86.86 0 0 1 .985 0l4.294 2.992a.26.26 0 0 0 .41-.214v-6.964a5.196 5.196 0 0 0-5.215-5.196"
        ></path>
        <path
          fill="url(#paint7_linear_4406_1457)"
          d="M31.338 20.31v-6.964a5.196 5.196 0 0 0-5.214-5.196c-2.468.009-4.519 1.761-5.046 4.077l.014.064 5.088 5.088a.86.86 0 0 1 .455.153l4.293 2.992a.26.26 0 0 0 .41-.214"
        ></path>
        <path
          fill="url(#paint8_linear_4406_1457)"
          d="M23.157 9.11v10.162l-1.107.771v-9.869a5.3 5.3 0 0 1 1.106-1.063z"
        ></path>
        <path
          fill="url(#paint9_linear_4406_1457)"
          d="M30.235 10.146v9.896l-1.107-.77V9.095a5.2 5.2 0 0 1 1.107 1.05"
        ></path>
        <path
          fill="url(#paint10_linear_4406_1457)"
          d="M26.143 8.153v9.228a.86.86 0 0 1 .492.154l4.294 2.992a.26.26 0 0 0 .41-.214V13.35a5.196 5.196 0 0 0-5.196-5.196"
        ></path>
        <path
          fill="url(#paint11_linear_4406_1457)"
          d="m30.929 19.684-4.294-2.992a.86.86 0 0 0-.985 0l-4.294 2.992a.26.26 0 0 1-.41-.214v.842c0 .21.237.334.41.214l4.294-2.991a.86.86 0 0 1 .985 0l4.294 2.991a.26.26 0 0 0 .41-.214v-.842a.26.26 0 0 1-.41.214"
        ></path>
        <path
          fill="url(#paint12_linear_4406_1457)"
          d="M23.157 9.11v5.249l-1.107-1.107v-3.078l.14-.173.061-.071q.061-.07.127-.14l.047-.049a4 4 0 0 1 .256-.248l.024-.022.068-.06a5 5 0 0 1 .326-.26l.057-.04z"
        ></path>
        <path
          fill="url(#paint13_linear_4406_1457)"
          d="M30.235 10.146v9.896l-1.107-.77V9.095a5.2 5.2 0 0 1 1.107 1.05"
        ></path>
        <path
          fill="url(#paint14_radial_4406_1457)"
          d="m33.6 7.885-.756 1.344c-.078.138-.128.29-.146.448l-.177 1.532c-.048.415-.31.774-.69.948l-1.401.641a1.2 1.2 0 0 0-.383.279l-1.042 1.134a1.19 1.19 0 0 1-1.116.363l-1.51-.306a1.2 1.2 0 0 0-.474 0l-1.51.306a1.2 1.2 0 0 1-1.116-.363l-1.041-1.134a1.2 1.2 0 0 0-.383-.279l-1.402-.64a1.2 1.2 0 0 1-.69-.95l-.177-1.531a1.2 1.2 0 0 0-.145-.448l-.757-1.344a1.2 1.2 0 0 1 0-1.172l.756-1.344q.118-.209.146-.448l.178-1.532c.048-.415.31-.775.689-.948l1.402-.641q.22-.101.383-.279l1.04-1.134a1.19 1.19 0 0 1 1.118-.363l1.509.306c.156.032.318.032.475 0l1.509-.306c.41-.083.834.054 1.116.363l1.042 1.134c.107.118.238.213.383.279l1.401.64c.38.174.642.534.69.949l.177 1.532c.019.157.068.31.146.448l.757 1.344c.205.364.205.808 0 1.172"
        ></path>
        <path
          fill="url(#paint15_radial_4406_1457)"
          d="m19.41 6.77.683-1.213c.07-.124.115-.262.132-.404l.16-1.383c.043-.374.28-.699.622-.856l1.265-.578q.199-.091.346-.251l.94-1.025a1.08 1.08 0 0 1 1.008-.327l1.362.276c.141.03.287.03.428 0l1.362-.276c.37-.075.753.05 1.008.327l.94 1.024c.097.106.215.192.346.252l1.265.578c.343.157.579.482.622.856l.16 1.383c.017.142.061.28.132.404l.683 1.213c.185.329.185.73 0 1.058L32.19 9.04q-.106.19-.132.404l-.16 1.383c-.043.374-.28.699-.622.856l-1.265.578q-.198.091-.346.252l-.94 1.024a1.08 1.08 0 0 1-1.008.327l-1.362-.276a1.1 1.1 0 0 0-.428 0l-1.362.276c-.37.075-.753-.05-1.008-.327l-.94-1.024a1.1 1.1 0 0 0-.346-.252l-1.265-.578a1.08 1.08 0 0 1-.622-.856l-.16-1.383a1.1 1.1 0 0 0-.132-.404l-.683-1.213a1.08 1.08 0 0 1 0-1.057"
        ></path>
        <path
          fill="url(#paint16_linear_4406_1457)"
          d="M26.142 13.191a5.891 5.891 0 1 0 0-11.782 5.891 5.891 0 0 0 0 11.782"
        ></path>
        <path
          fill="url(#paint17_linear_4406_1457)"
          d="M30.956 5.753a.35.35 0 0 0-.266-.194l-1.31-.19-2.921-2.915a.35.35 0 0 0-.317-.198.35.35 0 0 0-.316.198l-1.302 2.679-2.93.426a.35.35 0 0 0-.285.24.35.35 0 0 0 .092.363l2.077 2.07.014.018-.48 2.8a.35.35 0 0 0 .136.34l2.86 2.86q.187-.02.372.017l1.508.306c.41.083.834-.054 1.117-.363l1.042-1.134c.107-.118.238-.213.383-.279l1.401-.64c.38-.174.642-.534.69-.949l.177-1.532c.018-.157.068-.31.146-.448l.572-1.016z"
        ></path>
        <path
          fill="url(#paint18_linear_4406_1457)"
          d="M30.975 5.8a.35.35 0 0 0-.285-.241l-2.93-.426-1.302-2.679a.35.35 0 0 0-.316-.198.35.35 0 0 0-.316.198l-1.302 2.679-2.93.426a.35.35 0 0 0-.285.24.35.35 0 0 0 .092.363l2.105 2.006-.494 2.881a.35.35 0 0 0 .136.341c.1.075.238.098.367.033l2.627-1.309 2.627 1.31c.129.064.268.04.367-.034.1-.074.16-.2.136-.34l-.494-2.882 2.105-2.006a.35.35 0 0 0 .092-.363m-4.833 1.532-.004.001.002-.002.002-.003.003.003.002.002z"
        ></path>
        <path
          fill="url(#paint19_linear_4406_1457)"
          d="m26.14 7.331-.002.002-2.632.835-2.105-2.007a.35.35 0 0 1-.092-.362z"
        ></path>
        <path
          fill="url(#paint20_linear_4406_1457)"
          d="m26.138 7.335-2.99 4.057a.35.35 0 0 1-.136-.34l.494-2.882z"
        ></path>
        <path
          fill="url(#paint21_linear_4406_1457)"
          d="M29.137 11.39a.35.35 0 0 1-.368.034l-2.626-1.31V7.332l.004.001z"
        ></path>
        <path
          fill="url(#paint22_linear_4406_1457)"
          d="M26.142 7.332v2.782l-2.627 1.31a.35.35 0 0 1-.367-.034l2.99-4.057z"
        ></path>
        <path
          fill="url(#paint23_linear_4406_1457)"
          d="m29.137 11.392-2.99-4.057 2.631.835.494 2.881a.35.35 0 0 1-.135.34"
        ></path>
        <path
          fill="url(#paint24_linear_4406_1457)"
          d="m26.142 7.328-.002.003L21.31 5.8c.038-.12.139-.22.284-.24l2.93-.426z"
        ></path>
        <path
          fill="url(#paint25_linear_4406_1457)"
          d="m27.76 5.133-1.617 2.195V2.256c.126 0 .252.066.316.198z"
        ></path>
        <path
          fill="url(#paint26_linear_4406_1457)"
          d="M26.142 2.256v5.072l-1.618-2.195 1.302-2.679a.35.35 0 0 1 .316-.198"
        ></path>
        <path
          fill="url(#paint27_linear_4406_1457)"
          d="m30.975 5.8-4.83 1.531-.002-.003 1.618-2.195 2.93.426c.145.02.246.12.284.24"
        ></path>
        <path
          fill="url(#paint28_linear_4406_1457)"
          d="m30.883 6.162-2.105 2.006-2.632-.835-.002-.002 4.83-1.532a.35.35 0 0 1-.091.362"
        ></path>
        <path
          fill="url(#paint29_linear_4406_1457)"
          d="M30.156 5.981a.29.29 0 0 0-.236-.2l-2.434-.354-1.081-2.225a.29.29 0 0 0-.263-.164.29.29 0 0 0-.263.164l-1.081 2.225-2.434.354a.29.29 0 0 0-.236.2.29.29 0 0 0 .076.3l1.748 1.667-.41 2.394c-.02.117.03.221.113.283a.29.29 0 0 0 .305.028l2.182-1.088 2.182 1.088a.29.29 0 0 0 .305-.028.29.29 0 0 0 .113-.283l-.41-2.394 1.748-1.666a.29.29 0 0 0 .076-.3m-4.014 1.273-.004.001.002-.001.002-.003.002.003.002.001z"
        ></path>
      </g>
      <defs>
        <linearGradient
          id="paint0_linear_4406_1457"
          x1="7.443"
          x2="39.465"
          y1="4.594"
          y2="36.615"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#F9F7FC"></stop>
          <stop offset="1" stopColor="#F0DDFC"></stop>
        </linearGradient>
        <linearGradient
          id="paint1_linear_4406_1457"
          x1="18"
          x2="18"
          y1="33.943"
          y2="27.842"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#F9F7FC"></stop>
          <stop offset="1" stopColor="#F0DDFC"></stop>
        </linearGradient>
        <linearGradient
          id="paint2_linear_4406_1457"
          x1="35.298"
          x2="28.585"
          y1="31.653"
          y2="31.653"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#F0DDFC" stopOpacity="0"></stop>
          <stop offset="0.289" stopColor="#C8B7E0" stopOpacity="0.289"></stop>
          <stop offset="0.592" stopColor="#A595C8" stopOpacity="0.592"></stop>
          <stop offset="0.84" stopColor="#8F81B8" stopOpacity="0.84"></stop>
          <stop offset="1" stopColor="#8779B3"></stop>
        </linearGradient>
        <linearGradient
          id="paint3_linear_4406_1457"
          x1="34.651"
          x2="28.409"
          y1="28.161"
          y2="34.402"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#F0DDFC" stopOpacity="0"></stop>
          <stop offset="0.289" stopColor="#C8B7E0" stopOpacity="0.289"></stop>
          <stop offset="0.592" stopColor="#A595C8" stopOpacity="0.592"></stop>
          <stop offset="0.84" stopColor="#8F81B8" stopOpacity="0.84"></stop>
          <stop offset="1" stopColor="#8779B3"></stop>
        </linearGradient>
        <linearGradient
          id="paint4_linear_4406_1457"
          x1="31.4"
          x2="26.341"
          y1="33.09"
          y2="29.009"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#F9F7FC"></stop>
          <stop offset="1" stopColor="#F0DDFC"></stop>
        </linearGradient>
        <linearGradient
          id="paint5_linear_4406_1457"
          x1="34.898"
          x2="20.272"
          y1="19.51"
          y2="6.633"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#F0DDFC" stopOpacity="0"></stop>
          <stop offset="0.289" stopColor="#C8B7E0" stopOpacity="0.289"></stop>
          <stop offset="0.592" stopColor="#A595C8" stopOpacity="0.592"></stop>
          <stop offset="0.84" stopColor="#8F81B8" stopOpacity="0.84"></stop>
          <stop offset="1" stopColor="#8779B3"></stop>
        </linearGradient>
        <linearGradient
          id="paint6_linear_4406_1457"
          x1="23.234"
          x2="31.334"
          y1="12.206"
          y2="22.743"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#FF7044"></stop>
          <stop offset="1" stopColor="#F92814"></stop>
        </linearGradient>
        <linearGradient
          id="paint7_linear_4406_1457"
          x1="26.526"
          x2="26.164"
          y1="17.174"
          y2="12.071"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#F92814" stopOpacity="0"></stop>
          <stop offset="1" stopColor="#C1272D"></stop>
        </linearGradient>
        <linearGradient
          id="paint8_linear_4406_1457"
          x1="21.047"
          x2="25.719"
          y1="12.552"
          y2="18.629"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#F9F7FC"></stop>
          <stop offset="1" stopColor="#F0DDFC"></stop>
        </linearGradient>
        <linearGradient
          id="paint9_linear_4406_1457"
          x1="27.787"
          x2="33.208"
          y1="12.244"
          y2="19.296"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#F9F7FC"></stop>
          <stop offset="1" stopColor="#F0DDFC"></stop>
        </linearGradient>
        <linearGradient
          id="paint10_linear_4406_1457"
          x1="28.294"
          x2="19.537"
          y1="13.717"
          y2="7.353"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#18CFFC" stopOpacity="0"></stop>
          <stop offset="1" stopColor="#65E1DC"></stop>
        </linearGradient>
        <linearGradient
          id="paint11_linear_4406_1457"
          x1="26.142"
          x2="26.142"
          y1="19.543"
          y2="12.128"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#F92814" stopOpacity="0"></stop>
          <stop offset="1" stopColor="#C1272D"></stop>
        </linearGradient>
        <linearGradient
          id="paint12_linear_4406_1457"
          x1="22.603"
          x2="22.603"
          y1="17.251"
          y2="11.782"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#F0DDFC" stopOpacity="0"></stop>
          <stop offset="0.289" stopColor="#C8B7E0" stopOpacity="0.289"></stop>
          <stop offset="0.592" stopColor="#A595C8" stopOpacity="0.592"></stop>
          <stop offset="0.84" stopColor="#8F81B8" stopOpacity="0.84"></stop>
          <stop offset="1" stopColor="#8779B3"></stop>
        </linearGradient>
        <linearGradient
          id="paint13_linear_4406_1457"
          x1="29.681"
          x2="29.681"
          y1="17.25"
          y2="11.781"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#F0DDFC" stopOpacity="0"></stop>
          <stop offset="0.289" stopColor="#C8B7E0" stopOpacity="0.289"></stop>
          <stop offset="0.592" stopColor="#A595C8" stopOpacity="0.592"></stop>
          <stop offset="0.84" stopColor="#8F81B8" stopOpacity="0.84"></stop>
          <stop offset="1" stopColor="#8779B3"></stop>
        </linearGradient>
        <linearGradient
          id="paint16_linear_4406_1457"
          x1="22.756"
          x2="32.683"
          y1="3.914"
          y2="13.841"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#FFDA45"></stop>
          <stop offset="1" stopColor="#FFA425"></stop>
        </linearGradient>
        <linearGradient
          id="paint17_linear_4406_1457"
          x1="34.601"
          x2="18.776"
          y1="15.237"
          y2="1.647"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#FFDA45" stopOpacity="0"></stop>
          <stop offset="0.519" stopColor="#D9874F" stopOpacity="0.519"></stop>
          <stop offset="1" stopColor="#B53759"></stop>
        </linearGradient>
        <linearGradient
          id="paint18_linear_4406_1457"
          x1="24.257"
          x2="31.605"
          y1="5.666"
          y2="13.279"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#FFDA45"></stop>
          <stop offset="1" stopColor="#FCB37E"></stop>
        </linearGradient>
        <linearGradient
          id="paint19_linear_4406_1457"
          x1="23.132"
          x2="27.702"
          y1="6.066"
          y2="10.941"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#FCB37E" stopOpacity="0"></stop>
          <stop offset="1" stopColor="#FF7044"></stop>
        </linearGradient>
        <linearGradient
          id="paint20_linear_4406_1457"
          x1="23.007"
          x2="26.138"
          y1="9.363"
          y2="9.363"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#FFDA45"></stop>
          <stop offset="1" stopColor="#FCB37E"></stop>
        </linearGradient>
        <linearGradient
          id="paint21_linear_4406_1457"
          x1="25.396"
          x2="30.745"
          y1="7.88"
          y2="13.906"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#FCB37E" stopOpacity="0"></stop>
          <stop offset="1" stopColor="#FF7044"></stop>
        </linearGradient>
        <linearGradient
          id="paint22_linear_4406_1457"
          x1="22.331"
          x2="29.204"
          y1="9.48"
          y2="9.243"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#FCB37E" stopOpacity="0"></stop>
          <stop offset="1" stopColor="#FF7044"></stop>
        </linearGradient>
        <linearGradient
          id="paint23_linear_4406_1457"
          x1="28.475"
          x2="26.714"
          y1="12.25"
          y2="4.43"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#FFDA45"></stop>
          <stop offset="1" stopColor="#FCB37E"></stop>
        </linearGradient>
        <linearGradient
          id="paint24_linear_4406_1457"
          x1="23.494"
          x2="24.543"
          y1="5.56"
          y2="10.097"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#FFDA45"></stop>
          <stop offset="1" stopColor="#FCB37E"></stop>
        </linearGradient>
        <linearGradient
          id="paint25_linear_4406_1457"
          x1="27.963"
          x2="21.463"
          y1="5.499"
          y2="2.926"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#FCB37E" stopOpacity="0"></stop>
          <stop offset="1" stopColor="#FF7044"></stop>
        </linearGradient>
        <linearGradient
          id="paint26_linear_4406_1457"
          x1="25.141"
          x2="28.492"
          y1="3.365"
          y2="9.087"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#FCB37E" stopOpacity="0"></stop>
          <stop offset="1" stopColor="#FF7044"></stop>
        </linearGradient>
        <linearGradient
          id="paint27_linear_4406_1457"
          x1="29.299"
          x2="23.069"
          y1="5.527"
          y2="11.046"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#FCB37E" stopOpacity="0"></stop>
          <stop offset="1" stopColor="#FF7044"></stop>
        </linearGradient>
        <linearGradient
          id="paint28_linear_4406_1457"
          x1="28.179"
          x2="30.345"
          y1="7.77"
          y2="1.541"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#FCB37E" stopOpacity="0"></stop>
          <stop offset="1" stopColor="#FF7044"></stop>
        </linearGradient>
        <linearGradient
          id="paint29_linear_4406_1457"
          x1="26.269"
          x2="24.164"
          y1="7.386"
          y2="4.345"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#FFDA45" stopOpacity="0"></stop>
          <stop offset="1" stopColor="#FFEE83"></stop>
        </linearGradient>
        <radialGradient
          id="paint14_radial_4406_1457"
          cx="0"
          cy="0"
          r="1"
          gradientTransform="translate(24.3 3.054)scale(12.3679)"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#FFDA45"></stop>
          <stop offset="0.509" stopColor="#FFD844"></stop>
          <stop offset="0.692" stopColor="#FFD140"></stop>
          <stop offset="0.823" stopColor="#FFC639"></stop>
          <stop offset="0.928" stopColor="#FFB52F"></stop>
          <stop offset="1" stopColor="#FFA425"></stop>
        </radialGradient>
        <radialGradient
          id="paint15_radial_4406_1457"
          cx="0"
          cy="0"
          r="1"
          gradientTransform="rotate(180 13.902 5.565)scale(11.1626)"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#FFDA45"></stop>
          <stop offset="0.509" stopColor="#FFD844"></stop>
          <stop offset="0.692" stopColor="#FFD140"></stop>
          <stop offset="0.823" stopColor="#FFC639"></stop>
          <stop offset="0.928" stopColor="#FFB52F"></stop>
          <stop offset="1" stopColor="#FFA425"></stop>
        </radialGradient>
        <clipPath id="clip0_4406_1457">
          <path fill="#fff" d="M0 0h36v36H0z"></path>
        </clipPath>
      </defs>
    </svg>
  );
};

export const Trophy: React.FC = () => {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="36" height="36" fill="none" viewBox="0 0 36 36">
      <path
        fill="url(#paint0_radial_4406_1510)"
        d="M30.944 18.254a2 2 0 0 1-.16.043l-.09.018a1.58 1.58 0 0 1-1.194-.221 1.58 1.58 0 0 1-.688-1.002 2.11 2.11 0 0 1 .95-2.227l3.883-2.41a2.16 2.16 0 0 0 .97-2.275l-.128-.598a2.154 2.154 0 0 0-2.55-1.659 2.14 2.14 0 0 0-1.358.932 2.14 2.14 0 0 0-.301 1.62l.105.493a.638.638 0 0 0 1.248-.264.67.67 0 0 1 1.31-.278 1.978 1.978 0 0 1-3.012 2.068 1.96 1.96 0 0 1-.856-1.248l-.105-.494a3.47 3.47 0 0 1 .488-2.626 3.47 3.47 0 0 1 2.203-1.512c1.882-.4 3.739.807 4.138 2.69l.127.598a3.5 3.5 0 0 1-1.573 3.69l-3.883 2.41a.77.77 0 0 0-.346.813.25.25 0 0 0 .294.191l.09-.019a.247.247 0 0 0 .191-.294.67.67 0 1 1 1.31-.278c.087.415.009.839-.222 1.194-.2.308-.495.533-.84.645"
      ></path>
      <path
        fill="url(#paint1_radial_4406_1510)"
        d="M5.055 18.254q.079.025.161.043l.09.018c.415.088.839.01 1.194-.221s.6-.587.687-1.002a2.11 2.11 0 0 0-.95-2.227l-3.882-2.41a2.16 2.16 0 0 1-.97-2.275l.127-.598a2.154 2.154 0 0 1 2.551-1.659c.563.12 1.045.45 1.358.932s.42 1.057.3 1.62l-.104.493a.639.639 0 0 1-1.248-.264.67.67 0 0 0-1.31-.278 1.978 1.978 0 0 0 3.011 2.068c.443-.288.747-.731.857-1.248l.104-.494a3.47 3.47 0 0 0-.488-2.626 3.47 3.47 0 0 0-2.202-1.512 3.494 3.494 0 0 0-4.139 2.69l-.127.598a3.5 3.5 0 0 0 1.574 3.69l3.883 2.41a.77.77 0 0 1 .346.813.247.247 0 0 1-.295.19l-.09-.018a.247.247 0 0 1-.19-.294.67.67 0 0 0-1.31-.278c-.088.415-.01.839.222 1.194.2.308.495.533.84.645"
      ></path>
      <path
        fill="url(#paint2_linear_4406_1510)"
        d="m28.969 10.752.104.494c.11.516.414.96.857 1.247a1.978 1.978 0 0 0 3.01-2.067.67.67 0 1 0-1.31.277.63.63 0 0 1-.088.48.639.639 0 0 1-1.159-.215l-.105-.494c-.099-.467-.041-.943.16-1.367l-.982-.982a3.47 3.47 0 0 0-.487 2.627"
      ></path>
      <path
        fill="url(#paint3_linear_4406_1510)"
        d="M31.785 17.609c.231-.356.31-.78.222-1.194a.67.67 0 0 0-1.31.277.247.247 0 0 1-.19.294l-.09.02a.247.247 0 0 1-.294-.192.77.77 0 0 1 .22-.712l-.947-.947a2.11 2.11 0 0 0-.583 1.937c.088.415.332.77.688 1.001s.779.31 1.193.222l.09-.019a1.576 1.576 0 0 0 1.001-.687"
      ></path>
      <path
        fill="url(#paint4_radial_4406_1510)"
        d="M26.436 31.276c0-1.186-3.777-2.149-8.436-2.149-4.66 0-8.437.963-8.437 2.15s3.778 2.148 8.437 2.148 8.436-.962 8.436-2.149"
      ></path>
      <path
        fill="url(#paint5_linear_4406_1510)"
        d="M24.939 32.499c-1.523.56-4.063.926-6.94.926q-.789 0-1.54-.035l-2.92-2.92a.4.4 0 0 1-.119-.14l-.008-.022-.006-.018a.2.2 0 0 1-.002-.09H13.4l.006-.004c.029-.125.18-.244.432-.352q.08-.034.16-.069.147-.065.293-.133.012-.007.024-.01.213-.102.422-.21.118-.06.235-.126l.055-.031h.002A31 31 0 0 1 18 29.128c1.046 0 2.047.048 2.972.137.385.217.784.405 1.19.58q.169.07.274.149l.001.003z"
      ></path>
      <path
        fill="url(#paint6_linear_4406_1510)"
        d="m29.33 7.59-.708-2.91H7.378l-.707 2.91a16.6 16.6 0 0 0-.215 6.82c.784 4.408 4.005 7.854 8.12 9.103a3.18 3.18 0 0 1 2.261 3.042c0 1.035-.554 1.99-1.451 2.505l-.298.171c-.404.231-.824.43-1.25.613-.252.107-.404.226-.433.352l-.005.003h.005a.2.2 0 0 0-.005.041c0 .514 2.06.93 4.6.93s4.6-.416 4.6-.93a.2.2 0 0 0-.005-.04h.006l-.006-.004c-.029-.126-.181-.245-.432-.352a12 12 0 0 1-1.25-.613l-.299-.17a2.89 2.89 0 0 1-1.451-2.506c0-1.403.922-2.635 2.265-3.042 4.096-1.244 7.311-4.664 8.106-9.049a16.6 16.6 0 0 0-.205-6.874"
      ></path>
      <path
        fill="url(#paint7_linear_4406_1510)"
        d="m29.33 7.59-.708-2.91H7.378l-.707 2.91a16.6 16.6 0 0 0-.215 6.82c.784 4.408 4.005 7.854 8.12 9.103.63.191 1.166.565 1.559 1.052l3.393 3.393a2.9 2.9 0 0 1-.365-1.402c0-1.404.922-2.636 2.265-3.043 4.096-1.244 7.311-4.664 8.106-9.049a16.6 16.6 0 0 0-.205-6.874"
      ></path>
      <path
        fill="url(#paint8_radial_4406_1510)"
        d="M29.8 11.51c0 .996-.09 1.984-.266 2.954-1.006 5.552-5.892 9.558-11.534 9.558-5.663 0-10.553-4.037-11.544-9.613a16.6 16.6 0 0 1 .215-6.819l.707-2.91h21.244l.707 2.91c.316 1.301.47 2.617.47 3.92"
      ></path>
      <path
        fill="url(#paint9_linear_4406_1510)"
        d="M28.621 4.68c0-1.187-4.755-2.149-10.621-2.149s-10.621.962-10.621 2.15c0 1.186 4.755 2.148 10.621 2.148s10.621-.962 10.621-2.149"
      ></path>
      <path
        fill="url(#paint10_linear_4406_1510)"
        d="M27.601 4.5c0-.886-4.298-1.604-9.601-1.604S8.398 3.614 8.398 4.5s4.3 1.604 9.602 1.604S27.6 5.386 27.6 4.5"
      ></path>
      <path
        fill="#FFB211"
        d="M15.429 12.245q0-.461.372-.727l1.843-1.772a.65.65 0 0 1 .479-.195q.318 0 .575.168a.52.52 0 0 1 .258.452v8.795q0 .284-.284.452a1.26 1.26 0 0 1-.656.168q-.39 0-.664-.168-.275-.168-.275-.452V12.05l-.62.78a.54.54 0 0 1-.39.177.56.56 0 0 1-.453-.24.83.83 0 0 1-.186-.522"
      ></path>
      <defs>
        <linearGradient
          id="paint2_linear_4406_1510"
          x1="31.543"
          x2="27.627"
          y1="10.62"
          y2="10.189"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#FFB211" stopOpacity="0"></stop>
          <stop offset="0.228" stopColor="#FF9F19" stopOpacity="0.227"></stop>
          <stop offset="0.686" stopColor="#FF6F2E" stopOpacity="0.686"></stop>
          <stop offset="1" stopColor="#FF4B3E"></stop>
        </linearGradient>
        <linearGradient
          id="paint3_linear_4406_1510"
          x1="30.88"
          x2="27.808"
          y1="16.868"
          y2="16.529"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#FFB211" stopOpacity="0"></stop>
          <stop offset="0.228" stopColor="#FF9F19" stopOpacity="0.227"></stop>
          <stop offset="0.686" stopColor="#FF6F2E" stopOpacity="0.686"></stop>
          <stop offset="1" stopColor="#FF4B3E"></stop>
        </linearGradient>
        <linearGradient
          id="paint5_linear_4406_1510"
          x1="19.334"
          x2="18.487"
          y1="32.42"
          y2="27.049"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#FFB211" stopOpacity="0"></stop>
          <stop offset="0.228" stopColor="#FF9F19" stopOpacity="0.227"></stop>
          <stop offset="0.686" stopColor="#FF6F2E" stopOpacity="0.686"></stop>
          <stop offset="1" stopColor="#FF4B3E"></stop>
        </linearGradient>
        <linearGradient
          id="paint6_linear_4406_1510"
          x1="17.359"
          x2="20.398"
          y1="17.926"
          y2="17.926"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#FFE548"></stop>
          <stop offset="1" stopColor="#FFB211"></stop>
        </linearGradient>
        <linearGradient
          id="paint7_linear_4406_1510"
          x1="21.82"
          x2="19.53"
          y1="26.26"
          y2="17.355"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#FFB211" stopOpacity="0"></stop>
          <stop offset="0.181" stopColor="#FF931F" stopOpacity="0.18"></stop>
          <stop offset="0.401" stopColor="#FF732C" stopOpacity="0.4"></stop>
          <stop offset="0.615" stopColor="#FF5D36" stopOpacity="0.616"></stop>
          <stop offset="0.818" stopColor="#FF503C" stopOpacity="0.82"></stop>
          <stop offset="1" stopColor="#FF4B3E"></stop>
        </linearGradient>
        <linearGradient
          id="paint9_linear_4406_1510"
          x1="12.341"
          x2="24.151"
          y1="1.098"
          y2="8.574"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#FFE548"></stop>
          <stop offset="1" stopColor="#FFB211"></stop>
        </linearGradient>
        <linearGradient
          id="paint10_linear_4406_1510"
          x1="18"
          x2="18"
          y1="8.571"
          y2="0.173"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#FFB211" stopOpacity="0"></stop>
          <stop offset="0.228" stopColor="#FF9F19" stopOpacity="0.227"></stop>
          <stop offset="0.686" stopColor="#FF6F2E" stopOpacity="0.686"></stop>
          <stop offset="1" stopColor="#FF4B3E"></stop>
        </linearGradient>
        <radialGradient
          id="paint0_radial_4406_1510"
          cx="0"
          cy="0"
          r="1"
          gradientTransform="rotate(-17.879 47.777 -84.777)scale(7.55511)"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#FFE548"></stop>
          <stop offset="0.431" stopColor="#FFE346"></stop>
          <stop offset="0.636" stopColor="#FFDC3E"></stop>
          <stop offset="0.793" stopColor="#FFD031"></stop>
          <stop offset="0.926" stopColor="#FFBF1F"></stop>
          <stop offset="1" stopColor="#FFB211"></stop>
        </radialGradient>
        <radialGradient
          id="paint1_radial_4406_1510"
          cx="0"
          cy="0"
          r="1"
          gradientTransform="rotate(-162.121 4.664 4.684)scale(7.55511)"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#FFE548"></stop>
          <stop offset="0.431" stopColor="#FFE346"></stop>
          <stop offset="0.636" stopColor="#FFDC3E"></stop>
          <stop offset="0.793" stopColor="#FFD031"></stop>
          <stop offset="0.926" stopColor="#FFBF1F"></stop>
          <stop offset="1" stopColor="#FFB211"></stop>
        </radialGradient>
        <radialGradient
          id="paint4_radial_4406_1510"
          cx="0"
          cy="0"
          r="1"
          gradientTransform="matrix(10.264 0 0 4.53773 17.069 28.894)"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#FFE548"></stop>
          <stop offset="0.441" stopColor="#FFE346"></stop>
          <stop offset="0.643" stopColor="#FFDC3E"></stop>
          <stop offset="0.797" stopColor="#FFD032"></stop>
          <stop offset="0.925" stopColor="#FFC020"></stop>
          <stop offset="1" stopColor="#FFB211"></stop>
        </radialGradient>
        <radialGradient
          id="paint8_radial_4406_1510"
          cx="0"
          cy="0"
          r="1"
          gradientTransform="translate(10.555 6.576)scale(21.0096)"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#FFE548"></stop>
          <stop offset="0.431" stopColor="#FFE346"></stop>
          <stop offset="0.636" stopColor="#FFDC3E"></stop>
          <stop offset="0.793" stopColor="#FFD031"></stop>
          <stop offset="0.926" stopColor="#FFBF1F"></stop>
          <stop offset="1" stopColor="#FFB211"></stop>
        </radialGradient>
      </defs>
    </svg>
  );
};

export const MapPin: React.FC = () => {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
      <path
        fill="#EC761E"
        d="M12 6a3.75 3.75 0 1 0 0 7.5A3.75 3.75 0 0 0 12 6m0 6a2.25 2.25 0 1 1 0-4.5 2.25 2.25 0 0 1 0 4.5m0-10.5a8.26 8.26 0 0 0-8.25 8.25c0 2.944 1.36 6.064 3.938 9.023a23.8 23.8 0 0 0 3.885 3.591.75.75 0 0 0 .861 0 23.8 23.8 0 0 0 3.879-3.59c2.573-2.96 3.937-6.08 3.937-9.024A8.26 8.26 0 0 0 12 1.5m0 19.313c-1.55-1.22-6.75-5.696-6.75-11.063a6.75 6.75 0 0 1 13.5 0c0 5.365-5.2 9.844-6.75 11.063"
      ></path>
    </svg>
  );
};

export const EnvelopeSimpleIcon: React.FC = () => {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
      <path
        fill="#EC761E"
        d="M21 4.5H3a.75.75 0 0 0-.75.75V18a1.5 1.5 0 0 0 1.5 1.5h16.5a1.5 1.5 0 0 0 1.5-1.5V5.25A.75.75 0 0 0 21 4.5M19.072 6 12 12.483 4.928 6zm1.178 12H3.75V6.955l7.743 7.098a.75.75 0 0 0 1.014 0l7.743-7.098z"
      ></path>
    </svg>
  );
};

export const LinkdinIcon: React.FC = () => {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
      <path
        fill="currentColor"
        d="M18.336 18.339h-2.665v-4.177c0-.996-.02-2.278-1.39-2.278-1.389 0-1.601 1.084-1.601 2.205v4.25h-2.666V9.75h2.56v1.17h.035c.358-.674 1.228-1.387 2.528-1.387 2.7 0 3.2 1.778 3.2 4.092zM7.004 8.575a1.546 1.546 0 0 1-1.548-1.549 1.548 1.548 0 1 1 1.547 1.549m1.336 9.764H5.667V9.75H8.34zM19.67 3H4.33C3.594 3 3 3.58 3 4.297v15.406c0 .717.594 1.297 1.328 1.297h15.339C20.4 21 21 20.42 21 19.703V4.297c0-.716-.6-1.297-1.334-1.297z"
      ></path>
    </svg>
  );
};

export const InstagramIcon: React.FC = () => {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
      <path
        fill="currentColor"
        d="M13.028 2c1.125.002 1.696.008 2.189.023l.194.006c.224.008.445.018.712.03 1.064.05 1.79.218 2.427.466.66.254 1.216.597 1.772 1.153a4.9 4.9 0 0 1 1.153 1.772c.247.636.415 1.363.465 2.427.012.267.022.488.03.712l.006.194c.015.493.021 1.064.023 2.19l.001.745v1.31a79 79 0 0 1-.023 2.188l-.006.194c-.008.224-.018.446-.03.712-.05 1.064-.22 1.79-.466 2.428a4.9 4.9 0 0 1-1.153 1.771 4.9 4.9 0 0 1-1.772 1.154c-.637.246-1.363.415-2.427.465l-.712.03-.194.006c-.493.014-1.064.02-2.189.023h-2.055a78 78 0 0 1-2.189-.022l-.194-.007a63 63 0 0 1-.712-.03c-1.064-.05-1.79-.219-2.428-.465A4.9 4.9 0 0 1 3.68 20.32a4.9 4.9 0 0 1-1.154-1.771c-.247-.638-.415-1.364-.465-2.428l-.03-.712-.005-.194A79 79 0 0 1 2 13.028v-2.056c.002-1.125.008-1.696.022-2.189l.007-.194c.008-.224.018-.445.03-.712.05-1.065.218-1.79.465-2.427A4.9 4.9 0 0 1 3.68 3.678 4.9 4.9 0 0 1 5.45 2.525c.638-.248 1.363-.415 2.428-.465.266-.012.488-.022.712-.03l.194-.006a79 79 0 0 1 2.188-.023zM12 7a5 5 0 1 0 0 10 5 5 0 0 0 0-10m0 2a3 3 0 1 1 0 6 3 3 0 0 1 0-6m5.25-3.5a1.25 1.25 0 0 0 0 2.499 1.25 1.25 0 0 0 0-2.5"
      ></path>
    </svg>
  );
};

export const XIcon: React.FC = () => {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
      <path
        fill="currentColor"
        d="M10.488 14.651 15.25 21h7l-7.858-10.478L20.93 3h-2.65l-5.117 5.886L8.75 3h-7l7.51 10.015L2.32 21h2.65zM16.25 19 5.75 5h2l10.5 14z"
      ></path>
    </svg>
  );
};
export const UsersThreeIcon: React.FC = () => {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
      <path
        fill="#EC761E"
        d="M22.95 14.1a.75.75 0 0 1-1.05-.15A4.84 4.84 0 0 0 18 12a.75.75 0 1 1 0-1.5 2.25 2.25 0 1 0-2.179-2.812.75.75 0 1 1-1.453-.375 3.75 3.75 0 1 1 6.163 3.704 6.37 6.37 0 0 1 2.572 2.032.75.75 0 0 1-.153 1.051m-5.052 5.775a.75.75 0 1 1-1.297.75 5.344 5.344 0 0 0-9.202 0 .751.751 0 1 1-1.298-.75 6.76 6.76 0 0 1 3.163-2.805 4.5 4.5 0 1 1 5.471 0 6.76 6.76 0 0 1 3.163 2.805M12 16.5a3 3 0 1 0 0-6 3 3 0 0 0 0 6m-5.25-5.25A.75.75 0 0 0 6 10.5a2.25 2.25 0 1 1 2.179-2.812.75.75 0 1 0 1.453-.375 3.75 3.75 0 1 0-6.163 3.704 6.37 6.37 0 0 0-2.57 2.032.75.75 0 0 0 1.2.901A4.84 4.84 0 0 1 6 12a.75.75 0 0 0 .75-.75"
      ></path>
    </svg>
  );
};
export const BellIconNotification: React.FC = () => {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
      <path
        fill="#fff"
        d="M21 6.666a.75.75 0 0 1-1.01-.32 8.8 8.8 0 0 0-3.137-3.46.751.751 0 0 1 .8-1.27 10.45 10.45 0 0 1 3.668 4.04.75.75 0 0 1-.321 1.01M3.348 6.75a.75.75 0 0 0 .665-.405 8.8 8.8 0 0 1 3.137-3.46.75.75 0 0 0-.8-1.27 10.45 10.45 0 0 0-3.668 4.04.75.75 0 0 0 .666 1.095m17.447 9.745A1.5 1.5 0 0 1 19.5 18.75h-3.825a3.75 3.75 0 0 1-7.35 0H4.5a1.5 1.5 0 0 1-1.293-2.255C4.052 15.037 4.5 12.965 4.5 10.5a7.5 7.5 0 1 1 15 0c0 2.463.448 4.536 1.295 5.995M14.12 18.75H9.879a2.25 2.25 0 0 0 4.242 0"
      ></path>
    </svg>
  );
};
export const MessageIcon: React.FC = () => {
  return (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M21.75 9C21.75 8.60218 21.592 8.22064 21.3107 7.93934C21.0294 7.65804 20.6478 7.5 20.25 7.5H17.25V4.5C17.25 4.10218 17.092 3.72064 16.8107 3.43934C16.5294 3.15804 16.1478 3 15.75 3H3.75C3.35218 3 2.97064 3.15804 2.68934 3.43934C2.40804 3.72064 2.25 4.10218 2.25 4.5V16.5C2.25044 16.6411 2.29068 16.7792 2.36608 16.8985C2.44149 17.0177 2.54901 17.1133 2.67629 17.1742C2.80358 17.2351 2.94546 17.2589 3.08564 17.2428C3.22581 17.2266 3.3586 17.1713 3.46875 17.0831L6.75 14.4375V17.25C6.75 17.6478 6.90804 18.0294 7.18934 18.3107C7.47064 18.592 7.85218 18.75 8.25 18.75H17.0241L20.5312 21.5831C20.664 21.6905 20.8293 21.7493 21 21.75C21.1989 21.75 21.3897 21.671 21.5303 21.5303C21.671 21.3897 21.75 21.1989 21.75 21V9ZM17.7609 17.4169C17.6282 17.3095 17.4629 17.2507 17.2922 17.25H8.25V14.25H15.75C16.1478 14.25 16.5294 14.092 16.8107 13.8107C17.092 13.5294 17.25 13.1478 17.25 12.75V9H20.25V19.4297L17.7609 17.4169Z"
        fill="white"
      />
    </svg>
  );
};
