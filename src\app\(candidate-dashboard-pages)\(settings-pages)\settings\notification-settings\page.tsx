"use client";

import React, { useEffect, useState } from "react";
import { toast } from "sonner";
import SwitchButton from "./SwitchButton";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  B<PERSON><PERSON><PERSON>bList,
  B<PERSON><PERSON><PERSON>bP<PERSON>,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { useUpdateJobSeekerProfile } from "@/hooks/useMutation";
import { useGetJobSeekerProfile } from "@/hooks/useQuery";

interface NotificationSettings {
  desktopNotification: boolean;
  emailNotification: boolean;
  jobAlerts: boolean;
  applicationStatusUpdates: boolean;
  announcementsAndUpdates: boolean;
}

export default function NotificationsSettingsPage() {
  const { data: jobSeekerData, isLoading, isError } = useGetJobSeekerProfile();
  const jobSeekerProfile = jobSeekerData?.data;

  const [notificationSettings, setNotificationSettings] = useState<NotificationSettings>({
    desktopNotification: false,
    emailNotification: false,
    jobAlerts: false,
    applicationStatusUpdates: false,
    announcementsAndUpdates: false,
  });

  const { mutate: updateNotificationSettings, isPending } = useUpdateJobSeekerProfile({
    onSuccess: () => {
      toast.success("Notification settings updated successfully");
    },
    onError: (error) => {
      toast.error(error.response?.data?.message || "Failed to update notification settings");
    },
  });

  // Sync notification settings with API response
  useEffect(() => {
    if (jobSeekerProfile?.notificationSettings) {
      setNotificationSettings(jobSeekerProfile.notificationSettings);
    }
  }, [jobSeekerProfile]);

  const handleToggle = (key: keyof NotificationSettings) => {
    const updatedSettings = {
      ...notificationSettings,
      [key]: !notificationSettings[key],
    };
    setNotificationSettings(updatedSettings);

    // Update the notification settings via API
    updateNotificationSettings({ notificationSettings: updatedSettings });
  };

  if (isLoading) {
    return (
      <div className="text-center py-8">
        <div className="animate-spin rounded-full h-10 w-10 border-b-2 border-gray-900 mx-auto"></div>
        <p className="text-gray-500 mt-4">Loading notification settings...</p>
      </div>
    );
  }

  if (isError) {
    return (
      <div className="text-center py-8">
        <p className="text-red-500">
          Failed to load notification settings. Please try again later.
        </p>
      </div>
    );
  }

  return (
    <>
      <Breadcrumb className="mb-8">
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink href="/dashboard/applied-jobs">Dashboard</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>Notification Settings</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>

      <h2 className="text-orange-100 text-3xl font-bold mb-10">Notification Settings</h2>
      <div className="mb-10 border border-gray-300 p-6 rounded-2xl">
        {[
          {
            key: "desktopNotification",
            title: "Desktop Notification",
            description:
              "Get real-time notifications on YesJobs whenever a new job matches your profile or a recruiter views your application. Stay updated on interview invites, job offers, and important career opportunities—never miss a chance to advance your career!",
          },
          {
            key: "emailNotification",
            title: "Email Notifications",
            description:
              "Receive personalized job recommendations, recruiter messages, and interview invites directly in your inbox. Enable email notifications to stay informed about application updates and hiring decisions anytime, anywhere.",
          },
          {
            key: "jobAlerts",
            title: "Job Alerts",
            description:
              "Get notified instantly when a job matching your skills and preferences is posted. Never miss out on new opportunities!",
          },
          {
            key: "applicationStatusUpdates",
            title: "Application Status Updates",
            description:
              "Track your job applications in real time. Receive updates when recruiters view, shortlist, or respond to your application.",
          },
          {
            key: "announcementsAndUpdates",
            title: "Announcements & Updates",
            description:
              "Receive important updates about new YesJobs features, security improvements, and exclusive offers to enhance your experience.",
          },
        ].map((setting) => (
          <div
            key={setting.key}
            className="sm:flex justify-between items-center pb-4 border-b border-gray-300 pt-6"
          >
            <div className="mb-4 sm:mb-0">
              <h4 className="text-black-100 text-lg font-bold mb-2">{setting.title}</h4>
              <p className="text-gray-100 font-sm font-normal leading-6">{setting.description}</p>
            </div>
            <div className="w-[40%] sm:ml-auto sm:text-end">
              <SwitchButton
                isChecked={notificationSettings[setting.key as keyof NotificationSettings]}
                onChange={() => handleToggle(setting.key as keyof NotificationSettings)}
                disabled={isPending}
              />
            </div>
          </div>
        ))}
      </div>
    </>
  );
}
