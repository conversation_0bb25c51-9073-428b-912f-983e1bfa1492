"use client";

import Image from "next/image";
import React from "react";

interface FullPageLoaderProps {
  message?: string;
}

const FullPageLoader: React.FC<FullPageLoaderProps> = ({ message = "Please wait..." }) => {
  return (
    <div className="fixed inset-0 z-50 flex flex-col items-center justify-center bg-white bg-opacity-90">
      <div className="flex flex-col items-center justify-center space-y-6">
        {/* Logo */}
        <div className="relative w-32 h-12 mb-4">
          <Image
            src="/images/logo.png"
            alt="Yes Jobs Logo"
            fill
            className="object-contain"
            priority
          />
        </div>

        {/* Spinner */}
        <div className="relative">
          <div className="w-16 h-16 border-4 border-orange-100 rounded-full"></div>
          <div className="absolute top-0 w-16 h-16 border-4 border-blue-100 rounded-full border-t-transparent animate-spin"></div>
        </div>

        {/* Message */}
        <p className="text-lg font-medium text-gray-700">{message}</p>
      </div>
    </div>
  );
};

export default FullPageLoader;
