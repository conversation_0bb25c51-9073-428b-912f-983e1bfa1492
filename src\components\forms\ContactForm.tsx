"use client";

import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import Link from "next/link";
import { useForm, type SubmitHandler } from "react-hook-form";
import { toast } from "sonner";
import * as z from "zod";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { useContactForm } from "@/hooks/useMutation";
import { IContactFormRequestDto } from "@/types/mutation.types";

// Define the form schema with zod
const formSchema = z.object({
  name: z.string().min(2, { message: "Name must be at least 2 characters" }),
  email: z.string().email({ message: "Invalid email address" }),
  subject: z.string().min(3, { message: "Subject must be at least 3 characters" }),
  message: z.string().min(10, { message: "Message must be at least 10 characters" }),
  acceptTerms: z.boolean().refine((val) => val === true, {
    message: "You must accept the terms",
  }),
});

type FormInputs = z.infer<typeof formSchema>;

const inputStyles = "h-12 px-4 rounded-full border border-gray-100";

export default function ContactForm() {
  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm<FormInputs>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: "",
      email: "",
      subject: "",
      message: "",
      acceptTerms: true,
    },
  });

  // Use the contact form mutation hook
  const { mutate: submitContactForm, isPending: isLoading } = useContactForm({
    onSuccess: () => {
      // Reset the form on successful submission
      toast.success("Your message has been sent successfully!");
      reset();
    },
  });

  const onSubmit: SubmitHandler<FormInputs> = (data) => {
    // Extract the form data (excluding acceptTerms which is not needed for the API)

    const { ...contactFormData } = data;

    // Submit the form data to the API
    submitContactForm(contactFormData as IContactFormRequestDto);
  };

  return (
    <div className="w-full">
      <form onSubmit={handleSubmit(onSubmit)}>
        <div>
          <div className="mb-5 space-y-2">
            <Label htmlFor="name" className="text-black-100">
              Name
            </Label>
            <Input
              id="name"
              className={inputStyles}
              placeholder="Enter your name"
              {...register("name", { required: "Name is required" })}
            />
            {errors.name && <p className="text-red-500 text-sm">{errors.name.message}</p>}
          </div>
          <div className="mb-5 space-y-2">
            <Label htmlFor="email">Email</Label>
            <Input
              id="email"
              type="email"
              className={inputStyles}
              placeholder="Enter your email"
              {...register("email", {
                required: "Email is required",
                pattern: {
                  value: /\S+@\S+\.\S+/,
                  message: "Invalid email address",
                },
              })}
            />
            {errors.email && <p className="text-red-500 text-sm">{errors.email.message}</p>}
          </div>
          <div className="mb-5 space-y-2">
            <Label htmlFor="subject">Subject</Label>
            <Input
              id="subject"
              className={inputStyles}
              placeholder="Enter the subject"
              {...register("subject", { required: "Subject is required" })}
            />
            {errors.subject && <p className="text-red-500 text-sm">{errors.subject.message}</p>}
          </div>
          <div className="space-y-2 mb-5">
            <Label htmlFor="message">Message</Label>
            <Textarea
              id="message"
              className="min-h-[100px] px-4 py-2 rounded-3xl border border-[#737373]"
              placeholder="Enter your message"
              {...register("message", { required: "Message is required" })}
            />
            {errors.message && <p className="text-red-500 text-sm">{errors.message.message}</p>}
          </div>
          <div className="flex items-center space-x-2">
            <Checkbox
              id="terms"
              className="border-orange-100"
              {...register("acceptTerms", {
                required: "You must accept the terms",
              })}
            />
            <Label
              htmlFor="terms"
              className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
            >
              I accept the{" "}
              <Link href={"/terms-and-conditions"} className="text-orange-100">
                Terms and conditions
              </Link>
            </Label>
          </div>
          {errors.acceptTerms && (
            <p className="text-red-500 text-sm">{errors.acceptTerms.message}</p>
          )}
        </div>
        <div className="mt-8">
          <Button
            type="submit"
            className="text-base h-[48px] px-10 rounded-full bg-orange-100 text-white"
            disabled={isLoading}
          >
            {isLoading ? "Sending..." : "Send Message"}
          </Button>
        </div>
      </form>
    </div>
  );
}
