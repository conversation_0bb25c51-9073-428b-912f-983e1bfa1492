"use client";

import Image from "next/image";
import { PrimaryHeading } from "@/components/Headings/PrimaryHeading";
import CTASection from "@/components/Sections/CTASection";
import MobileAppSection from "@/components/Sections/MobileAppSection";
import SapportCompaniesSection from "@/components/Sections/SapportCompaniesSection";
import { useGetAboutUsPage } from "@/hooks/useQuery";

const AboutUsPageContent = () => {
  const { data: aboutUsPageData, isLoading, isError } = useGetAboutUsPage();
  const ourMission = aboutUsPageData?.data.page.ourMission;
  const aboutUs = aboutUsPageData?.data.page.aboutUs;
  const ourVision = aboutUsPageData?.data.page.ourVision;
  const ourValues = aboutUsPageData?.data.page.ourValues;

  if (isLoading) {
    return (
      <div className="container mx-auto py-20 text-center">
        <div className="animate-pulse space-y-4">
          <div className="h-10 bg-gray-200 rounded w-1/2 mx-auto" />
          <div className="h-8 bg-gray-200 rounded w-2/3 mx-auto" />
          <div className="h-[400px] bg-gray-200 rounded-lg mt-10" />
        </div>
      </div>
    );
  }

  if (isError) {
    return (
      <div className="container mx-auto py-20 text-center text-red-500">
        Failed to load About Us content. Please try again later.
      </div>
    );
  }

  return (
    <>
      <section className="lg:py-20 py-14">
        <div className="container mx-auto text-center">
          <h2 className="text-orange-100">{ourMission?.heading}</h2>
          <h1 className="font-bold lg:text-5xl text-3xl leading-tight text-blue-100 mt-5 mx-auto">
            {ourMission?.description}
          </h1>
        </div>
      </section>
      <section>
        <div className="container mx-auto">
          <div className="grid lg:grid-cols-2 grid-cols-1 mb-14">
            <PrimaryHeading>{aboutUs?.heading}</PrimaryHeading>
            <p className="font-medium text-lg leading-7 mt-3 lg:mt-0">{aboutUs?.description}</p>
          </div>
          <Image
            src={aboutUs?.image || "/images/about-us-main.png"}
            alt=""
            width={1520}
            height={738}
            className="rounded-3xl"
          />
        </div>
      </section>
      <section className="lg:py-20 py-14">
        <div className="container mx-auto text-center">
          <h3 className="text-black-100"> {ourVision?.heading} </h3>
          <h2 className="font-bold lg:text-5xl text-3xl leading-tight text-orange-100 mt-5   mx-auto">
            {ourVision?.description}
          </h2>
        </div>
      </section>
      <section className="bg-offWhite-100 lg:py-20 py-14 mb-14 lg:mb-0">
        <div className="container mx-auto text-center">
          <PrimaryHeading>{ourValues?.heading}</PrimaryHeading>
          <p className="font-medium text-lg leading-7 mt-5 text-gray-100">
            {ourValues?.description}
          </p>
          <div className="grid lg:grid-cols-3 grid-cols-1 gap-10 mt-14">
            {ourValues?.values.map((value) => (
              <div key={value._id}>
                <Image
                  src={value.iconImage}
                  alt=""
                  width={50}
                  height={50}
                  className="rounded-full w-[50px] h-[50px] mx-auto"
                />
                <h4 className="uppercase text-3xl font-bold mt-8 mb-5">{value.name}</h4>
                <p className="text-gray-100">{value.description}</p>
              </div>
            ))}
            {/* <div>
              <Image
                src={"/images/integrity123.png"}
                alt=""
                width={50}
                height={50}
                className="rounded-full w-[50px] h-[50px] mx-auto"
              />
              <h4 className="uppercase text-3xl font-bold mt-8 mb-5">Integrity</h4>
              <p className="text-gray-100">
                Building trust through transparency and accountability.
              </p>
            </div>
            <div>
              <Image
                src={"/images/Innovation.png"}
                alt=""
                width={50}
                height={50}
                className="rounded-full w-[50px] h-[50px] mx-auto"
              />
              <h4 className="uppercase text-3xl font-bold mt-8 mb-5">Innovation</h4>
              <p className="text-gray-100">
                Pioneering solutions to simplify recruitment and job searches.
              </p>
            </div>
            <div>
              <Image
                src={"/images/Inclusivity.png"}
                alt=""
                width={50}
                height={50}
                className="rounded-full w-[50px] h-[50px] mx-auto"
              />
              <h4 className="uppercase text-3xl font-bold mt-8 mb-5">Inclusivity</h4>
              <p className="text-gray-100">
                Creating opportunities for everyone, regardless of background or experience.
              </p>
            </div> */}
          </div>
        </div>
      </section>
      <SapportCompaniesSection />
      <CTASection />
      <MobileAppSection />
    </>
  );
};

export default AboutUsPageContent;
