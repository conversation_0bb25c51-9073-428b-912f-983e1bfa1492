"use client";
import { Suspense } from "react";
import MultiStepper from "@/components/MultiStepper";
import Sidebar from "@/components/Sections/Sidebar";
import AchievementsForms from "@/components/forms/AchievementsForms";
import Certifications from "@/components/forms/CertificationsForms";
import CompleteYourCV from "@/components/forms/CompleteYourCV";
import EducationalDetails from "@/components/forms/EducationalDetails";
import JobPreferences from "@/components/forms/JobPreferences";
import ProfessionalExperience from "@/components/forms/ProfessionalExperience";
import ProfessionalSkills from "@/components/forms/ProfessionalSkills";
import TellUsAboutYourself from "@/components/forms/TellUsAboutYourself";
import { useGetCurrentUser } from "@/hooks/useQuery";
import { useUserStore } from "@/store/useUserStore";
import { CurrentJobSeekerType } from "@/types/query.types";

const steps = [
  {
    id: "complete-cv",
    title: "Complete Your CV",
    component: CompleteYourCV,
  },
  {
    id: "about-yourself",
    title: "About Yourself",
    component: TellUsAboutYourself,
  },
  {
    id: "educational-details",
    title: "Educational Details",
    component: EducationalDetails,
  },
  {
    id: "certifications",
    title: "Certifications",
    component: Certifications,
  },
  {
    id: "professional-experience",
    title: "Professional Experience",
    component: ProfessionalExperience,
  },
  {
    id: "professional-skills",
    title: "Professional Skills",
    component: ProfessionalSkills,
  },
  {
    id: "achievements",
    title: "Achievements",
    component: AchievementsForms,
  },
  {
    id: "job-preferences",
    title: "Job Preferences",
    component: JobPreferences,
  },
];

export default function ProfileCompletionPage() {
  const { currentUser } = useUserStore();
  const { data: userData } = useGetCurrentUser({
    enabled: !!currentUser,
  });

  const user = userData?.data as CurrentJobSeekerType | undefined;

  // useEffect(() => {
  //   if (!currentUser) {
  //     router.push("/login");
  //   }
  // }, [currentUser, router]);

  if (!user) {
    return null;
  }

  return (
    <Suspense>
      <div className="xl:p-6">
        <div className="lg:flex ">
          {/* Step Navigation */}
          <div className="xl:w-1/4 lg:w-[30%] mb-6 lg:mb-0">
            <Sidebar steps={steps} />
          </div>

          {/* Form Content */}
          <div className="xl:w-3/4 lg:w-[70%]">
            <div className="bg-white lg:p-6 rounded-lg ">
              <MultiStepper steps={steps} />
            </div>
          </div>
        </div>
      </div>
    </Suspense>
  );
}
