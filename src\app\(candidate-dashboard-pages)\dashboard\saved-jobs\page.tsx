"use client";

import dynamic from "next/dynamic";
import { Suspense } from "react";

// Import SavedJobsContent with dynamic import to handle useSearchParams
const SavedJobsContent = dynamic(() => import("./SavedJobsContent"), {
  ssr: false,
});

export default function SavedJobsPage() {
  return (
    <Suspense
      fallback={
        <div className="flex justify-center items-center min-h-[400px] py-20">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-orange-100"></div>
        </div>
      }
    >
      <SavedJobsContent />
    </Suspense>
  );
}
