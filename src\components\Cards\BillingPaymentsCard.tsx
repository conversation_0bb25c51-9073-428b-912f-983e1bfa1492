import React from "react";
import PricingsCard from "./PricingsCard";

const pricingDescription1 = [
  { text: "Profile highlighted in recruiter searches" },
  { text: "Priority placement in candidate search" },
  { text: "Increased visibility on job listings" },
];
const pricingDescription2 = [
  { text: "All Basic Plan features" },
  { text: "Featured badge on your profile" },
  { text: "Appear in recommended candidates list" },
];

const BillingPaymentsCard = () => {
  return (
    <div className="grid grid-cols-1 xl:grid-cols-2 items-start gap-4">
      <div>
        <h2 className="text-4xl font-bold text-black-100 mb-4">
          Get Noticed with a <span className="text-orange-100">Featured Profile!</span>
        </h2>
        <p className="text-sm font-normal leading-5 text-gray-100">
          Boost your visibility and increase your chances of getting hired by featuring your
          profile. Choose a plan that works for you!
        </p>
      </div>

      <div className="space-y-6">
        <PricingsCard
          pricingDescription={pricingDescription1}
          priceName="Free Plan"
          Price="9.99"
          duration="7 Days"
        />
        <PricingsCard
          pricingDescription={pricingDescription2}
          priceName="Standard"
          Price="19.99"
          duration="14 Days"
          highlight
        />
        <PricingsCard
          pricingDescription={pricingDescription2}
          priceName="Professional"
          Price="29.99 + GST"
          duration="30 Days"
        />

        {/* GST Note */}
        <p className="text-xs text-gray-500 mt-2 italic">
          * All transactions in Australia attract Goods and Services Tax (GST).
        </p>
      </div>
    </div>
  );
};

export default BillingPaymentsCard;
