import { useMutation, UseMutationOptions } from "@tanstack/react-query";
import { getToken } from "firebase/messaging";
import { useEffect } from "react";
import { getMessagingInstance } from "@/lib/firebase";
import { addFirebaseToken } from "@/service/mutation.service";
import { getFirebaseTokens } from "@/service/query.service";
import { ApiError } from "@/types/common.types";

export const registerServiceWorker = async (): Promise<ServiceWorkerRegistration | null> => {
  if (!("serviceWorker" in navigator)) throw new Error("Service Worker not supported");

  try {
    const registration = await navigator.serviceWorker.register("/firebase-messaging-sw.js");
    console.log("Service Worker registered successfully:", registration);
    return registration;
  } catch (error) {
    throw error;
  }
};

// export const listenToForegroundMessages = async (): Promise<void> => {
//   try {
//     if (!messaging) {
//       console.warn("Firebase messaging not available");
//       return;
//     }

//     onMessage(messaging, (payload) => {
//       console.log("Foreground message received:", payload);
//       const { title, body } = payload.notification || {};

//       if (title && body && "Notification" in window) {
//         new Notification(title, { body });
//       }
//     });
//   } catch (error) {
//     console.error("Failed to listen to foreground messages:", error);
//   }
// };

interface IPushNotificationsResponse {
  firebaseTokens: string[];
}

export const useFirebasePushNotifications = (
  options?: UseMutationOptions<IPushNotificationsResponse, ApiError>
) => {
  const { mutate, ...rest } = useMutation({
    mutationFn: async () => {
      // Request notification permission
      await Notification.requestPermission().then((permission) => {
        if (permission !== "granted") {
          console.warn("Notification permission not granted");
          return;
        }
      });

      // Register the service worker
      const swRegistration = await registerServiceWorker().then((registration) => {
        if (!registration) {
          console.warn("Service Worker registration failed");
          return;
        }
        return registration;
      });

      const messaging = await getMessagingInstance();

      // Get the token from Firebase messaging
      const token = await getToken(messaging, {
        vapidKey: process.env.NEXT_PUBLIC_FIREBASE_VAPID_KEY,
        serviceWorkerRegistration: swRegistration,
      });

      console.log("token", token);
      localStorage.setItem("fcmToken", token);

      // Check if the token is already in the server
      const tokens = await getFirebaseTokens();
      if (tokens.data.firebaseTokens.includes(token)) {
        return tokens.data;
      }

      // Add the token to the server
      const response = await addFirebaseToken({ token });
      return response.data;
    },
    ...options,
  });

  useEffect(() => {
    mutate();
  }, [mutate]);

  return rest;
};
