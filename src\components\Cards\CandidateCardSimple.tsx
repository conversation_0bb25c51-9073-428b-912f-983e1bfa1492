import React from "react";
import { LeftIconTop, LocationIcon1 } from "../Icons";

interface CandidateCardSimpleProps {
  imageUrl: string;
  name: string;
  jobTitle: string;
  location: string;
  skills: string[];
  onClick?: () => void;
}

const CandidateCardSimple: React.FC<CandidateCardSimpleProps> = ({
  imageUrl,
  name,
  jobTitle,
  location,
  skills,
  onClick,
}) => {
  return (
    <div
      className="p-6 border border-gray-300 hover:border-orange-100 transition-all rounded-2xl shadow-md"
      onClick={onClick}
    >
      <div className="flex items-center">
        <img src={imageUrl} alt={name} className="w-[100px] h-[100px] rounded-full object-cover" />
        <div className="ml-4">
          <div className="flex gap-x-3 items-center mb-3">
            <h2 className="text-2xl font-bold text-black-100">{name}</h2>
          </div>
          <p className="text-orange-100 font-medium">{jobTitle}</p>
        </div>
      </div>
      <div className="flex my-6">
        <div className="text-black-100 flex gap-x-2">
          <LocationIcon1 />
          {location}
        </div>
      </div>
      <div className="mb-6">
        <ul className="flex flex-wrap gap-2">
          {skills.slice(0, 4).map((skill, index) => (
            <li
              key={index}
              className="border border-gray-300 inline-block text-gray-100 text-base font-normal px-6 py-3 rounded-full"
            >
              {skill}
            </li>
          ))}
          {skills.length > 4 && (
            <li className="bg-orange-100 text-white text-base font-normal flex justify-center items-center w-[60px] h-[44px] rounded-full">
              +{skills.length - 4}
            </li>
          )}
        </ul>
      </div>
      <button
        className="border border-orange-100 text-orange-100 hover:bg-orange-100 rounded-full hover:text-white p-4 flex justify-center items-center gap-x-2 w-full"
        onClick={onClick}
      >
        View Profile <LeftIconTop />
      </button>
    </div>
  );
};

export default CandidateCardSimple;
