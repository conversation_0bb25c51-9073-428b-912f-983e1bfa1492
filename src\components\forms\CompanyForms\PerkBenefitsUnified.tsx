"use client";

import { useQueryClient } from "@tanstack/react-query";
import { Trash2 } from "lucide-react";
import { useRouter } from "next/navigation";
import React, { useEffect, useState } from "react";
import { useForm, type SubmitHandler } from "react-hook-form";
import { toast } from "sonner";
import { Input } from "../../ui/input";
import { Label } from "../../ui/label";
import { useUpdateCompanyProfile } from "@/hooks/useMutation";
import { useGetCompanyProfile } from "@/hooks/useQuery";
import { IPerkBenefit } from "@/types/query.types";

const inputClasses = "h-12 px-4 rounded-full border border-gray-300 w-full";

export interface PerkBenefitsUnifiedProps {
  isEditMode?: boolean;
}

const PerkBenefitsUnified: React.FC<PerkBenefitsUnifiedProps> = ({ isEditMode = false }) => {
  const router = useRouter();
  const queryClient = useQueryClient();
  const { data: companyData, isLoading: isProfileLoading } = useGetCompanyProfile();
  const companyProfile = companyData?.data;

  // Local state for perks
  const [perks, setPerks] = useState<IPerkBenefit[]>([]);

  // Initialize with data from server
  useEffect(() => {
    if (companyProfile?.perksAndBenefits) {
      setPerks(companyProfile.perksAndBenefits);
    }
  }, [companyProfile]);

  const {
    register,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm<Omit<IPerkBenefit, "_id">>({
    defaultValues: {
      benefitName: "",
      benefitDescription: "",
    },
  });

  const { mutate: updateProfile, isPending } = useUpdateCompanyProfile({
    onSuccess: () => {
      // Don't show a toast here since we're showing specific toasts for add/delete actions
      queryClient.invalidateQueries({ queryKey: ["get-company-profile"] });
    },
    onError: (error) => {
      toast.error(error.response?.data?.message || "Failed to save perks and benefits");
    },
  });

  // Handle form submission
  const onSubmit: SubmitHandler<Omit<IPerkBenefit, "_id">> = (formData) => {
    const newPerk: IPerkBenefit = {
      ...formData,
      _id: Date.now().toString(), // Temporary ID
    };

    const updatedPerks = [...perks, newPerk];
    setPerks(updatedPerks);

    // Always save immediately when adding a new perk
    updateProfile({ perksAndBenefits: updatedPerks });

    // Show appropriate toast message
    toast.success(`Perk "${formData.benefitName}" added successfully`);

    reset();
  };

  // Handle delete
  const handleDelete = (perkId: string) => {
    // Find the perk to be deleted (for the toast message)
    const perkToDelete = perks.find((perk) => perk._id === perkId);
    const updatedPerks = perks.filter((perk) => perk._id !== perkId);

    setPerks(updatedPerks);

    // Always save immediately when deleting a perk
    updateProfile({ perksAndBenefits: updatedPerks });

    // Show appropriate toast message
    if (perkToDelete) {
      toast.success(`Perk "${perkToDelete.benefitName}" deleted successfully`);
    } else {
      toast.success("Perk deleted successfully");
    }
  };

  // Next Step button only navigates to the next step
  const handleNextStep = () => {
    // Just navigate to the next step without hitting the API
    router.push("/company-profile-completion?stepId=achievements");
  };

  if (isProfileLoading) {
    return (
      <div className="text-center py-8">
        <div className="animate-spin rounded-full h-10 w-10 border-b-2 border-gray-900 mx-auto"></div>
        <p className="text-gray-500 mt-4">Loading perks and benefits...</p>
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto px-4">
      <h2 className="text-blue-600 text-2xl font-bold">Company</h2>
      <h3 className="text-3xl font-semibold text-gray-800 mt-2 mb-6">Perks & Benefits</h3>

      <div className="lg:w-[80%] mt-8">
        <form onSubmit={handleSubmit(onSubmit)}>
          <div className="grid grid-cols-1 gap-7">
            <div>
              <Label htmlFor="name" className="mb-3 block">
                Perk/Benefit Name
              </Label>
              <Input
                className={`${inputClasses} lg:w-[375px] h-[60px]`}
                id="name"
                {...register("benefitName", {
                  required: "Perk/Benefit name is required",
                })}
              />
              {errors.benefitName && (
                <p className="text-red-500 text-sm">{errors.benefitName.message}</p>
              )}
            </div>
            <div>
              <Label htmlFor="description" className="mb-3 block">
                Description
              </Label>
              <Input
                className={inputClasses}
                id="description"
                {...register("benefitDescription", {
                  required: "Description is required",
                })}
              />
              {errors.benefitDescription && (
                <p className="text-red-500 text-sm">{errors.benefitDescription.message}</p>
              )}
            </div>
          </div>

          <div className="mt-6">
            <button
              type="submit"
              disabled={isPending}
              className="text-blue-600 font-bold text-lg inline-flex gap-x-3 items-center hover:text-blue-700 transition-colors"
            >
              <span>
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  fill="none"
                  viewBox="0 0 24 24"
                >
                  <path
                    fill="currentColor"
                    fillRule="evenodd"
                    d="M12 0C5.373 0 0 5.373 0 12s5.373 12 12 12 12-5.373 12-12S18.627 0 12 0m1.09 16.364a1.09 1.09 0 1 1-2.18 0V13.09H7.635a1.09 1.09 0 1 1 0-2.182h3.273V7.636a1.09 1.09 0 1 1 2.182 0v3.273h3.273a1.09 1.09 0 1 1 0 2.182H13.09z"
                    clipRule="evenodd"
                  ></path>
                </svg>
              </span>
              <span>{isPending ? "Adding..." : "Add Perk/Benefit"}</span>
            </button>
          </div>
        </form>

        <div className="mt-8 space-y-4">
          {perks.length === 0 ? (
            <p className="text-gray-500 italic">No perks or benefits added yet.</p>
          ) : (
            perks.map((perk) => (
              <div
                key={perk._id}
                className="flex items-center justify-between p-4 border rounded-lg bg-white shadow-sm"
              >
                <div>
                  <h4 className="font-medium text-gray-800">{perk.benefitName}</h4>
                  <p className="text-gray-600">{perk.benefitDescription}</p>
                </div>
                <button
                  onClick={() => handleDelete(perk._id)}
                  className="text-red-500 hover:text-red-700 p-2 rounded-full hover:bg-red-50 transition-colors"
                  aria-label={`Delete ${perk.benefitName}`}
                  disabled={isPending}
                >
                  <Trash2 size={20} />
                </button>
              </div>
            ))
          )}
        </div>
      </div>

      {!isEditMode && (
        <div className="flex gap-5 mt-10">
          <button
            onClick={() => router.back()}
            className="font-medium py-3 px-8 rounded-full bg-gray-200 hover:bg-gray-300 text-gray-700 transition-colors"
          >
            Go Back
          </button>
          <button
            onClick={handleNextStep}
            className="font-medium py-3 px-8 rounded-full bg-orange-500 hover:bg-orange-600 text-white transition-colors"
          >
            Next Step
          </button>
        </div>
      )}
    </div>
  );
};

export default PerkBenefitsUnified;
