import React from "react";

const DeleteJobModal = ({
  cancelDelete,
  confirmDelete,
}: {
  cancelDelete: () => void;
  confirmDelete: () => void;
}) => {
  return (
    <div className="fixed inset-0 bg-black-100 bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white p-6 rounded-lg shadow-lg w-[400px]">
        <h2 className="text-lg font-semibold mb-4">Confirm Deletion</h2>
        <p className="text-gray-600 mb-6">
          Are you sure you want to delete this job? This action cannot be undone.
        </p>
        <div className="flex justify-end gap-4">
          <button onClick={cancelDelete} className="px-4 py-2 bg-gray-200 text-gray-700 rounded-lg">
            Cancel
          </button>
          <button onClick={confirmDelete} className="px-4 py-2 bg-red-500 text-white rounded-lg">
            Delete
          </button>
        </div>
      </div>
    </div>
  );
};

export default DeleteJobModal;
