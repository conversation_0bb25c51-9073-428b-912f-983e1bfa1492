"use client";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { cn } from "@/lib/utils";

const navItems = [
  { href: "/company-settings/company-profile", label: "My Profile" },
  { href: "/company-settings/about-company", label: "About Company" },
  { href: "/company-settings/social-network", label: "Social Network" },
  { href: "/company-settings/company-photos", label: "Company Photos" },
  { href: "/company-settings/perk-benefits", label: "Perk & Benefits" },
  { href: "/company-settings/achivements", label: "Achivements" },
];

const CompanySettingsNav = () => {
  const pathname = usePathname();
  return (
    <nav>
      <div className="setting-navigation relative ml-3">
        {navItems.map((item) => {
          const isActive = pathname === item.href;

          return (
            <div
              key={item.href}
              className={`${
                isActive ? "setting-navigation-item" : ""
              }  relative pb-12 last:pb-0 pl-6`}
            >
              <Link
                // Added key prop for list items
                href={item.href}
                className={cn(
                  " font-medium transition-colors",
                  isActive ? "text-orange-100 font-bold" : "text-gray-100 hover:text-orange-100"
                )}
              >
                {item.label}
              </Link>
            </div>
          );
        })}
      </div>
    </nav>
  );
};

export default CompanySettingsNav;
