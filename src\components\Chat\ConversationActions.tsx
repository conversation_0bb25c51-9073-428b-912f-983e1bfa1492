"use client";

import { Trash2, MoreH<PERSON><PERSON><PERSON> } from "lucide-react";
import { useState } from "react";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useDeleteConversation } from "@/hooks/useMutation";
import { debugLog } from "@/lib/debug";

interface ConversationActionsProps {
  conversationId: string;
  conversationName: string;
  onDeleted?: (conversationId: string) => void;
}

export default function ConversationActions({
  conversationId,
  conversationName,
  onDeleted,
}: ConversationActionsProps) {
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);

  // Use the mutation hook for deleting conversations
  const { mutate: deleteConversation, isPending: isDeleting } = useDeleteConversation({
    onSuccess: (data) => {
      // Log the successful deletion
      debugLog(`Conversation ${conversationId} deleted successfully:`, data);

      // Dialog will be closed by the success handler
      setIsDeleteDialogOpen(false);

      // Call the onDeleted callback if provided, passing the conversationId
      if (onDeleted) {
        debugLog(`Calling onDeleted callback for conversation ${conversationId}`);
        onDeleted(conversationId);
      }
    },
  });

  const handleDelete = () => {
    if (!conversationId) return;

    // Log the action
    debugLog(`Deleting conversation ${conversationId}`);

    // Call the mutation which will:
    // 1. Call the API endpoint to delete the conversation
    // 2. Emit the socket event on success
    // 3. Refresh the conversation list
    deleteConversation(conversationId);
  };

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="ghost"
            size="sm"
            className="h-8 w-8 p-0 rounded-full  absolute right-4 top-4 z-10"
          >
            <MoreHorizontal className="h-4 w-4" />
            <span className="sr-only">Conversation actions</span>
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuItem
            className="text-destructive flex items-center"
            onClick={(e) => {
              e.stopPropagation();
              setIsDeleteDialogOpen(true);
            }}
          >
            <Trash2 className="h-4 w-4 mr-2" />
            Delete Conversation
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      {/* Delete confirmation dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Conversation</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this conversation with{" "}
              <strong>{conversationName}</strong>? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsDeleteDialogOpen(false)}
              disabled={isDeleting}
            >
              Cancel
            </Button>
            <Button variant="destructive" onClick={handleDelete} disabled={isDeleting}>
              {isDeleting ? "Deleting..." : "Delete"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}
