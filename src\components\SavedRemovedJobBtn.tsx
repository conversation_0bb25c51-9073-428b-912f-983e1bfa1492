"use client";

import React from "react";
import { toast } from "sonner";
import { <PERSON><PERSON><PERSON>, Tooltip<PERSON>ontent, TooltipProvider, TooltipTrigger } from "./ui/tooltip";
import { useSaveJob, useUnsaveJob } from "@/hooks/useMutation";

const SavedRemovedJobBtn = ({ isSaved, jobId }: { isSaved: boolean; jobId: string }) => {
  const { mutate: saveJob, isPending: isSaving } = useSaveJob();
  const { mutate: unsaveJob, isPending: isUnsaving } = useUnsaveJob();

  const handleToggleSaveJob = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    if (!jobId) {
      return;
    }

    if (isSaved) {
      // Unsave job
      unsaveJob(jobId, {
        onSuccess: () => {
          toast.success("Job removed from saved list");
        },
        onError: () => {
          toast.error("Failed to remove job from saved list");
        },
      });
    } else {
      // Save job
      saveJob(jobId, {
        onSuccess: () => {
          toast.success("Job saved successfully");
        },
        onError: () => {
          toast.error("Failed to save job");
        },
      });
    }
  };
  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <button
            onClick={handleToggleSaveJob}
            disabled={isSaving || isUnsaving}
            className={`focus:outline-none transition-transform duration-200 ${isSaving || isUnsaving ? "opacity-50 cursor-not-allowed" : ""}`}
          >
            <span>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                fill="none"
                viewBox="0 0 24 24"
              >
                <path
                  fill={isSaved ? "#FF6B00" : "#262626"}
                  d={
                    isSaved
                      ? "M17.25 3H6.75a1.5 1.5 0 0 0-1.5 1.5V21a.75.75 0 0 0 1.148.636L12 18.134l5.603 3.502A.75.75 0 0 0 18.75 21V4.5a1.5 1.5 0 0 0-1.5-1.5z"
                      : "M17.25 3H6.75a1.5 1.5 0 0 0-1.5 1.5V21a.75.75 0 0 0 1.148.636L12 18.134l5.603 3.502A.75.75 0 0 0 18.75 21V4.5a1.5 1.5 0 0 0-1.5-1.5m0 1.5v10.647l-4.853-3.033a.75.75 0 0 0-.795 0L6.75 15.146V4.5zm-4.853 12.114a.75.75 0 0 0-.795 0L6.75 19.647v-2.732l5.25-3.28 5.25 3.28v2.732z"
                  }
                ></path>
              </svg>
            </span>
          </button>
        </TooltipTrigger>
        <TooltipContent>
          <p>{isSaved ? "Remove Job from Saved" : "Save Job"}</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
};

export default SavedRemovedJobBtn;
