import React from 'react';

interface SearchButtonProps {
    onClick?: () => void;
    text: string;
    icon?: React.ReactNode;
}

const SearchButton: React.FC<SearchButtonProps> = ({ onClick, text, icon }) => {
    return (
        <button 
            onClick={onClick} 
            className="bg-orange-100 text-white w-full sm:w-auto py-3 px-8 rounded-full inline-flex items-center justify-center space-x-2"
        >
            <span className="text">{text}</span>
            {icon && <span className="icon">{icon}</span>}
        </button>
    );
};

export default SearchButton;