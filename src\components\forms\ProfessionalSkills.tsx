"use client";

import { useQueryClient } from "@tanstack/react-query";
import { useRouter } from "next/navigation";
import React, { useState, useEffect, useCallback } from "react";
import { toast } from "sonner";
import FullPageLoader from "../ui/FullPageLoader";
import { SearchableSkillsMultiSelect } from "@/components/ui/searchable-skills-multiselect";
import { useUpdateJobSeekerProfile } from "@/hooks/useMutation";
import { useGetJobSeekerProfile } from "@/hooks/useQuery";

const ProfessionalSkills = ({ isButton = true }: { isButton?: boolean }) => {
  const router = useRouter();
  const queryClient = useQueryClient();
  const [selectedSkills, setSelectedSkills] = useState<string[]>([]);
  const [originalSkills, setOriginalSkills] = useState<string[]>([]);

  // Fetch job seeker profile
  const {
    data: profileData,
    isLoading: isProfileLoading,
    error: profileError,
  } = useGetJobSeekerProfile();

  const { mutate: updateProfile, isPending } = useUpdateJobSeekerProfile({
    onSuccess: () => {
      toast.success("Skills updated successfully");
      queryClient.invalidateQueries({ queryKey: ["get-jobseeker-profile"] });
      // Update original skills to match current selection
      setOriginalSkills(selectedSkills);
    },
    onError: (error) => {
      toast.error(error.response?.data?.message || "Failed to update skills");
    },
  });

  // Initialize selected skills from profile data
  useEffect(() => {
    if (profileData?.data.skills) {
      const skills = profileData?.data.skills as string[];
      setSelectedSkills(skills);
      setOriginalSkills(skills);
    }
  }, [profileData]);

  const handleSkillsChange = useCallback((skills: string[]) => {
    setSelectedSkills(skills);
  }, []);

  const handleUpdateSkills = useCallback(() => {
    updateProfile({ skills: selectedSkills });
  }, [selectedSkills, updateProfile]);

  const handleCancelChanges = useCallback(() => {
    setSelectedSkills(originalSkills);
  }, [originalSkills]);

  // Check if there are unsaved changes
  const hasChanges =
    JSON.stringify(selectedSkills.sort()) !== JSON.stringify(originalSkills.sort());

  if (isProfileLoading || isPending) {
    return <FullPageLoader message="Loading..." />;
  }

  if (profileError) {
    return <div>Error loading data. Please try again later.</div>;
  }

  return (
    <div>
      <h2 className="text-blue-200 text-2xl font-bold">Professional Skills</h2>
      <h3 className="text-4xl font-medium text-black-100 mt-3">Tell us about your Skills</h3>

      <div className="mt-10">
        <div className="flex items-center justify-between mb-4">
          <p className="text-black-100 font-medium">Select Your Skills</p>
          <p className="text-orange-100 font-medium">{selectedSkills.length} items selected</p>
        </div>

        <SearchableSkillsMultiSelect
          value={selectedSkills}
          onValueChange={handleSkillsChange}
          placeholder="Search and select your skills (e.g., SEO, Full-Stack, etc.)"
          className="w-full"
          disabled={isPending}
        />

        {/* Update and Cancel buttons */}
        {hasChanges && (
          <div className="flex gap-3 mt-4">
            <button
              onClick={handleCancelChanges}
              disabled={isPending}
              className="px-6 py-2 rounded-full border border-gray-300 text-gray-700
                font-medium hover:border-gray-400 disabled:opacity-50 transition-colors"
            >
              Cancel Changes
            </button>
            <button
              onClick={handleUpdateSkills}
              disabled={isPending}
              className="px-6 py-2 rounded-full bg-orange-500 text-white
                font-medium hover:bg-orange-600 disabled:opacity-70 transition-colors"
            >
              {isPending ? "Updating..." : "Update Skills"}
            </button>
          </div>
        )}
      </div>

      {isButton ? (
        <>
          {/* Navigation Buttons */}
          <div className="flex gap-5 mt-10">
            <button
              onClick={() => router.back()}
              className="py-4 px-10 bg-[#E7E7E7] font-bold rounded-full"
            >
              Go Back
            </button>
            <button
              disabled={isPending}
              onClick={() => router.push("/profile-completion?stepId=achievements")}
              className="py-4 px-10 bg-orange-100 text-white font-bold rounded-full"
            >
              Next Step
            </button>
          </div>
        </>
      ) : (
        <></>
      )}
    </div>
  );
};

export default ProfessionalSkills;
