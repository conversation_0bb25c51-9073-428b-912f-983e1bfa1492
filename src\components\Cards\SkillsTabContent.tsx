"use client";

import { Check } from "lucide-react";
import React from "react";

interface SkillsTabContentProps {
  onSelectSkill: (skill: string) => void;
  selectedSkills: string[];
  activeTab: string;
  title?: string;
}

const skillsData: Record<
  string,
  { title: string; skills: { name: string; description?: string }[] }
> = {
  digitalMarketing: {
    title: "Search Engine Optimization (SEO)",
    skills: [
      { name: "Keyword Research" },
      { name: "On-Page Optimization" },
      { name: "Off-Page SEO" },
      { name: "Big Word Here" },
      { name: "SEO Analytics" },
      { name: "On-Page Optimization" },
    ],
  },
  socialMedia: {
    title: "Social Media Marketing",
    skills: [
      { name: "Social Media Content Strategy" },
      { name: "Community Management" },
      { name: "Social Media Analytics" },
      { name: "Lorem Ipsum" },
      { name: "Paid Social Advertising" },
      { name: "Lorem Ipsum" },
    ],
  },
  affiliateMarketing: {
    title: "Affiliate Marketing",
    skills: [
      { name: "Partner/Publisher Outreach" },
      { name: "Performance Tracking" },
      { name: "Lorem Ipsum" },
      { name: "Commission Strategy" },
      { name: "ROI Analysis" },
      { name: "Affiliate Platforms" },
    ],
  },
  softwareDevelopment: {
    title: "Software Development",
    skills: [
      { name: "JavaScript" },
      { name: "React.js" },
      { name: "Node.js" },
      { name: "TypeScript" },
      { name: "GraphQL" },
      { name: "Python" },
      { name: "Django" },
      { name: "REST API" },
      { name: "Docker" },
      { name: "SQL" },
    ],
  },
  design: {
    title: "Design",
    skills: [
      { name: "UI/UX Design" },
      { name: "Figma" },
      { name: "Adobe XD" },
      { name: "Photoshop" },
      { name: "Wireframing" },
      { name: "Prototyping" },
      { name: "Typography" },
      { name: "Brand Identity" },
      { name: "Motion Graphics" },
    ],
  },
  humanResources: {
    title: "Human Resources",
    skills: [
      { name: "Recruitment" },
      { name: "Employee Relations" },
      { name: "HR Analytics" },
      { name: "Performance Management" },
      { name: "Payroll Management" },
      { name: "Workforce Planning" },
    ],
  },
  dataScience: {
    title: "Data Science",
    skills: [
      { name: "Machine Learning" },
      { name: "Deep Learning" },
      { name: "Data Analysis" },
      { name: "Big Data" },
      { name: "TensorFlow" },
      { name: "PyTorch" },
      { name: "SQL" },
      { name: "Data Visualization" },
      { name: "Power BI" },
    ],
  },
  customerService: {
    title: "Customer Service",
    skills: [
      { name: "Customer Support" },
      { name: "Live Chat" },
      { name: "Complaint Resolution" },
      { name: "Help Desk Management" },
      { name: "CRM Tools" },
      { name: "Service Training" },
    ],
  },
  projectManagement: {
    title: "Project Management",
    skills: [
      { name: "Agile Methodology" },
      { name: "Scrum" },
      { name: "Kanban" },
      { name: "Risk Management" },
      { name: "Project Scheduling" },
      { name: "Stakeholder Management" },
    ],
  },
  contentWriting: {
    title: "Content Writing",
    skills: [
      { name: "Copywriting" },
      { name: "Technical Writing" },
      { name: "SEO Writing" },
      { name: "Blogging" },
      { name: "Editing & Proofreading" },
    ],
  },
};

const SkillsTabContent = ({ onSelectSkill, selectedSkills, activeTab }: SkillsTabContentProps) => {
  const categoryData = skillsData[activeTab];
  if (!categoryData) return null;

  return (
    <div className="p-6">
      <h3 className="text-xl font-semibold mb-6">{categoryData.title}</h3>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {categoryData.skills.map((skill) => {
          const isSelected = selectedSkills.includes(skill.name);
          return (
            <button
              key={skill.name}
              onClick={() => onSelectSkill(skill.name)}
              className={`
                flex items-center justify-between px-6 py-3 
                rounded-full border-2 transition-all duration-200
                ${
                  isSelected
                    ? "bg-orange-100 border-orange-100 text-white"
                    : "border-gray-200 text-gray-600 hover:border-orange-100 hover:text-orange-100"
                }
              `}
            >
              <span className="text-base font-medium">{skill.name}</span>
              {isSelected && (
                <span className="flex items-center justify-center w-6 h-6 bg-white rounded-full">
                  <Check className="w-4 h-4 text-orange-100" />
                </span>
              )}
            </button>
          );
        })}
      </div>
    </div>
  );
};

export default SkillsTabContent;
