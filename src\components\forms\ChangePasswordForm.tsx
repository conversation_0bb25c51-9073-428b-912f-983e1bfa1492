"use client";

import { <PERSON>, <PERSON>Off } from "lucide-react";
import React, { useState } from "react";
import { useForm, type SubmitHandler } from "react-hook-form";
import { toast } from "sonner";
import { <PERSON><PERSON> } from "../ui/button";
import { Input } from "../ui/input";
import { Label } from "../ui/label";
import { useChangePassword } from "@/hooks/useMutation";
import { ApiError } from "@/types/common.types";
import { IChangePassword } from "@/types/mutation.types";

const inputStyles = "h-12 px-4 rounded-full border border-[#737373] w-full";

const ChangePasswordForm = () => {
  const [showCurrentPassword, setShowCurrentPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
  } = useForm<IChangePassword>();

  const { mutateAsync: changePassword, isPending } = useChangePassword({
    onSuccess: () => {
      toast.success("Password changed successfully", {
        description: "Your password has been updated.",
      });
    },
    onError: (error: ApiError) => {
      toast.error("Failed to change password", {
        description: error.response?.data?.message || "Something went wrong.",
      });
    },
  });

  const onSubmit: SubmitHandler<IChangePassword> = async (data) => {
    try {
      await changePassword(data);
    } catch (error) {
      console.error("Error changing password:", error);
    }
  };

  const currentPassword = watch("currentPassword");
  const newPassword = watch("newPassword");

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
      {/* Current Password */}
      <div className="space-y-2">
        <Label htmlFor="currentPassword" className="text-black-100">
          Current Password
        </Label>
        <div className="relative">
          <Input
            id="currentPassword"
            type={showCurrentPassword ? "text" : "password"}
            className={inputStyles}
            placeholder="Enter your current password"
            {...register("currentPassword", {
              required: "Current password is required",
            })}
          />
          <button
            type="button"
            onClick={() => setShowCurrentPassword(!showCurrentPassword)}
            className="absolute right-4 top-1/2 -translate-y-1/2 text-gray-500 hover:text-gray-700"
          >
            {showCurrentPassword ? <EyeOff size={20} /> : <Eye size={20} />}
          </button>
        </div>
        {errors.currentPassword && (
          <p className="text-red-500 text-sm">{errors.currentPassword.message}</p>
        )}
      </div>

      {/* New Password */}
      <div className="space-y-2">
        <Label htmlFor="newPassword" className="text-black-100">
          New Password
        </Label>
        <div className="relative">
          <Input
            id="newPassword"
            type={showNewPassword ? "text" : "password"}
            className={inputStyles}
            placeholder="Enter your new password"
            {...register("newPassword", {
              required: "New password is required",
              minLength: {
                value: 8,
                message: "Password must be at least 8 characters",
              },
              pattern: {
                value: /^(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*]).{8,}$/,
                message:
                  "Password must contain at least one uppercase letter, one number, and one special character",
              },
              validate: (value) =>
                value !== currentPassword || "New password must not match the current password",
            })}
          />
          <button
            type="button"
            onClick={() => setShowNewPassword(!showNewPassword)}
            className="absolute right-4 top-1/2 -translate-y-1/2 text-gray-500 hover:text-gray-700"
          >
            {showNewPassword ? <EyeOff size={20} /> : <Eye size={20} />}
          </button>
        </div>
        {errors.newPassword && <p className="text-red-500 text-sm">{errors.newPassword.message}</p>}
      </div>

      {/* Confirm New Password */}
      <div className="space-y-2">
        <Label htmlFor="confirmNewPassword" className="text-black-100">
          Confirm New Password
        </Label>
        <div className="relative">
          <Input
            id="confirmNewPassword"
            type={showConfirmPassword ? "text" : "password"}
            className={inputStyles}
            placeholder="Confirm your new password"
            {...register("confirmNewPassword", {
              required: "Confirm password is required",
              validate: (value) => value === newPassword || "Passwords do not match",
            })}
          />
          <button
            type="button"
            onClick={() => setShowConfirmPassword(!showConfirmPassword)}
            className="absolute right-4 top-1/2 -translate-y-1/2 text-gray-500 hover:text-gray-700"
          >
            {showConfirmPassword ? <EyeOff size={20} /> : <Eye size={20} />}
          </button>
        </div>
        {errors.confirmNewPassword && (
          <p className="text-red-500 text-sm">{errors.confirmNewPassword.message}</p>
        )}
      </div>

      {/* Submit Button */}
      <Button
        type="submit"
        className="w-full bg-orange-100 text-base h-[48px] rounded-full"
        disabled={isPending}
      >
        {isPending ? "Updating..." : "Update Password"}
      </Button>
    </form>
  );
};

export default ChangePasswordForm;
