"use client";
import Image from "next/image";
import Link from "next/link";
import React from "react";
import { PrimaryHeading } from "../Headings/PrimaryHeading";
import { useGetAppSection } from "@/hooks/useQuery";

const MobileAppSection = () => {
  const { data: appSectionData } = useGetAppSection();
  return (
    <section className="lg:py-20 py-14">
      <div className="container mx-auto">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-20 items-center">
          <div>
            <Image
              src="/images/mobile-section-left.png"
              width={764}
              height={680}
              alt="Mobile App"
            />
          </div>
          <div>
            <PrimaryHeading>
              {/* Take <span>YesJobs</span> On the Go */}
              {appSectionData?.data.section.heading}
            </PrimaryHeading>
            <p className="text-lg text-gray-100 mb-5">{appSectionData?.data.section.description}</p>
            <div className="flex space-x-4">
              <Link href={"#"}>
                {/* <Link href={appSectionData?.data.section.appStoreURL || "#"}> */}
                <Image
                  src="/images/app-store.png"
                  alt="App Store"
                  className="h-12"
                  width={150}
                  height={50}
                />
              </Link>
              <Link href={"#"}>
                {/* <Link href={appSectionData?.data.section.playStoreURL || "#"}> */}
                <Image
                  src="/images/google-play.png"
                  alt="Google Play"
                  className="h-12"
                  width={150}
                  height={50}
                />
              </Link>
            </div>
            <p className="text-lg text-orange-100 mt-2 font-bold">Coming Soon...</p>
          </div>
        </div>
      </div>
    </section>
  );
};

export default MobileAppSection;
