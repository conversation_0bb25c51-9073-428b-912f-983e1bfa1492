"use client";

import dynamic from "next/dynamic";
import { Suspense } from "react";

interface Step {
  id: string;
  title: string;
  component: React.ComponentType<unknown>;
}

interface MultiStepperProps {
  steps: Step[];
}

// Import MultiStepperContent with dynamic import to handle useSearchParams
const MultiStepperContent = dynamic(() => import("./MultiStepperContent"), {
  ssr: false,
});

export default function MultiStepper({ steps }: MultiStepperProps) {
  return (
    <Suspense
      fallback={
        <div className="max-w-4xl mx-auto">
          <div className="bg-white rounded-lg shadow-sm p-6 animate-pulse">
            <div className="h-64 w-full bg-gray-200 rounded"></div>
          </div>
        </div>
      }
    >
      <MultiStepperContent steps={steps} />
    </Suspense>
  );
}
