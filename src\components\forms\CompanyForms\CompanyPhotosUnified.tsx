"use client";

import { useQueryClient } from "@tanstack/react-query";
import { useRouter } from "next/navigation";
import React, { useEffect, useState } from "react";
import { toast } from "sonner";
import UploadPortfolio from "@/components/Cards/UploadPortfolio";
import { useUploadCompanyPhotos, useDeletePhoto } from "@/hooks/useMutation";
import { useGetCompanyProfile } from "@/hooks/useQuery";

interface PhotoItem {
  id: string;
  file?: File;
  previewUrl: string;
  isUploaded: boolean;
  s3Key?: string;
  uploadedAt?: string;
  isUploading?: boolean;
}

export interface CompanyPhotosProps {
  isEditMode?: boolean;
  onNext?: () => void;
}

const CompanyPhotosUnified: React.FC<CompanyPhotosProps> = ({ isEditMode = false, onNext }) => {
  const router = useRouter();
  const queryClient = useQueryClient();
  const { data: companyData, isLoading: isProfileLoading } = useGetCompanyProfile();
  const companyProfile = companyData?.data;

  const [photos, setPhotos] = useState<PhotoItem[]>([]);
  const [uploadingPhotoIds, setUploadingPhotoIds] = useState<string[]>([]);
  const [deletingPhotoId, setDeletingPhotoId] = useState<string | null>(null);

  const { mutateAsync: uploadPhotos } = useUploadCompanyPhotos();
  const { mutate: deletePhoto } = useDeletePhoto();

  // Initialize with existing photos from profile
  useEffect(() => {
    if (companyProfile?.companyPhotos) {
      setPhotos(
        companyProfile.companyPhotos.map((photo) => ({
          id: photo.s3Key,
          previewUrl: photo.url,
          isUploaded: true,
          s3Key: photo.s3Key,
          uploadedAt: photo.uploadedAt,
        }))
      );
    }
  }, [companyProfile]);

  // Handle file selection for multiple files
  const handleFilesSelected = (files: FileList) => {
    // Check if adding these files would exceed the limit
    // if (photos.length + files.length > 8) {
    //   toast.error(
    //     `You can upload a maximum of 8 photos. You already have ${photos.length} photos.`
    //   );
    //   return;
    // }

    const newPhotos = Array.from(files).map((file) => ({
      id: URL.createObjectURL(file),
      file,
      previewUrl: URL.createObjectURL(file),
      isUploaded: false,
      isUploading: false,
    }));

    // Add new photos to the state
    const updatedPhotos = [...photos, ...newPhotos];
    setPhotos(updatedPhotos);

    // Show toast for multiple uploads
    if (files.length > 1) {
      toast.info(`Uploading ${files.length} photos...`);
    }

    // Upload each photo immediately
    newPhotos.forEach((photo) => {
      if (photo.file) {
        handleUploadSinglePhoto(photo.id, photo.file);
      }
    });
  };

  // Handle single file upload
  const handleUploadSinglePhoto = async (photoId: string, file: File) => {
    // Add to uploading state
    setUploadingPhotoIds((prev) => [...prev, photoId]);

    try {
      const formData = new FormData();
      formData.append("photos", file);

      const response = await uploadPhotos(formData as unknown as File[]);

      // Update the photo with the S3 key after successful upload
      if (response.data && response.data.length > 0) {
        setPhotos((prev) =>
          prev.map((photo) =>
            photo.id === photoId
              ? {
                  ...photo,
                  isUploaded: true,
                  s3Key: response.data[0].s3Key,
                  previewUrl: response.data[0].url,
                }
              : photo
          )
        );

        // Show success toast for each uploaded photo
        toast.success(`Photo "${file.name}" uploaded successfully`);
      }

      // Refresh company profile data
      queryClient.invalidateQueries({ queryKey: ["get-company-profile"] });
    } catch (error) {
      toast.error(
        `Failed to upload "${file.name}": ${(error as Error).message || "Unknown error"}`
      );

      // Remove the failed photo from the list
      setPhotos((prev) => prev.filter((photo) => photo.id !== photoId));
    } finally {
      // Remove from uploading state
      setUploadingPhotoIds((prev) => prev.filter((id) => id !== photoId));
    }
  };

  // Handle single file upload from UploadPortfolio component
  const handleSingleFileUpload = (file: File, previewUrl: string) => {
    const newPhotoId = Date.now().toString();

    // Add to photos state
    setPhotos((prev) => [
      ...prev,
      {
        id: newPhotoId,
        file,
        previewUrl,
        isUploaded: false,
        isUploading: true,
      },
    ]);

    // Upload immediately
    handleUploadSinglePhoto(newPhotoId, file);
  };

  // Handle photo deletion
  const handleDeletePhoto = (id: string, s3Key?: string) => {
    if (!s3Key) {
      // If the photo is not uploaded yet, just remove it from the list
      setPhotos((prev) => prev.filter((photo) => photo.id !== id));
      return;
    }

    // Set deleting state
    setDeletingPhotoId(id);

    deletePhoto(
      { s3Key },
      {
        onSuccess: () => {
          setPhotos((prev) => prev.filter((photo) => photo.id !== id));
          toast.success("Photo deleted successfully");
          queryClient.invalidateQueries({ queryKey: ["get-company-profile"] });
        },
        onError: (error) => {
          toast.error(error.response?.data?.message || "Failed to delete photo");
        },
        onSettled: () => {
          setDeletingPhotoId(null);
        },
      }
    );
  };

  // Handle next step button click
  const handleNext = () => {
    if (onNext) {
      onNext();
    } else {
      router.push("/company-profile-completion?stepId=perks-benefits");
    }
  };

  // Handle back button click
  const handleBack = () => {
    router.back();
  };

  if (isProfileLoading) {
    return (
      <div className="text-center py-8">
        <div className="animate-spin rounded-full h-10 w-10 border-b-2 border-gray-900 mx-auto"></div>
        <p className="text-gray-500 mt-4">Loading company photos...</p>
      </div>
    );
  }

  const isAnyPhotoUploading = uploadingPhotoIds.length > 0;

  return (
    <div className="max-w-6xl mx-auto px-4">
      <h2 className="text-blue-600 text-2xl font-bold">Company</h2>
      <h3 className="text-3xl font-semibold text-gray-800 mt-2 mb-6">Company Photos</h3>

      <div className="mb-8">
        {/* <p className="text-lg text-gray-700 mb-4">Upload Photos ({photos.length}/8 uploaded)</p> */}
        <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4 mb-6">
          {photos.map((photo) => (
            <div key={photo.id} className="relative">
              <UploadPortfolio
                image={photo.previewUrl}
                onDelete={() => handleDeletePhoto(photo.id, photo.s3Key)}
                isDeleting={deletingPhotoId === photo.id}
                isPreview={!photo.isUploaded}
              />
              {uploadingPhotoIds.includes(photo.id) && (
                <div className="absolute inset-0 bg-black/30 flex items-center justify-center rounded-lg">
                  <div className="bg-white/90 px-4 py-2 rounded-full flex items-center">
                    <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-orange-500 mr-2"></div>
                    <span className="text-sm font-medium text-gray-700">Uploading...</span>
                  </div>
                </div>
              )}
            </div>
          ))}

          {/* Always show upload box */}
          <div>
            {isEditMode ? (
              <UploadPortfolio onUpload={handleSingleFileUpload} multiple={true} />
            ) : (
              <UploadPortfolio onFilesSelected={handleFilesSelected} multiple={true} />
            )}
          </div>
        </div>

        {isAnyPhotoUploading && (
          <p className="text-orange-500 text-sm mb-4">
            Uploading photos... Please wait for uploads to complete.
          </p>
        )}
      </div>

      {!isEditMode && (
        <div className="flex gap-4 mt-10">
          <button
            onClick={handleBack}
            className="font-medium py-3 px-8 rounded-full bg-gray-200 hover:bg-gray-300 text-gray-700"
          >
            Go Back
          </button>
          <button
            onClick={handleNext}
            disabled={isAnyPhotoUploading}
            className="font-medium py-3 px-8 rounded-full bg-orange-500 hover:bg-orange-600 text-white disabled:opacity-70"
          >
            {isAnyPhotoUploading ? "Processing..." : "Next Step"}
          </button>
        </div>
      )}
    </div>
  );
};

export default CompanyPhotosUnified;
