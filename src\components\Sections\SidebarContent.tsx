"use client";

import { Check } from "lucide-react";
import { useSearchParams } from "next/navigation";
import React from "react";

interface Step {
  id: string;
  title: string;
}

interface SidebarContentProps {
  steps: Step[];
}

const SidebarContent = ({ steps }: SidebarContentProps) => {
  const searchParams = useSearchParams();
  const currentStepId = searchParams.get("stepId") || steps[0].id;
  const currentStepIndex = steps.findIndex((step) => step.id === currentStepId);

  return (
    <nav className="rounded-3xl border border-gray-200 lg:p-10 p-4 h-full">
      <h1 className="text-2xl text-orange-100 font-bold mb-10">Profile Completion</h1>
      <ol className="lg:space-y-10 flex gap-4 sm:gap-0 flex-wrap sm:justify-between lg:block">
        {steps.map((step, index) => {
          const isCompleted = index < currentStepIndex;
          const isCurrent = step.id === currentStepId;

          return (
            <li
              key={step.id}
              className={`flex items-center li-after relative ${
                isCurrent || isCompleted ? "text-orange-100" : "text-gray-100"
              }`}
            >
              <span
                className={`w-8 h-8 flex items-center justify-center rounded-full mr-4 ${
                  isCompleted
                    ? "text-white border border-orange-100 bg-orange-100"
                    : isCurrent
                      ? "bg-white text-orange-100 border border-orange-100"
                      : "bg-gray-200 text-gray-600"
                }`}
              >
                {isCompleted ? <Check /> : `${index + 1}`}
              </span>
              <span
                className={`${
                  isCurrent ? "font-bold text-orange-100" : "text-gray-100"
                } hidden lg:inline`}
              >
                {step.title}
              </span>
            </li>
          );
        })}
      </ol>
    </nav>
  );
};

export default SidebarContent;
