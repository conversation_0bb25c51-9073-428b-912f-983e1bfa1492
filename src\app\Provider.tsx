"use client";

import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import SocketProvider from "@/components/Chat/SocketProvider";
import SocketStatus from "@/components/Chat/SocketStatus";
import { LoaderProvider } from "@/context/LoaderContext";

// Create a QueryClient instance
const queryClient = new QueryClient();

export function Providers({ children }: { children: React.ReactNode }) {
  return (
    <QueryClientProvider client={queryClient}>
      <LoaderProvider>
        <SocketProvider>
          {children}
          <SocketStatus />
        </SocketProvider>
      </LoaderProvider>
    </QueryClientProvider>
  );
}
