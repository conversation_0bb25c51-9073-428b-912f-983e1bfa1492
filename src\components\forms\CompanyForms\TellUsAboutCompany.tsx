"use client";

import { useQueryClient } from "@tanstack/react-query";
import { Camera } from "lucide-react";
import Image from "next/image";
import { useRouter } from "next/navigation";
import React, { useEffect, useState } from "react";
import { useForm, type SubmitHandler } from "react-hook-form";
import { toast } from "sonner";
import usePlacesAutocomplete, { getGeocode, getLatLng } from "use-places-autocomplete";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { DEFAULT_IMAGE } from "@/constants/app.constants";
import { useUpdateCompanyProfile, useUploadProfilePicture } from "@/hooks/useMutation";
import { useGetCompanyProfile } from "@/hooks/useQuery";
import { useUserStore } from "@/store/useUserStore";
import { ApiError } from "@/types/common.types";
import { ICompany, ICompanyProfile, Ilocation } from "@/types/query.types";

const inputClasses = "h-12 px-4 rounded-full border border-gray-300 w-full";

const TellUsAboutCompany = () => {
  const router = useRouter();
  const { currentUser } = useUserStore();
  const queryClient = useQueryClient();
  const { data: companyData, isLoading } = useGetCompanyProfile();
  const companyProfile = companyData?.data;
  const company = currentUser;

  const [profilePicture, setProfilePicture] = useState(DEFAULT_IMAGE);
  const [loading, setLoading] = useState(true);

  const { mutateAsync: uploadProfilePicture } = useUploadProfilePicture();

  // Places autocomplete
  const {
    ready,
    value,
    setValue: setLocationValue,
    suggestions: { status, data: suggestionsData },
    clearSuggestions,
  } = usePlacesAutocomplete({
    requestOptions: {
      componentRestrictions: { country: "au" },
    },
  });

  // Define a type for address components
  interface AddressComponent {
    long_name: string;
    short_name: string;
    types: string[];
  }

  // Handle location selection
  const handleSelect = async (description: string) => {
    setLocationValue(description, false);
    clearSuggestions();

    try {
      const results = await getGeocode({ address: description });
      const { lat, lng } = getLatLng(results[0]);
      const address = results[0].formatted_address;

      // Extract address components
      const addressComponents = results[0].address_components;
      const city =
        addressComponents.find((c: AddressComponent) => c.types.includes("locality"))?.long_name ||
        addressComponents.find((c: AddressComponent) =>
          c.types.includes("administrative_area_level_2")
        )?.long_name ||
        "";
      const state =
        addressComponents.find((c: AddressComponent) =>
          c.types.includes("administrative_area_level_1")
        )?.long_name || "";
      const country =
        addressComponents.find((c: AddressComponent) => c.types.includes("country"))?.long_name ||
        "";

      // Update form values directly
      setValue("location", {
        type: "Point" as const, // Use const assertion to ensure type is exactly "Point"
        coordinates: [lng, lat] as [number, number],
        formattedAddress: address,
        city,
        state,
        country,
      });
    } catch {
      toast.error("Failed to get location details");
    }
  };

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    reset,
  } = useForm<
    Omit<ICompanyProfile, "companyABN" | "abn"> & {
      abn: string;
      companyEmail: string;
      location: Ilocation;
    }
  >({
    defaultValues: {
      companyEmail: "",
      companyName: "",
      profilePicture: DEFAULT_IMAGE,
      abn: companyData?.data.companyProfile.abn ? String(companyData?.data.companyProfile.abn) : "",
      foundedDate: "",
      companySize: "",
      websiteUrl: "",
      location: {
        type: "Point",
        coordinates: [0, 0],
        formattedAddress: "",
        city: "",
        state: "",
        country: "",
      },
    },
  });

  // Format date from ISO string to YYYY-MM-DD for date inputs
  const formatDateForInput = (dateString: string | undefined): string => {
    if (!dateString) return "";
    const date = new Date(dateString);
    if (isNaN(date.getTime())) return "";
    return date.toISOString().split("T")[0] || "";
  };

  // Update form values when companyProfile data is available
  useEffect(() => {
    if (companyProfile) {
      // Format the founded date for the date input
      const formattedFoundedDate = formatDateForInput(companyProfile.companyProfile?.foundedDate);

      // Check if location data exists in the API response
      const locationData = companyProfile.companyProfile?.location
        ? {
            type: "Point" as const,
            coordinates: companyProfile.companyProfile.location.coordinates || [0, 0],
            formattedAddress: companyProfile.companyProfile.location.formattedAddress || "",
            city: companyProfile.companyProfile.location.city || "",
            state: companyProfile.companyProfile.location.state || "",
            country: companyProfile.companyProfile.location.country || "",
          }
        : {
            type: "Point" as const,
            coordinates: [0, 0] as [number, number],
            formattedAddress: "",
            city: "",
            state: "",
            country: "",
          };

      // If there's location data, set the location value for autocomplete
      if (locationData.formattedAddress) {
        setLocationValue(locationData.formattedAddress, false);
      } else if (locationData.city || locationData.country) {
        // Fallback to city and country if no formatted address
        const locationString = [locationData.city, locationData.country].filter(Boolean).join(", ");
        setLocationValue(locationString, false);
      }

      // Create a new object with all company data and the location field
      const formData = {
        ...companyProfile.companyProfile, // Spread existing company profile data
        companyEmail: companyProfile?.user.email || "",
        abn: companyProfile.companyProfile?.abn ? String(companyProfile.companyProfile.abn) : "",
        foundedDate: formattedFoundedDate,
        location: locationData,
      };

      // Form data is ready for reset
      reset(formData);

      // Set profile picture
      setProfilePicture(companyProfile.companyProfile?.profilePicture || DEFAULT_IMAGE);
      setLoading(false);
    }
  }, [companyProfile, company, reset, setLocationValue]);

  const { mutate: updateProfile, isPending } = useUpdateCompanyProfile({
    onSuccess: () => {
      toast.success("Company profile updated successfully");
      queryClient.invalidateQueries({ queryKey: ["get-company-profile"] });
      router.push("/company-profile-completion?stepId=company-details");
    },
    onError: (error) => {
      toast.error(error.response?.data?.message || "Failed to update company profile");
    },
  });

  const handleProfilePictureChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    try {
      const response = await uploadProfilePicture(file);
      setProfilePicture(response.data.profilePicture);
      setValue("profilePicture", response.data.profilePicture);
      toast.success("Profile picture uploaded successfully");
    } catch (error) {
      toast.error((error as ApiError).response?.data.message || "Failed to upload profile picture");
    }
  };

  const onSubmit: SubmitHandler<
    Omit<ICompanyProfile, "companyABN" | "abn"> & {
      abn: string;
      companyEmail: string;
      location: Ilocation;
    }
  > = async (formData) => {
    if (!companyProfile?._id) {
      toast.error("Company profile not found");
      return;
    }

    // Additional validation for location
    if (!formData.location?.formattedAddress) {
      toast.error("Please select a location");
      return;
    }

    // Convert form data to the expected format
    const abnNumber = formData.abn ? Number(formData.abn) : companyProfile.companyProfile?.abn || 0;

    // Create a properly typed object for the API
    const updatedProfile: Partial<ICompany> = {
      companyProfile: {
        companyName: formData.companyName,
        profilePicture: formData.profilePicture,
        companySize: formData.companySize,
        websiteUrl: formData.websiteUrl,
        abn: abnNumber,
        companyABN: abnNumber,
        foundedDate: new Date(formData.foundedDate).toISOString(),
        companyEmail: formData.companyEmail,
        // Add location object
        location: {
          type: "Point",
          coordinates: formData.location.coordinates || [0, 0],
          formattedAddress: formData.location.formattedAddress || "",
          city: formData.location.city || "",
          state: formData.location.state || "",
          country: formData.location.country || "",
        },
      },
    };

    updateProfile(updatedProfile);
  };

  // Prepare form submission

  if (loading || isLoading) {
    return (
      <div className="flex justify-center items-center h-screen">
        <div className="loader border-t-4 border-orange-100 rounded-full w-16 h-16 animate-spin"></div>
      </div>
    );
  }

  return (
    <div>
      <h2 className="text-blue-200 text-2xl font-bold">Tell us about Company</h2>
      <h3 className="text-4xl font-medium text-black-100 mt-3 mb-6">Company Profile</h3>
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        <Label
          htmlFor="profile-picture"
          className="inline-flex items-center space-x-4 cursor-pointer"
        >
          <div className="w-24 h-24 bg-gray-200 rounded-full flex items-center justify-center overflow-hidden">
            {profilePicture ? (
              <Image
                src={profilePicture}
                alt="Company Logo"
                width={96}
                height={96}
                className="object-cover"
              />
            ) : (
              <Camera className="w-8 h-8 text-gray-100" />
            )}
          </div>
          <div>
            <div className="text-black-100 text-lg font-normal underline">Company Logo</div>
            <Input
              id="profile-picture"
              type="file"
              accept="image/*"
              className="mt-1 hidden"
              onChange={handleProfilePictureChange}
              disabled={isPending}
            />
          </div>
        </Label>
        <div className="xl:w-[80%]">
          <div className="grid lg:grid-cols-2 gap-7">
            <div>
              <Label htmlFor="company-name" className="mb-3 block">
                Company Name
              </Label>
              <Input
                className={inputClasses}
                id="company-name"
                {...register("companyName", {
                  required: "Company name is required",
                })}
              />
              {errors.companyName && (
                <p className="text-red-500 text-sm">{errors.companyName.message}</p>
              )}
            </div>
            <div>
              <Label htmlFor="company-email" className="mb-3 block">
                Company Email
              </Label>
              <Input
                className={`${inputClasses} bg-gray-100/20 cursor-not-allowed`}
                id="company-email"
                readOnly
                value={companyProfile?.user.email || ""} // Use company email from the user store
                {...register("companyEmail", {
                  required: "Company name is required",
                })}
              />
              {errors.companyName && (
                <p className="text-red-500 text-sm">{errors.companyName.message}</p>
              )}
            </div>
            <div>
              <Label htmlFor="company-abn" className="mb-3 block">
                Company ABN
              </Label>
              <Input
                className={inputClasses}
                id="company-abn"
                type="text"
                maxLength={11}
                onKeyPress={(e) => {
                  // Allow only numbers
                  if (!/[0-9]/.test(e.key)) {
                    e.preventDefault();
                  }
                }}
                {...register("abn", {
                  required: "ABN is required",
                  pattern: {
                    value: /^\d{11}$/,
                    message: "ABN must be exactly 11 digits",
                  },
                  validate: (value) => {
                    const abnString = String(value);
                    if (abnString.length !== 11) {
                      return "ABN must be exactly 11 digits";
                    }
                    if (!/^\d+$/.test(abnString)) {
                      return "ABN must contain only numbers";
                    }
                    return true;
                  },
                })}
              />
              {errors.abn && <p className="text-red-500 text-sm">{errors.abn.message}</p>}
            </div>
            <div>
              <Label htmlFor="founded-date" className="mb-3 block">
                Founded Date
              </Label>
              <Input
                className={`${inputClasses} w-full block text-gray-100`}
                id="founded-date"
                type="date"
                max={new Date().toISOString().split("T")[0]} // Set max date to today
                {...register("foundedDate", {
                  required: "Founded date is required",
                  validate: (value) => {
                    if (!value) return true;

                    // Check if founded date is in the future
                    const selectedDate = new Date(value);
                    const currentDate = new Date();
                    currentDate.setHours(0, 0, 0, 0);

                    if (selectedDate > currentDate) {
                      return "Founded date cannot be in the future";
                    }

                    return true;
                  },
                })}
              />
              {errors.foundedDate && (
                <p className="text-red-500 text-sm">{errors.foundedDate.message}</p>
              )}
            </div>
            <div>
              <Label htmlFor="company-size" className="mb-3 block">
                Company Size
              </Label>
              <Input
                className={inputClasses}
                id="company-size"
                {...register("companySize", {
                  required: "Company size is required",
                })}
              />
              {errors.companySize && (
                <p className="text-red-500 text-sm">{errors.companySize.message}</p>
              )}
            </div>
            <div>
              <Label htmlFor="website" className="mb-3 block">
                Website URL
              </Label>
              <Input
                className={inputClasses}
                id="website"
                type="url"
                {...register("websiteUrl", {
                  required: "Website URL is required",
                })}
              />
              {errors.websiteUrl && (
                <p className="text-red-500 text-sm">{errors.websiteUrl.message}</p>
              )}
            </div>

            <div className="col-span-1 md:col-span-2">
              <Label htmlFor="location" className="mb-3 block">
                Location
              </Label>
              <div className="relative">
                <Input
                  className={inputClasses}
                  id="location"
                  placeholder="e.g., Sydney, Australia"
                  value={value}
                  disabled={!ready || isPending}
                  required
                  {...register("location.formattedAddress", {
                    required: "Location is required",
                    onChange: (e) => {
                      setLocationValue(e.target.value);
                      // Clear suggestions when user types
                      if (status === "OK") {
                        clearSuggestions();
                      }
                    },
                  })}
                />
                {status === "OK" && suggestionsData && suggestionsData.length > 0 && (
                  <ul className="absolute z-10 w-full bg-white border border-gray-300 rounded-lg mt-2 max-h-60 overflow-y-auto">
                    {suggestionsData.map(({ place_id, description }) => (
                      <li
                        key={place_id}
                        className="p-2 cursor-pointer hover:bg-gray-100"
                        onClick={() => handleSelect(description)}
                      >
                        {description}
                      </li>
                    ))}
                  </ul>
                )}
              </div>
              {errors.location && errors.location.formattedAddress && (
                <p className="text-red-500 text-sm">{errors.location.formattedAddress.message}</p>
              )}
            </div>
          </div>

          <div className="flex flex-wrap gap-y-3 gap-x-5 mt-10">
            <button
              type="submit"
              disabled={isPending}
              className={`font-bold py-3 px-8 rounded-full inline-flex items-center justify-center bg-orange-500 text-white transition-colors ${
                isPending ? "opacity-70 cursor-not-allowed" : "hover:bg-orange-600"
              }`}
            >
              {isPending ? (
                <>
                  <svg
                    className="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                  >
                    <circle
                      className="opacity-25"
                      cx="12"
                      cy="12"
                      r="10"
                      stroke="currentColor"
                      strokeWidth="4"
                    ></circle>
                    <path
                      className="opacity-75"
                      fill="currentColor"
                      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                    ></path>
                  </svg>
                  Saving...
                </>
              ) : (
                "Next Step"
              )}
            </button>
          </div>
        </div>
      </form>
    </div>
  );
};

export default TellUsAboutCompany;
