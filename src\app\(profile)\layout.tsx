import type { Metadata } from "next";
import Image from "next/image";
import type React from "react"; // Import React

export const metadata: Metadata = {
  title: "Authentication",
  description: "Login and register pages",
};

export default function ProfileLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <>
      <div className="container mx-auto pt-10">
        <Image src={"/images/logo.png"} alt="" width={140} height={50} />
        <div className="mt-10">{children}</div>
      </div>
    </>
  );
}
