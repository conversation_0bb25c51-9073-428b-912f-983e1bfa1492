import Image from "next/image";
import React from "react";

import ApplyNowButton from "../Buttons/ApplyNowButton";
import PrimaryButton from "../Buttons/PrimaryButton";
import { LeftIconTop } from "../Icons";
import SavedRemovedJobBtn from "../SavedRemovedJobBtn";

interface JobShortCardDashboardProps {
  jobId: string;
  imageUrl: string;
  jobTitle: string;
  companyName: string;
  category: string;
  jobType: string;
  cityName: string;
  salaryRange: string;
  deadline: string;
  shortlisted?: boolean;
  pending?: boolean;
  rejected?: boolean;
  savedJobs?: boolean;
  viewJob?: boolean;
  detailLink?: string;
  onApplySuccess?: () => void;
  isSaved?: boolean;
  isBoosted?: boolean;
  isPremium?: boolean;
  alreadyApplied?: boolean;
  isSavedBtnShow?: boolean;
  isDeleted?: boolean;
}

const JobShortCardDashboard: React.FC<JobShortCardDashboardProps> = ({
  jobId,
  imageUrl,
  jobTitle,
  companyName,
  category,
  jobType,
  cityName,
  salaryRange,
  deadline,
  shortlisted,
  pending,
  rejected,
  savedJobs,
  viewJob,
  detailLink = "/jobs/" + jobId,
  onApplySuccess,
  isSaved = false,
  isBoosted = false,
  isPremium = false,
  alreadyApplied = false,
  isSavedBtnShow = false,
  isDeleted = false,
}) => {
  return (
    <div
      className={`${isDeleted ? " cursor-not-allowed relative" : ""} rounded-[18px] border border-gray-200 p-[30px]`}
    >
      {isDeleted && (
        <div className="bg-red-500 rounded-[18px] z-10 bg-opacity-45 absolute top-0 left-0 right-0 bottom-0 flex items-center justify-center">
          <div className="text-black text-2xl">This job post was deleted</div>
        </div>
      )}
      <div className="flex flex-wrap gap-4">
        <div>
          <Image
            src={imageUrl}
            alt="Company Logo"
            width={80}
            height={80}
            className="w-[80px] h-[80px] rounded-full"
          />
        </div>
        <div>
          {jobTitle && <h2 className="text-2xl font-medium text-black-100 mb-3">{jobTitle}</h2>}
          {(companyName || category) && (
            <p className="text-black-100">
              {companyName && (
                <>
                  <span className="text-gray-100">by</span> {companyName}
                </>
              )}{" "}
              {category && (
                <>
                  <span className="text-gray-100">in</span> {category}
                </>
              )}
            </p>
          )}
        </div>
        <div className="flex space-x-4 ml-auto">
          {isPremium && (
            <span>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                fill="none"
                viewBox="0 0 24 24"
              >
                <path
                  fill={isPremium ? "#FF6B00" : "#262626"}
                  d="M21.647 6.9a1.486 1.486 0 0 0-1.772.355l-3.157 3.403-3.356-7.528v-.01a1.5 1.5 0 0 0-2.724 0v.01l-3.356 7.528-3.157-3.403A1.5 1.5 0 0 0 1.53 8.543l2.126 9.738A1.5 1.5 0 0 0 5.13 19.5h13.74a1.5 1.5 0 0 0 1.474-1.219l2.126-9.738q-.002-.015.007-.03a1.486 1.486 0 0 0-.83-1.613m-2.77 11.07-.006.03H5.129l-.006-.03L3 8.25l.013.015 3.938 4.241a.75.75 0 0 0 1.235-.204L12 3.75l3.815 8.555a.75.75 0 0 0 1.235.204l3.938-4.241L21 8.25z"
                ></path>
              </svg>
            </span>
          )}
          {isBoosted && (
            <span>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                fill="none"
                viewBox="0 0 24 24"
              >
                <path
                  fill={isBoosted ? "#FF6B00" : "#262626"}
                  d="M20.23 11.079a.75.75 0 0 0-.468-.531L14.36 8.522l1.374-6.875a.75.75 0 0 0-1.283-.656l-10.5 11.25a.75.75 0 0 0 .28 1.219l5.404 2.026-1.371 6.867a.75.75 0 0 0 1.283.656l10.5-11.25a.75.75 0 0 0 .182-.68m-9.977 8.984.982-4.911a.75.75 0 0 0-.469-.85l-4.954-1.86 7.934-8.5-.981 4.91a.75.75 0 0 0 .469.85l4.95 1.857z"
                ></path>
              </svg>
            </span>
          )}
          {isSavedBtnShow ? (
            <>
              <span>
                <SavedRemovedJobBtn isSaved={isSaved} jobId={jobId} />
              </span>
            </>
          ) : (
            <></>
          )}
        </div>
      </div>
      <div className="flex gap-4 flex-wrap mt-6">
        {jobType && (
          <div className="bg-offWhite-100 text-orange-100 px-6 py-3 rounded-full">{jobType}</div>
        )}
        {cityName && (
          <div className="bg-white text-gray-100 border border-gray-100 px-6 py-3 rounded-full">
            {cityName}
          </div>
        )}
        {salaryRange && (
          <div className="bg-white text-gray-100 border border-gray-100 px-6 py-3 rounded-full">
            {salaryRange}
          </div>
        )}
        {shortlisted && (
          <div className="ml-auto">
            <div className="flex items-center gap-x-2 bg-blue-100 text-white px-7 py-3 rounded-full text-lg font-medium">
              <span>
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="20"
                  height="20"
                  fill="none"
                  viewBox="0 0 20 20"
                >
                  <path
                    fill="#fff"
                    d="m11.907 6.918-7 6.875a.94.94 0 0 1-1.314 0l-3-2.946A.939.939 0 0 1 1.907 9.51l2.344 2.302 6.343-6.23a.938.938 0 0 1 1.314 1.337M19.42 5.59a.937.937 0 0 0-1.328-.011L11.75 11.81l-.616-.605a.937.937 0 1 0-1.314 1.337l1.273 1.25a.94.94 0 0 0 1.314 0l7-6.875a.94.94 0 0 0 .012-1.325z"
                  ></path>
                </svg>
              </span>
              <span>Shortlisted</span>
            </div>
          </div>
        )}
        {rejected && (
          <div className="ml-auto">
            <div className="flex items-center gap-x-2 bg-red-500 text-white px-7 py-3 rounded-full text-lg font-medium">
              <span>
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="20"
                  height="20"
                  fill="none"
                  viewBox="0 0 20 20"
                >
                  <path
                    fill="#fff"
                    d="M16.288 14.962a.939.939 0 1 1-1.328 1.328L10 11.328l-4.962 4.96A.939.939 0 1 1 3.71 14.96L8.672 10 3.712 5.04A.94.94 0 1 1 5.04 3.71L10 8.672l4.962-4.962a.94.94 0 1 1 1.328 1.328L11.328 10z"
                  ></path>
                </svg>
              </span>
              <span>Rejected</span>
            </div>
          </div>
        )}
        {pending && (
          <div className="ml-auto">
            <div className="flex items-center gap-x-2 bg-gray-300 text-gray-100 px-7 py-3 rounded-full text-lg font-medium">
              <span>
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="20"
                  height="20"
                  fill="none"
                  viewBox="0 0 20 20"
                >
                  <path
                    fill="#737373"
                    d="m14.169 8.964-3.75 1.875A.938.938 0 0 1 9.062 10V5.625a.938.938 0 0 1 1.875 0v2.858l2.393-1.196a.937.937 0 1 1 .839 1.677m3.572 9.442a.937.937 0 0 1-1.147-.665c-.188-.694-.84-1.178-1.594-1.178s-1.406.484-1.594 1.178a.937.937 0 1 1-1.813-.482c.19-.71.602-1.34 1.178-1.797a2.812 2.812 0 1 1 4.453 0c.576.457.99 1.087 1.178 1.797a.94.94 0 0 1-.662 1.147m-3.679-4.656a.938.938 0 1 0 1.876 0 .938.938 0 0 0-1.876 0m-4.884 2.762a6.562 6.562 0 1 1 7.334-7.333.938.938 0 1 0 1.86-.235 8.437 8.437 0 1 0-9.426 9.428.937.937 0 1 0 .232-1.86"
                  ></path>
                </svg>
              </span>
              <span>Pending</span>
            </div>
          </div>
        )}
        {savedJobs && (
          <div className="ml-auto">
            {alreadyApplied ? (
              <>
                <button
                  disabled
                  className={`py-3 px-8 rounded-full inline-flex items-center justify-center space-x-2 bg-gray-300 cursor-not-allowed text-white transition-colors`}
                >
                  <span className="text">Already Applied</span>
                  <span className="icon">
                    <LeftIconTop />
                  </span>
                </button>
              </>
            ) : (
              <>
                <ApplyNowButton jobId={jobId} onSuccess={onApplySuccess} />
              </>
            )}
          </div>
        )}
        {viewJob && (
          <div className="ml-auto">
            <PrimaryButton
              text="View Job"
              link={detailLink}
              icon={
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="20"
                  height="20"
                  fill="none"
                  viewBox="0 0 20 20"
                >
                  <path
                    fill="#fff"
                    d="M16.522 2.76 6.346.961a1.56 1.56 0 0 0-1.81 1.268L2.21 15.433a1.563 1.563 0 0 0 1.267 1.81l10.176 1.796q.136.024.275.024a1.56 1.56 0 0 0 1.54-1.293l2.324-13.203a1.564 1.564 0 0 0-1.27-1.808m-2.85 14.377-9.56-1.688L6.328 2.862l9.56 1.689zM7.18 5.234a.94.94 0 0 1 1.086-.76l5.25.927a.937.937 0 0 1-.156 1.864 1 1 0 0 1-.165-.014l-5.255-.93a.937.937 0 0 1-.76-1.087m-.54 3.078a.94.94 0 0 1 1.088-.76l5.251.928a.938.938 0 0 1-.33 1.846l-5.251-.928a.94.94 0 0 1-.757-1.086m-.546 3.077a.94.94 0 0 1 1.086-.76l2.628.465a.937.937 0 1 1-.326 1.846l-2.626-.464a.936.936 0 0 1-.762-1.087"
                  ></path>
                </svg>
              }
            />
          </div>
        )}
      </div>
      {deadline && (
        <div className="flex justify-between mt-6">
          <div className="text-orange-100">
            Deadline Date: <span className="text-black-100">{deadline}</span>{" "}
          </div>
        </div>
      )}
    </div>
  );
};

export default JobShortCardDashboard;
