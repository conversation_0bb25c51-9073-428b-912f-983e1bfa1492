# Yes Jobs Messaging System Documentation

This documentation outlines the complete messaging flow, including socket events, API endpoints, and the required payloads for the Yes Jobs messaging system.

---

## Socket Connection

### Authentication

Authentication is required to connect to the socket. Include your authentication token in the handshake request:
---

## Complete Messaging Flow

### 1. Connect to Socket

```js
const socket = io("https://node.hostingladz.com:3127/messages", {
  auth: {
    token: token, // Send token in auth object
  },
});

socket.on("connect", () => {
  console.log("Connected to messaging socket");
});

socket.on("error", (error) => {
  console.error("Socket error:", error);
});
```

### 2. Auto-Join Conversations

```js
socket.on("join_conversation", (data) => {
  if (data.autoJoined) {
    console.log(`Automatically joined ${data.conversationCount} conversations`);
  } else {
    console.log(`Joined conversation: ${data.conversationId}`);
  }
});
```

### 3. Create a Conversation

#### For Recruiters with Job Application

```js
// API Endpoint: POST /conversations
const createConversationPayload = {
  type: "application",
  jobApplicationId: "job_application_id_here",
};

// After successful API call:
socket.on("create_conversation", (data) => {
  console.log(`Conversation created: ${data.conversationId}`);
});
```

#### For Recruiters without Job Application (Direct Message)

```js
// API Endpoint: POST /conversations
const createDirectConversationPayload = {
  type: "direct",
  jobSeekerProfileId: "job_seeker_profile_id_here",
};

// After successful API call:
socket.on("create_conversation", (data) => {
  console.log(`Direct conversation created: ${data.conversationId}`);
});
```

### 4. Upload Media (if needed)

```js
// API Endpoint: POST /upload/media/chat
// Content-Type: multipart/form-data
// Include files in the form data

// Response:
{
  "success": true,
  "message": "Media has been uploaded",
  "data": {
    "mediaFiles": [
      {
        "url": "https://s3-url.com/file1.jpg",
        "s3Key": "messages/user-123/file1.jpg",
        "fileName": "file1.jpg",
        "fileSize": 1024
      }
    ]
  }
}
```

### 5. Send a Message

```js
// Socket Event: send_message
const messagePayload = {
  conversationId: "conversation_id_here",
  content: "Hello, this is a message",
  mediaFiles: [
    {
      url: "https://s3-url.com/file1.jpg",
      s3Key: "messages/user-123/file1.jpg",
      fileName: "file1.jpg",
      fileSize: 1024,
    },
  ],
};

socket.emit("send_message", messagePayload);

// Listen for new messages
socket.on("new_message", (message) => {
  console.log("New message received:", message);
});
```

### 6. Mark Messages as Read

```js
// Socket Event: mark_as_read
const markAsReadPayload = {
  messageId: "message_id_here",
};

socket.emit("mark_as_read", markAsReadPayload);

socket.on("message_read", (data) => {
  console.log(`Message ${data.messageId} was read by ${data.readBy} at ${data.readAt}`);
});
```

### 7. Typing Indicators

```js
// Socket Event: typing
const typingPayload = {
  conversationId: "conversation_id_here",
  isTyping: true,
};

socket.emit("typing", typingPayload);

socket.on("typing", (data) => {
  console.log(`${data.userName} is typing...`);
});

socket.on("stop_typing", (data) => {
  console.log(`${data.userName} stopped typing`);
});
```

### 8. Delete a Conversation

```js
// API Endpoint: DELETE /conversations/:conversationId

socket.on("delete_conversation", (data) => {
  console.log(`Conversation deleted: ${data.conversationId}`);
});

socket.emit("delete_conversation", {
  conversationId: "conversation_id_here",
});
```

### 9. Leave a Conversation

```js
// Socket Event: leave_conversation
const leavePayload = {
  conversationId: "conversation_id_here",
};

socket.emit("leave_conversation", leavePayload);

socket.on("leave_conversation", (data) => {
  console.log(`${data.userName} left the conversation`);
});
```

---

## API Endpoints

### Conversations

- **Create Conversation**  
  `POST /conversations`
- **Get All Conversations**  
  `GET /conversations?page=1&limit=10&search=optional_search_term`
- **Get Conversation by ID**  
  `GET /conversations/:conversationId`
- **Delete Conversation**  
  `DELETE /conversations/:conversationId`

### Messages

- **Get Messages**  
  `GET /messages?conversationId=conversation_id_here&page=1&limit=20`
- **Delete Message**  
  `DELETE /messages/:messageId?deleteForEveryone=true`

### Media Upload

- **Upload Media**  
  `POST /upload/media/chat`  
  Content-Type: multipart/form-data

---

## Important Notes

### Media Handling

- Always upload media files **before** sending a message
- Store the returned media file objects and include them in the message payload
- Media files are stored directly in the message object

### Conversation Deletion

- Deleting a conversation only removes it **for the current user**
- Other participants can still see and interact with the conversation
- Deleted conversations won't appear in your list

### Authentication

- All API requests and socket connections require a **valid JWT token**
- Include in the `Authorization` header: `Bearer YOUR_JWT_TOKEN`

### Error Handling

- Always listen for the `error` event on the socket
- Check API responses for error messages

### Duplicate Prevention

- Duplicate conversations between the same users are prevented
- Existing conversation will be returned if it already exists

---

## Example Implementation Flow

1. Connect to the socket with authentication
2. Create a conversation or use an existing one
3. If sending media, upload using the upload API
4. Send messages through the socket
5. Listen for `new_message` and update UI
6. Mark messages as read when viewed
7. Use typing indicators for user experience
8. Allow users to delete or leave conversations as needed
