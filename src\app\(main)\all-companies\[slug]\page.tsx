"use client";

import { use } from "react";
import CandidateCard from "@/components/Cards/CandidateCard";
import CompanyDetailCard from "@/components/Cards/CompanyDetailCard";
import LoadingSpinner from "@/components/LoadingSpinner/LoadingSpinner";
import { DEFAULT_IMAGE } from "@/constants/app.constants";
import { useGetCompanyProfileById, useGetRelatedCompanies } from "@/hooks/useQuery";

// Related Companies Component
const RelatedCompanies = ({ companyId }: { companyId: string }) => {
  const { data, isLoading, isError } = useGetRelatedCompanies(companyId, {
    page: 1,
    limit: 3,
    includeLocation: "true",
    includeCompanySize: "true",
    includeDescription: "true",
    maxDistance: 50,
  });

  const relatedCompanies = data?.data?.data || [];

  if (isLoading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mt-10">
        {Array.from({ length: 3 }).map((_, index) => (
          <div key={index} className="animate-pulse">
            <div className="p-6 border flex flex-col border-gray-300 rounded-2xl shadow-md">
              <div className="flex items-center">
                <div className="w-[100px] h-[100px] rounded-full bg-gray-200"></div>
                <div className="ml-4 w-full">
                  <div className="h-6 bg-gray-200 rounded w-3/4 mb-3"></div>
                  <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                </div>
              </div>
              <div className="h-4 bg-gray-200 rounded w-full my-6"></div>
              <div className="h-20 bg-gray-200 rounded w-full mb-6"></div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  if (isError) {
    return <div className="text-center py-8">Error loading related companies.</div>;
  }

  return (
    <>
      {relatedCompanies.length > 0 && (
        <>
          <h2 className="text-2xl font-bold text-black-100 mb-6 mt-14">Related Companies</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mt-10">
            <>
              {relatedCompanies.map((company) => (
                <div key={company._id}>
                  <CandidateCard
                    isAllCompanies
                    candidateId={company._id}
                    candidateDescription={
                      company.aboutCompany?.description || "No description available."
                    }
                    candidateImage={company.companyProfile.profilePicture || DEFAULT_IMAGE}
                    candidateLocation={
                      company.companyProfile.location
                        ? `${company.companyProfile.location.formattedAddress}`
                        : "Location not specified"
                    }
                    candidateName={company.companyProfile.companyName}
                    link={`/all-companies/${company._id}`}
                    openJobs={company.activeJobs} // Default value as we don't have this information
                  />
                </div>
              ))}
            </>
          </div>
        </>
      )}
    </>
  );
};

export default function CompanyDetailPage({ params }: { params: Promise<{ slug: string }> }) {
  const { slug } = use(params);
  const companyId = slug;
  const { data: companyData, isLoading, isError } = useGetCompanyProfileById(companyId);
  const company = companyData?.data;

  if (isLoading) {
    return (
      <div className="container mx-auto py-20">
        <div className="flex justify-center items-center h-64">
          <LoadingSpinner />
        </div>
      </div>
    );
  }

  if (isError || !company) {
    return (
      <div className="container mx-auto py-20">
        <div className="text-center py-8 text-red-500">
          Error loading company details. Please try again later.
        </div>
      </div>
    );
  }

  // Extract company data
  const {
    companyProfile,
    aboutCompany,
    // socialNetworks = [],
    // companyPhotos = [],
    perksAndBenefits = [],
    companyAchievements = [],
  } = company;

  // Format founded date
  const foundedDate = companyProfile?.foundedDate
    ? new Date(companyProfile.foundedDate).getFullYear().toString()
    : "";

  // Format social network links
  // const socialLinks = {
  //   linkedin:
  //     socialNetworks.find((network) => network.networkName === "LinkedIn")?.networkUrl || "#",
  //   twitter: socialNetworks.find((network) => network.networkName === "Twitter")?.networkUrl || "#",
  //   facebook:
  //     socialNetworks.find((network) => network.networkName === "Facebook")?.networkUrl || "#",
  // };

  // Format perks and benefits
  const formattedPerks = perksAndBenefits?.map((perk) => perk.benefitDescription) || [];

  // Format company photos
  const photoUrls = company.companyPhotos?.map((photo) => photo.url || "") || [];

  // Format company achievements to match the expected type
  const formattedAchievements = companyAchievements.map((achievement) => ({
    ...achievement,
    date: achievement.date || "",
  }));

  return (
    <>
      <section className="pt-16 pb-20">
        <div className="container mx-auto">
          <div className="p-[30px]">
            <CompanyDetailCard
              imageUrl={companyProfile.profilePicture || DEFAULT_IMAGE}
              comanyName={companyProfile.companyName}
              category={companyProfile.companySize || "Not specified"} // Default category
              openJobs={company.activeJobs || 0} // Default value
              cityName={companyProfile.location?.city || "Not specified"}
              salaryRange="Competitive" // Default value
              designation="Company" // Default value
              aboutUs={aboutCompany.description || ""}
              founded={foundedDate}
              location={companyProfile.location?.formattedAddress || "Not specified"}
              companySize={companyProfile.companySize}
              qualification="Various" // Default value
              email={company.user?.email || ""}
              socialNetworks={company?.socialNetworks}
              // Xlink={socialLinks.twitter}
              // instaLink={socialLinks.facebook}
              // linkdinLink={socialLinks.linkedin}
              // skillsExperience={skillsExperience}
              // skillsTags={skillTags}
              perksBenefits={formattedPerks}
              companyPhotos={photoUrls}
              // companyVideoUrl={"http://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4"}
              companyVideoUrl={aboutCompany.companyVideo?.url}
              achievements={formattedAchievements}
            />
            {/* Related Companies Section */}
            {/* <div> */}
            {/* <h2 className="text-2xl font-bold text-black-100 mb-6 mt-14">Related Companies</h2> */}
            <RelatedCompanies companyId={companyId} />
            {/* </div> */}
          </div>
        </div>
      </section>
    </>
  );
}
