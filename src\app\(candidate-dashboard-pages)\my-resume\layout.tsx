import type { Metadata } from "next";
import MyResumeLayout from "./MyResumeLayout";
import Header from "@/components/layout/Header";

export const metadata: Metadata = {
  title: "Authentication",
  description: "Login and register pages",
};

export default function MyResumePagesLayout({ children }: { children: React.ReactNode }) {
  return (
    <>
      <Header dashboardPages={true} />
      <div className="container mx-auto py-14">
        <div className="lg:flex gap-x-10">
          <MyResumeLayout />
          <div className="lg:w-[calc(100%-350px)] rounded-[18px]">{children}</div>
        </div>
      </div>
    </>
  );
}
