"use client";

import { useQueryClient } from "@tanstack/react-query";
import { AlertCircle, Loader2 } from "lucide-react";
import React, { useEffect, useState } from "react";
import { toast } from "sonner";
import UploadPortfolio from "@/components/Cards/UploadPortfolio";
import { useDeletePortfolioImage, useUploadPortfolio } from "@/hooks/useMutation";
import { useGetJobSeekerProfile } from "@/hooks/useQuery";

interface PortfolioImage {
  id: string; // Unique identifier for the image
  file?: File; // Original file (for new uploads)
  s3Key: string; // S3 key for deletion
  previewUrl: string; // URL for displaying the image
  uploadedAt: string; // Timestamp of when the image was uploaded
  isUploaded: boolean; // Whether the image has been uploaded to the server
  isUploading?: boolean; // Whether the image is currently uploading
  uploadProgress?: number; // Upload progress percentage
  error?: string; // Error message if upload failed
}

const PortfolioImages = () => {
  const queryClient = useQueryClient();
  const { data: jobSeekerData, isLoading: isProfileLoading, isError } = useGetJobSeekerProfile();
  const jobSeekerProfile = jobSeekerData?.data;

  const [portfolioImages, setPortfolioImages] = useState<PortfolioImage[]>([]);
  const [uploadingImageIds, setUploadingImageIds] = useState<string[]>([]);
  const [deletingImageId, setDeletingImageId] = useState<string | null>(null);
  const { mutateAsync: uploadPortfolioImages } = useUploadPortfolio();
  const { mutate: deletePortfolioImage } = useDeletePortfolioImage();

  // Sync portfolioImages state with jobSeekerProfile data
  useEffect(() => {
    if (jobSeekerProfile?.portFolioImages) {
      setPortfolioImages(
        jobSeekerProfile.portFolioImages.map((image) => ({
          id: image.s3Key,
          s3Key: image.s3Key,
          previewUrl: image.url,
          uploadedAt: image.uploadedAt,
          isUploaded: true,
        }))
      );
    }
  }, [jobSeekerProfile]);

  // Handle file selection for multiple files
  const handleFilesSelected = (files: FileList) => {
    // Create new image entries with upload status
    const newImages = Array.from(files).map((file) => ({
      id: URL.createObjectURL(file),
      file,
      previewUrl: URL.createObjectURL(file),
      s3Key: "",
      uploadedAt: new Date().toISOString(),
      isUploaded: false,
      isUploading: false,
    }));

    // Add new images to the state
    const updatedImages = [...portfolioImages, ...newImages];
    setPortfolioImages(updatedImages);

    // Show toast for multiple uploads
    if (files.length > 1) {
      toast.info(`Uploading ${files.length} images...`);
    }

    // Upload each image immediately
    newImages.forEach((image) => {
      if (image.file) {
        handleUploadSingleImage(image.id, image.file);
      }
    });
  };

  // Handle single image upload
  const handleUploadSingleImage = async (imageId: string, file: File) => {
    // Add to uploading state
    setUploadingImageIds((prev) => [...prev, imageId]);

    // Update image status to uploading
    setPortfolioImages((prev) =>
      prev.map((img) =>
        img.id === imageId
          ? {
              ...img,
              isUploading: true,
              uploadProgress: 0,
            }
          : img
      )
    );

    try {
      // Track upload progress
      const handleProgress = (progress: number) => {
        setPortfolioImages((prev) =>
          prev.map((img) => (img.id === imageId ? { ...img, uploadProgress: progress } : img))
        );
      };

      // Upload the image
      const response = await uploadPortfolioImages({
        files: [file],
        options: { onProgress: handleProgress },
      });

      // Update the image with the S3 key after successful upload
      if (response && response.s3Keys && response.s3Keys.length > 0) {
        setPortfolioImages((prev) =>
          prev.map((img) =>
            img.id === imageId
              ? {
                  ...img,
                  isUploading: false,
                  isUploaded: true,
                  s3Key: response.s3Keys[0] || "",
                  previewUrl: response.urls[0] || img.previewUrl,
                  uploadProgress: 100,
                }
              : img
          )
        );

        // Show success toast for each uploaded image
        toast.success(`Image "${file.name}" uploaded successfully`);
      }

      // Refresh profile data
      queryClient.invalidateQueries({ queryKey: ["get-jobseeker-profile"] });
    } catch (error) {
      toast.error(
        `Failed to upload "${file.name}": ${(error as Error).message || "Unknown error"}`
      );

      // Update the image with error status
      setPortfolioImages((prev) =>
        prev.map((img) =>
          img.id === imageId
            ? {
                ...img,
                isUploading: false,
                error: "Failed to upload image",
              }
            : img
        )
      );
    } finally {
      // Remove from uploading state
      setUploadingImageIds((prev) => prev.filter((id) => id !== imageId));
    }
  };

  // Handle single file upload from UploadPortfolio component
  const handleSingleFileUpload = (file: File, previewUrl: string) => {
    const newImageId = Date.now().toString();

    // Add to images state
    setPortfolioImages((prev) => [
      ...prev,
      {
        id: newImageId,
        file,
        previewUrl,
        s3Key: "",
        uploadedAt: new Date().toISOString(),
        isUploaded: false,
        isUploading: true,
      },
    ]);

    // Upload immediately
    handleUploadSingleImage(newImageId, file);
  };

  // Handle image deletion
  const handleDeleteImage = (id: string, s3Key?: string) => {
    if (!s3Key) {
      // If the image is not uploaded yet, just remove it from the list
      setPortfolioImages((prev) => prev.filter((image) => image.id !== id));
      return;
    }

    // Set deleting state
    setDeletingImageId(id);

    deletePortfolioImage(
      { s3Key },
      {
        onSuccess: () => {
          // Remove the image from the local state
          setPortfolioImages((prev) => prev.filter((image) => image.id !== id));
          toast.success("Portfolio image deleted successfully");
          // Refresh the profile data
          queryClient.invalidateQueries({ queryKey: ["get-jobseeker-profile"] });
        },
        onError: (error) => {
          toast.error(error.response?.data?.message || "Failed to delete portfolio image");
        },
        onSettled: () => {
          // Clear deleting state
          setDeletingImageId(null);
        },
      }
    );
  };

  if (isProfileLoading) {
    return (
      <div className="text-center py-8">
        <div className="animate-spin rounded-full h-10 w-10 border-b-2 border-gray-900 mx-auto"></div>
        <p className="text-gray-500 mt-4">Loading portfolio images...</p>
      </div>
    );
  }

  if (isError) {
    return (
      <div className="text-center py-8">
        <p className="text-red-500">Failed to load portfolio images. Please try again later.</p>
      </div>
    );
  }

  // Check if any image is currently uploading
  const isAnyImageUploading = uploadingImageIds.length > 0;

  return (
    <div>
      <h2 className="text-blue-200 text-2xl font-bold">Portfolio</h2>
      <h3 className="text-4xl font-medium text-black-100 mt-3 mb-6">Portfolio Images</h3>
      <p className="mb-4">Upload your portfolio images</p>

      {isAnyImageUploading && (
        <p className="text-orange-500 text-sm mb-4">
          Uploading images... Please wait for uploads to complete.
        </p>
      )}

      <div className="grid xl:grid-cols-4 lg:grid-cols-2 md:grid-cols-2 gap-5 mb-10">
        {portfolioImages.map((image) => (
          <div key={image.id} className="relative">
            <UploadPortfolio
              image={image.previewUrl}
              onDelete={() => handleDeleteImage(image.id, image.s3Key)}
              isDeleting={deletingImageId === image.id}
              isUploading={image.isUploading}
              uploadProgress={image.uploadProgress}
              error={image.error}
              isPreview={!image.isUploaded}
            />

            {/* Uploading overlay - alternative to the built-in progress in UploadPortfolio */}
            {image.isUploading && (
              <div className="absolute inset-0 bg-black/30 flex items-center justify-center rounded-lg">
                <div className="bg-white/90 px-4 py-2 rounded-full flex items-center">
                  <Loader2 className="h-4 w-4 text-orange-500 animate-spin mr-2" />
                  <span className="text-sm font-medium text-gray-700">
                    {image.uploadProgress ? `${Math.round(image.uploadProgress)}%` : "Uploading..."}
                  </span>
                </div>
              </div>
            )}

            {/* Error overlay */}
            {image.error && (
              <div className="absolute bottom-0 left-0 right-0 bg-red-50 p-2 flex items-center space-x-2">
                <AlertCircle className="h-4 w-4 text-red-500" />
                <span className="text-xs text-red-500">{image.error}</span>
              </div>
            )}
          </div>
        ))}

        <div>
          <UploadPortfolio
            onUpload={handleSingleFileUpload}
            onFilesSelected={handleFilesSelected}
            multiple={true}
          />
        </div>
        {/* Upload button */}
        {/* {portfolioImages.length < 8 && (
        )} */}
      </div>
    </div>
  );
};

export default PortfolioImages;
