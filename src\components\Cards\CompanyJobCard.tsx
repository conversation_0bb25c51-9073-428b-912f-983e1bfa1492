import Image from "next/image";
// import Link from "next/link";
import React from "react";

import PrimaryButton from "../Buttons/PrimaryButton";

interface CompanyJobCardProps {
  imageUrl?: string;
  jobTitle?: string;
  companyName?: string;
  jobType?: string;
  cityName?: string;
  salaryRange?: string;
  isJobActive?: boolean;
  viewJob?: boolean;
  jobId?: string;
  onEdit?: (jobId: string) => void;
  onDelete?: (jobId: string) => void;
}

const CompanyJobCard: React.FC<CompanyJobCardProps> = ({
  imageUrl,
  jobTitle,
  companyName,
  jobType,
  cityName,
  salaryRange,
  isJobActive,
  viewJob,
  jobId,
  onEdit,
  onDelete,
}) => {
  return (
    <div className="rounded-[18px] border border-gray-200 p-[30px]">
      <div className="flex flex-wrap">
        <div className="mr-4">
          <Image
            src={imageUrl}
            alt="Company Logo"
            width={80}
            height={80}
            className="w-[80px] h-[80px] rounded-full"
          />
        </div>
        <div>
          <h2 className="text-2xl font-semibold text-black-100 mb-3">{jobTitle}</h2>
          <p className="text-black-100">
            <span className="text-gray-100">by</span> {companyName}
          </p>
        </div>
        <div className="flex space-x-4 ml-auto">
          <button
            onClick={() => onEdit(jobId)}
            className="text-blue-500 hover:text-blue-700"
            title="Edit Job"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              fill="none"
              viewBox="0 0 24 24"
            >
              <path
                fill="#262626"
                d="M21.647 6.9a1.486 1.486 0 0 0-1.772.355l-3.157 3.403-3.356-7.528v-.01a1.5 1.5 0 0 0-2.724 0v.01l-3.356 7.528-3.157-3.403A1.5 1.5 0 0 0 1.53 8.543l2.126 9.738A1.5 1.5 0 0 0 5.13 19.5h13.74a1.5 1.5 0 0 0 1.474-1.219l2.126-9.738q-.002-.015.007-.03a1.486 1.486 0 0 0-.83-1.613m-2.77 11.07-.006.03H5.129l-.006-.03L3 8.25l.013.015 3.938 4.241a.75.75 0 0 0 1.235-.204L12 3.75l3.815 8.555a.75.75 0 0 0 1.235.204l3.938-4.241L21 8.25z"
              ></path>
            </svg>
          </button>
          <button
            onClick={() => onDelete(jobId)}
            className="text-red-500 hover:text-red-700"
            title="Delete Job"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              fill="none"
              viewBox="0 0 24 24"
            >
              <path
                fill="#262626"
                d="M20.23 11.079a.75.75 0 0 0-.468-.531L14.36 8.522l1.374-6.875a.75.75 0 0 0-1.283-.656l-10.5 11.25a.75.75 0 0 0 .28 1.219l5.404 2.026-1.371 6.867a.75.75 0 0 0 1.283.656l10.5-11.25a.75.75 0 0 0 .182-.68m-9.977 8.984.982-4.911a.75.75 0 0 0-.469-.85l-4.954-1.86 7.934-8.5-.981 4.91a.75.75 0 0 0 .469.85l4.95 1.857z"
              ></path>
            </svg>
          </button>
        </div>
      </div>
      <div className="flex gap-4 flex-wrap mt-6">
        <div className="bg-offWhite-100 text-orange-100 px-6 py-3 rounded-full">{jobType}</div>
        <div className="bg-white text-gray-100 border border-gray-100 px-6 py-3 rounded-full">
          {cityName}
        </div>
        <div className="bg-white text-gray-100 border border-gray-100 px-6 py-3 rounded-full">
          {salaryRange}
        </div>
        {isJobActive && viewJob && (
          <div className="ml-auto">
            <PrimaryButton text="View Job" link={`/jobs/${jobId}`} />
          </div>
        )}
      </div>
    </div>
  );
};

export default CompanyJobCard;
