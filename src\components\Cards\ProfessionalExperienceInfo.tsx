import React from "react";
import { DeleteIcon, EditIcon } from "../Icons";

interface IProfessionalExperienceInfo {
  title: string;
  date: string;
  text: string;
  icon: React.ReactNode;
  onEdit: () => void;
  onDelete: () => void;
}

const ProfessionalExperienceInfo = ({
  title,
  date,
  text,
  icon,
  onEdit,
  onDelete,
}: IProfessionalExperienceInfo) => {
  return (
    <div className="flex flex-wrap sm:flex-nowrap gap-y-3 shadow-lg border rounded-[20px] sm:p-10 p-5">
      <div className="mr-5">{icon}</div>
      <div>
        <h3 className="text-black-100 text-2xl font-bold mb-2">{title}</h3>
        <p className="text-gray-100 text-lg font-medium mb-2">{date}</p>
        <p className="text-black-100 font-medium">{text}</p>
      </div>
      <div className="ml-auto flex gap-x-3">
        <button
          className="text-gray-100 w-[40px] h-[40px] rounded-full bg-gray-300 flex justify-center items-center"
          onClick={onEdit}
        >
          <EditIcon />
        </button>
        <button
          className="text-gray-100 w-[40px] h-[40px] rounded-full bg-gray-300 flex justify-center items-center"
          onClick={onDelete}
        >
          <DeleteIcon />
        </button>
      </div>
    </div>
  );
};

export default ProfessionalExperienceInfo;
