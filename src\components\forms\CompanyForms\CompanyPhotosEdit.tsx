"use client";

import { useQueryClient } from "@tanstack/react-query";
import React, { useState, useEffect } from "react";
import { toast } from "sonner";
import UploadPortfolio from "@/components/Cards/UploadPortfolio";
import { useUploadCompanyPhotos, useDeletePhoto } from "@/hooks/useMutation";
import { useGetCompanyProfile } from "@/hooks/useQuery";

interface PhotoItem {
  id: string; // Unique identifier for the photo
  file?: File; // File object for newly uploaded photos
  previewUrl: string; // URL for displaying the photo
  s3Key?: string; // S3 key for deletion
  isNew?: boolean; // Indicates if the photo is newly added
}

const CompanyPhotosEdit = () => {
  const queryClient = useQueryClient();
  const { data: companyData, isLoading: isProfileLoading } = useGetCompanyProfile();
  const companyProfile = companyData?.data;

  // Convert existing photos to PhotoItem format
  const [photos, setPhotos] = useState<PhotoItem[]>([]);
  // const [isUploading, setIsUploading] = useState(false);
  const { mutateAsync: uploadPhotos } = useUploadCompanyPhotos();
  const { mutate: deletePhoto } = useDeletePhoto();

  // Sync photos state with companyProfile data
  useEffect(() => {
    if (companyProfile?.companyPhotos) {
      setPhotos(
        companyProfile.companyPhotos.map((photo, index) => ({
          id: photo.s3Key || `temp-${index}`, // Use s3Key or a temporary ID
          s3Key: photo.s3Key || "",
          previewUrl: photo.url,
          isNew: false,
        }))
      );
    }
  }, [companyProfile]);

  const handlePhotoUpload = async (file: File, previewUrl: string) => {
    const newPhoto: PhotoItem = {
      id: Date.now().toString(),
      file,
      previewUrl,
      isNew: true,
    };
    setPhotos((prev) => [...prev, newPhoto]);

    try {
      const uploadedPhotos = await uploadPhotos([file]); // Upload the photo immediately
      toast.success("Photo uploaded successfully");

      // Update the photo with the S3 key after successful upload
      setPhotos((prev) =>
        prev.map((photo) =>
          photo.id === newPhoto.id
            ? { ...photo, s3Key: uploadedPhotos.data[0]?.s3Key, isNew: false }
            : photo
        )
      );

      // Invalidate the query to refresh the list of photos
      queryClient.invalidateQueries({ queryKey: ["get-company-profile"] });
    } catch (error) {
      console.error(error);
      toast.error("Failed to upload photo");
    }
  };

  const handlePhotoDelete = (photoId: string, s3Key?: string) => {
    if (!s3Key) {
      // If the photo is not uploaded yet, just remove it from the list
      setPhotos((prev) => prev.filter((photo) => photo.id !== photoId));
      return;
    }

    deletePhoto(
      { s3Key },
      {
        onSuccess: () => {
          toast.success("Photo deleted successfully");
          setPhotos((prev) => prev.filter((photo) => photo.id !== photoId));
          queryClient.invalidateQueries({ queryKey: ["get-company-profile"] }); // Refresh the list of photos
        },
        onError: (error) => {
          toast.error(error.response?.data?.message || "Failed to delete photo");
        },
      }
    );
  };

  // const handleUpdatePhotos = async () => {
  //   setIsUploading(true);
  //   try {
  //     // Extract only new photos with file objects
  //     const newPhotos = photos
  //       .filter((photo) => photo.isNew && photo.file)
  //       .map((photo) => photo.file);

  //     if (newPhotos.length > 0) {
  //       await uploadPhotos(newPhotos as File[]); // Send array of File objects

  //       // Invalidate queries to refresh company profile
  //       queryClient.invalidateQueries({ queryKey: ["get-company-profile"] });
  //       toast.success("Photos updated successfully");
  //     } else {
  //       toast.info("No new photos to upload");
  //     }
  //   } catch (error) {
  //     console.error(error);
  //     toast.error("Failed to update photos");
  //   } finally {
  //     setIsUploading(false);
  //   }
  // };

  if (isProfileLoading) {
    return (
      <div className="text-center py-8">
        <div className="animate-spin rounded-full h-10 w-10 border-b-2 border-gray-900 mx-auto"></div>
        <p className="text-gray-500 mt-4">Loading company photos...</p>
      </div>
    );
  }

  return (
    <div>
      <h2 className="text-blue-200 text-2xl font-bold">Company</h2>
      <h3 className="text-4xl font-medium text-black-100 mt-3 mb-6">Company Photos</h3>
      <p className="mb-4">Upload Photos</p>
      <div className="grid xl:grid-cols-4 lg:grid-cols-2 md:grid-cols-2 gap-5 mb-10">
        {photos.map((photo) => (
          <div key={photo.id}>
            <UploadPortfolio
              image={photo.previewUrl}
              onDelete={() => handlePhotoDelete(photo.id, photo.s3Key)}
              isPreview={photo.isNew}
            />
          </div>
        ))}
        {photos.length < 8 && (
          <div>
            <UploadPortfolio onUpload={handlePhotoUpload} />
          </div>
        )}
      </div>

      {/* <div className="mt-6">
        <button
          disabled={isUploading}
          onClick={handleUpdatePhotos}
          className="bg-orange-100 text-white rounded-full px-10 py-3 disabled:opacity-70 disabled:cursor-not-allowed"
        >
          {isUploading ? "Updating Photos..." : "Update Photos"}
        </button>
      </div> */}
    </div>
  );
};

export default CompanyPhotosEdit;
