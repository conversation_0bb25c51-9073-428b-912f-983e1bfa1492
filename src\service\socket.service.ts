import { io, Socket } from "socket.io-client";
import { debugLog, debugError } from "@/lib/debug";
import { useUserStore } from "@/store/useUserStore";
import { IMediaFile, IMessage } from "@/types/query.types";

// Define socket event types for better type safety
export interface SocketJoinConversationResponse {
  conversationId: string;
  autoJoined?: boolean;
  conversationCount?: number;
  userId?: string;
  userName?: string;
  isDirectMessage?: boolean;
}

export interface SocketTypingEvent {
  conversationId: string;
  userName: string;
  userId?: string;
  isTyping?: boolean;
}

export interface SocketMessageReadEvent {
  messageId: string;
  readBy: string;
  readAt: string;
}

export interface SocketNotificationEvent {
  notificationId: string;
  userId: string;
  title: string;
  message: string;
  type: string;
  isRead: boolean;
  metadata?: {
    jobId?: string;
    applicationId?: string;
  };
  createdAt: string;
}

export interface SocketNotificationReadEvent {
  notificationId: string;
  userId: string;
}

export interface SocketAllNotificationsReadEvent {
  userId: string;
  count: number;
}

export interface SocketMessageDeletedEvent {
  messageId: string;
  conversationId: string;
  userId?: string;
  deleteForEveryone?: boolean;
  deletedBy?: string; // Added to match the example
}

export interface SocketConversationDeletedEvent {
  conversationId: string;
  userId: string;
}

export interface SocketCreateConversationEvent {
  conversationId: string;
  jobSeekerId?: string;
  isDirectMessage?: boolean;
}

export interface SocketLeaveConversationEvent {
  conversationId: string;
  userId?: string;
  userName?: string;
}

export interface SocketSendMessagePayload {
  conversationId: string;
  content: string;
  mediaFiles?: IMediaFile[];
}

// Socket connection status
export enum SocketConnectionStatus {
  DISCONNECTED = "disconnected",
  CONNECTING = "connecting",
  CONNECTED = "connected",
  ERROR = "error",
}

// Singleton socket instance
let socket: Socket | null = null;
let reconnectTimer: NodeJS.Timeout | null = null;
let connectionStatus: SocketConnectionStatus = SocketConnectionStatus.DISCONNECTED;

/**
 * Get the current socket connection status
 * @returns The current connection status
 */
export const getSocketConnectionStatus = (): SocketConnectionStatus => {
  return connectionStatus;
};

/**
 * Initialize the socket connection
 * @param token The authentication token
 * @returns The socket instance
 */
export const initializeSocket = (token: string): Socket => {
  // Update connection status
  connectionStatus = SocketConnectionStatus.CONNECTING;

  if (socket && socket.connected) {
    connectionStatus = SocketConnectionStatus.CONNECTED;
    return socket;
  }

  // If socket exists but is disconnected, try to reconnect
  if (socket) {
    try {
      socket.connect();
      return socket;
    } catch (error) {
      debugError("Failed to reconnect socket:", error);
      // Continue to create a new socket
    }
  }

  if (!token) {
    debugError("No token available for socket connection");
    connectionStatus = SocketConnectionStatus.ERROR;
    throw new Error("No token available for socket connection");
  }

  // Connect to the socket server
  const socketUrl = "https://node.hostingladz.com:3127/messages";

  try {
    // Log token for debugging (only first few characters)
    if (process.env.NODE_ENV === "development") {
      debugLog(`Initializing socket with token: ${token.substring(0, 10)}...`);
    }

    // According to the documentation, we should use the auth object with token
    socket = io(socketUrl, {
      auth: {
        token: token, // Send token in auth object as specified in the documentation
      },
      transports: ["websocket", "polling"], // Allow both WebSocket and polling
      reconnection: true,
      // reconnectionAttempts: 10,
      // reconnectionDelay: 1000,
      // reconnectionDelayMax: 5000,
      // timeout: 30000,
      autoConnect: true, // Ensure auto-connect is enabled
    });

    // Set up event listeners
    socket.on("connect", () => {
      debugLog("Connected to messaging socket");
      connectionStatus = SocketConnectionStatus.CONNECTED;

      // Log detailed connection info
      debugLog("Socket connection details:", {
        id: socket?.id,
        connected: socket?.connected,
        disconnected: socket?.disconnected,
        auth: socket?.auth,
      });

      // Add a global listener for the new_message event
      // This ensures we always have at least one listener for this event
      if (socket && !socket.hasListeners("new_message")) {
        debugLog("Adding global new_message listener on connect");
        socket.on("new_message", (message) => {
          debugLog("Global new_message event received:", message);
        });
      }

      // Clear any reconnect timer when successfully connected
      if (reconnectTimer) {
        clearInterval(reconnectTimer);
        reconnectTimer = null;
      }
    });

    socket.on("connect_error", (error) => {
      debugError("Socket connection error:", error);
      connectionStatus = SocketConnectionStatus.ERROR;

      // Try to reconnect manually after a short delay
      if (!reconnectTimer) {
        reconnectTimer = setTimeout(() => {
          debugLog("Attempting manual reconnect after connection error");
          if (socket) {
            socket.connect();
          }
          reconnectTimer = null;
        }, 2000);
      }
    });

    socket.on("error", (error) => {
      debugError("Socket error:", error);
      connectionStatus = SocketConnectionStatus.ERROR;
    });

    socket.on("disconnect", (reason) => {
      debugLog(`Disconnected from messaging socket: ${reason}`);
      connectionStatus = SocketConnectionStatus.DISCONNECTED;

      // If the disconnection was not initiated by the client, try to reconnect
      if (reason !== "io client disconnect" && !reconnectTimer) {
        reconnectTimer = setTimeout(() => {
          debugLog(`Attempting manual reconnect after disconnect: ${reason}`);
          if (socket) {
            socket.connect();
          }
          reconnectTimer = null;
        }, 2000);
      }
    });

    // Add additional reconnection event handlers
    socket.on("reconnect", (attemptNumber) => {
      debugLog(`Socket reconnected after ${attemptNumber} attempts`);
      connectionStatus = SocketConnectionStatus.CONNECTED;
    });

    socket.on("reconnect_attempt", (attemptNumber) => {
      debugLog(`Socket reconnection attempt ${attemptNumber}`);
      connectionStatus = SocketConnectionStatus.CONNECTING;
    });

    socket.on("reconnect_error", (error) => {
      debugError("Socket reconnection error:", error);
      connectionStatus = SocketConnectionStatus.ERROR;
    });

    socket.on("reconnect_failed", () => {
      debugError("Socket reconnection failed");
      connectionStatus = SocketConnectionStatus.ERROR;

      // Try one more manual reconnect
      if (!reconnectTimer) {
        reconnectTimer = setTimeout(() => {
          debugLog("Attempting manual reconnect after reconnection failure");
          if (socket) {
            socket.connect();
          }
          reconnectTimer = null;
        }, 5000);
      }
    });

    return socket;
  } catch (error) {
    debugError("Failed to initialize socket:", error);
    connectionStatus = SocketConnectionStatus.ERROR;
    throw error;
  }
};

/**
 * Get the current socket instance
 * @returns The socket instance or null if not initialized
 */
export const getSocket = (): Socket | null => {
  return socket;
};

/**
 * Check if socket is connected
 * @returns True if socket is connected, false otherwise
 */
export const isSocketConnected = (): boolean => {
  return socket !== null && socket.connected;
};

/**
 * Disconnect the socket
 */
export const disconnectSocket = (): void => {
  if (socket) {
    debugLog("Disconnecting socket");
    socket.disconnect();
    socket = null;
    connectionStatus = SocketConnectionStatus.DISCONNECTED;
  }
};

/**
 * Join a conversation
 * @param conversationId The ID of the conversation to join
 * @returns Promise that resolves when the conversation is joined
 */
export const joinConversation = (conversationId: string): Promise<boolean> => {
  return new Promise((resolve, reject) => {
    if (!socket || !socket.connected) {
      debugError("Socket not connected. Cannot join conversation. Attempting to reconnect...");

      // Try to reconnect the socket
      const token = useUserStore.getState().token;
      if (token) {
        try {
          debugLog("Initializing socket for joining conversation");
          socket = initializeSocket(token);

          // Force reconnect if not connected
          if (socket && !socket.connected) {
            debugLog("Socket still not connected after initialization, forcing reconnect");
            socket.connect();
          }
        } catch (error) {
          debugError("Failed to initialize socket for joining conversation:", error);
          reject(new Error("Socket initialization failed"));
          return;
        }
      } else {
        debugError("Socket not connected and no token available. Cannot join conversation.");
        reject(new Error("Socket not connected and no token available"));
        return;
      }
    }

    try {
      // At this point, we know socket is not null
      const currentSocket = socket!;

      // Set up a one-time listener for the join_conversation event
      const handleJoinConversation = (data: SocketJoinConversationResponse) => {
        if (data.conversationId === conversationId) {
          debugLog(`Successfully joined conversation: ${conversationId}`);
          currentSocket.off("join_conversation", handleJoinConversation);
          resolve(true);
        }
      };

      // Listen for the join_conversation event
      currentSocket.on("join_conversation", handleJoinConversation);

      // Set up a timeout to prevent hanging if the server doesn't respond
      const timeoutId = setTimeout(() => {
        debugLog(
          `Timeout waiting for join confirmation for conversation ${conversationId}, assuming success`
        );
        currentSocket.off("join_conversation", handleJoinConversation);
        resolve(true); // Assume success after timeout to prevent blocking UI
      }, 2000); // 2 second timeout

      // Define the expected response type
      interface JoinConversationResponse {
        success?: boolean;
        error?: string;
        message?: string;
        conversationId?: string;
      }

      // Emit the join_conversation event with acknowledgment callback
      currentSocket.emit(
        "join_conversation",
        { conversationId },
        (response: JoinConversationResponse | null) => {
          clearTimeout(timeoutId);
          currentSocket.off("join_conversation", handleJoinConversation);

          debugLog(`Joined conversation with acknowledgement: ${conversationId}`, response);
          resolve(true);
        }
      );

      debugLog(`Joining conversation: ${conversationId}`);
    } catch (error) {
      debugError("Failed to join conversation:", error);
      reject(error);
    }
  });
};

/**
 * Send a message via socket
 * @param payload The message payload
 * @returns Promise that resolves when the message is sent
 */
export const sendMessage = (payload: SocketSendMessagePayload): Promise<boolean> => {
  return new Promise<boolean>((resolvePromise, reject) => {
    if (!socket || !socket.connected) {
      debugError("Socket not connected. Cannot send message. Attempting to reconnect...");
      try {
        const token = useUserStore.getState().token;
        if (token) {
          socket = initializeSocket(token);
          debugLog("Socket reconnected for sending message");
        } else {
          reject(new Error("No token available for socket connection"));
          return;
        }
      } catch (reconnectError) {
        debugError("Failed to reconnect socket for sending message:", reconnectError);
        reject(new Error("Socket reconnection failed"));
        return;
      }
    }

    try {
      const currentSocket = socket!;
      let messageReceived = false;
      let messageListener: ((message: IMessage) => void) | null = null;
      let timeoutId: NodeJS.Timeout | null = null;

      // Cleanup function
      const cleanup = () => {
        if (messageListener) {
          currentSocket.off("new_message", messageListener);
          messageListener = null;
        }
        if (timeoutId) {
          clearTimeout(timeoutId);
          timeoutId = null;
        }
      };

      // Message listener
      messageListener = (message: {
        _id?: string;
        conversationId?: string;
        content?: string;
        senderId?: string;
        senderType?: string;
        createdAt?: string;
        mediaFiles?: IMediaFile[];
      }) => {
        // Check if this is the message we sent
        const isMatchingMessage =
          message.conversationId === payload.conversationId &&
          message.content === payload.content &&
          // Check media files match
          ((!payload.mediaFiles?.length && !message.mediaFiles?.length) || // No media in either
            (payload.mediaFiles?.length === message.mediaFiles?.length && // Same number of media files
              payload.mediaFiles?.every(
                (media, index) =>
                  media.url === message.mediaFiles?.[index]?.url &&
                  media.s3Key === message.mediaFiles?.[index]?.s3Key
              )));

        if (!messageReceived && isMatchingMessage) {
          debugLog("Received matching new_message after send_message:", message);
          messageReceived = true;
          cleanup();
        }
      };

      // Add the listener before sending the message
      currentSocket.on("new_message", messageListener);

      // Set timeout
      timeoutId = setTimeout(() => {
        if (!messageReceived) {
          debugLog("Message receive timeout, cleaning up");
          cleanup();
        }
      }, 3000);

      // First ensure we're in the conversation
      joinConversation(payload.conversationId)
        .then(() => {
          debugLog(`Successfully joined conversation ${payload.conversationId}`);

          // Send message immediately after joining
          currentSocket.emit("send_message", payload, (acknowledgement: { success: boolean }) => {
            if (acknowledgement?.success) {
              debugLog("Message sent successfully with acknowledgement");
              resolvePromise(true);
            } else {
              debugError("Failed to send message with acknowledgement");
              resolvePromise(false);
            }
          });
        })
        .catch((error) => {
          debugError(`Failed to join conversation ${payload.conversationId}:`, error);
          cleanup();
          resolvePromise(false);
        });
    } catch (error) {
      debugError("Error in sendMessage:", error);
      reject(error);
    }
  });
};

/**
 * Mark a message as read
 * @param messageId The ID of the message to mark as read
 * @returns Promise that resolves when the message is marked as read
 */
export const markAsRead = (messageId: string): Promise<boolean> => {
  return new Promise((resolve, reject) => {
    if (!socket || !socket.connected) {
      debugError("Socket not connected. Cannot mark message as read.");
      reject(new Error("Socket not connected"));
      return;
    }

    try {
      // Prepare the payload according to the documentation
      const markAsReadPayload = {
        messageId,
      };

      // Set up a timeout to prevent hanging if the server doesn't respond
      const timeoutId = setTimeout(() => {
        debugLog(
          `Timeout waiting for read confirmation for message ${messageId}, assuming success`
        );
        resolve(true); // Assume success after timeout to prevent blocking UI
      }, 2000); // 2 second timeout

      // Define the expected response type
      interface MarkAsReadResponse {
        success?: boolean;
        error?: string;
        message?: string;
      }

      // Track if we've already resolved this promise
      let isResolved = false;

      // Set up a one-time listener for the message_read event
      const handleMessageRead = (data: { messageId: string; readBy: string; readAt: string }) => {
        if (data.messageId === messageId && !isResolved && socket) {
          debugLog(`Message ${messageId} was read by ${data.readBy} at ${data.readAt}`);
          clearTimeout(timeoutId);
          socket.off("message_read", handleMessageRead);
          isResolved = true;
          resolve(true);
        }
      };

      // Listen for the message_read event
      socket.on("message_read", handleMessageRead);

      // Emit the mark_as_read event with acknowledgment callback
      socket.emit("mark_as_read", markAsReadPayload, (response: MarkAsReadResponse | null) => {
        if (!isResolved && socket) {
          clearTimeout(timeoutId);
          socket.off("message_read", handleMessageRead);
          isResolved = true;

          if (response && typeof response === "object" && response.success === false) {
            debugError(
              `Error marking message ${messageId} as read: ${response.error || "Unknown error"}`
            );
            resolve(false);
          } else {
            debugLog(`Message ${messageId} was marked as read successfully (ack)`);
            resolve(true);
          }
        }
      });

      debugLog(`Sent mark_as_read event for message ${messageId}`);
    } catch (error) {
      debugError("Failed to mark message as read:", error);
      // Resolve with false instead of rejecting to prevent UI blocking
      resolve(false);
    }
  });
};

/**
 * Set typing indicator for a conversation
 * @param conversationId The ID of the conversation
 * @param isTyping Whether the user is typing
 * @returns Promise that resolves when the typing indicator is set
 */
export const setTyping = (conversationId: string, isTyping: boolean): Promise<boolean> => {
  return new Promise((resolve, reject) => {
    if (!socket || !socket.connected) {
      debugError("Socket not connected. Cannot set typing indicator.");
      reject(new Error("Socket not connected"));
      return;
    }

    try {
      // Get the current user's information for the typing payload
      const { currentUser } = useUserStore.getState();

      if (!currentUser) {
        debugError("No current user found. Cannot set typing indicator.");
        reject(new Error("No current user found"));
        return;
      }

      // Create the typing payload with user information
      const typingPayload = {
        conversationId,
        isTyping,
        userId: currentUser._id,
      };

      // Emit the typing event with the complete payload
      socket.emit("typing", typingPayload);

      debugLog(
        `Emitted typing event: ${isTyping ? "started typing" : "stopped typing"} in conversation ${conversationId}`
      );

      resolve(true);
    } catch (error) {
      debugError("Failed to set typing indicator:", error);
      reject(error);
    }
  });
};

/**
 * Delete a message using socket
 * @param messageId The ID of the message to delete
 * @param deleteForEveryone Whether to delete the message for everyone
 * @returns Promise that resolves when the message is deleted
 */
export const deleteMessageSocket = (
  messageId: string,
  deleteForEveryone: boolean = false
): Promise<boolean> => {
  return new Promise((resolve, reject) => {
    if (!socket || !socket.connected) {
      debugError("Socket not connected. Cannot delete message.");
      reject(new Error("Socket not connected"));
      return;
    }

    try {
      debugLog(`Emitting delete_message event for message ${messageId}`);

      // Emit the delete_message event as per the example
      socket.emit("delete_message", { messageId, deleteForEveryone });

      // Since we're using the socket event pattern, we'll resolve immediately
      // The actual confirmation will come via the message_deleted event
      // which we're listening for separately
      debugLog(`Message ${messageId} deletion request sent`);
      resolve(true);
    } catch (error) {
      debugError("Failed to delete message:", error);
      reject(error);
    }
  });
};

/**
 * Set up a listener for the message_deleted event
 * @param callback The callback to execute when a message is deleted
 * @returns A cleanup function to remove the listener
 */
export const listenForMessageDeleted = (
  callback: (data: SocketMessageDeletedEvent) => void
): (() => void) => {
  if (!socket || !socket.connected) {
    debugError("Socket not connected. Cannot listen for message_deleted events.");
    return () => {}; // Return empty cleanup function
  }

  // Add the listener
  socket.on("message_deleted", callback);
  debugLog("Added message_deleted listener");

  // Return a cleanup function
  return () => {
    if (socket) {
      socket.off("message_deleted", callback);
      debugLog("Removed message_deleted listener");
    }
  };
};

/**
 * Delete a conversation using socket
 * @param conversationId The ID of the conversation to delete
 * @returns Promise that resolves when the conversation is deleted
 */
export const deleteConversationSocket = (conversationId: string): Promise<boolean> => {
  return new Promise((resolve, reject) => {
    if (!socket || !socket.connected) {
      debugError("Socket not connected. Cannot delete conversation.");
      reject(new Error("Socket not connected"));
      return;
    }

    try {
      // Define the expected response type
      interface DeleteConversationResponse {
        success?: boolean;
        error?: string;
        message?: string;
      }

      // Set up a timeout to prevent hanging if the server doesn't respond
      const timeoutId = setTimeout(() => {
        debugLog(
          `Timeout waiting for delete confirmation for conversation ${conversationId}, assuming success`
        );
        resolve(true); // Assume success after timeout to prevent blocking UI
      }, 2000); // 2 second timeout

      // Set up a one-time listener for the delete_conversation event
      const handleDeleteConversation = (data: SocketConversationDeletedEvent) => {
        if (data.conversationId === conversationId) {
          debugLog(`Received delete_conversation event for conversation ${conversationId}`);
          clearTimeout(timeoutId);
          socket?.off("delete_conversation", handleDeleteConversation);
          resolve(true);
        }
      };

      // Listen for the delete_conversation event
      socket.on("delete_conversation", handleDeleteConversation);

      // Emit the delete_conversation event with acknowledgment callback
      socket.emit(
        "delete_conversation", // This is the event name used in the socket server
        {
          conversationId,
        },
        (response: DeleteConversationResponse | null) => {
          clearTimeout(timeoutId);
          socket?.off("delete_conversation", handleDeleteConversation);

          if (response && typeof response === "object" && response.success === false) {
            debugError(
              `Error deleting conversation ${conversationId}: ${response.error || "Unknown error"}`
            );
            resolve(false);
          } else {
            debugLog(`Conversation ${conversationId} was deleted successfully`);
            resolve(true);
          }
        }
      );

      debugLog(`Sent delete_conversation event for conversation ${conversationId}`);
    } catch (error) {
      debugError("Failed to delete conversation:", error);
      // Resolve with false instead of rejecting to prevent UI blocking
      resolve(false);
    }
  });
};

/**
 * Leave a conversation
 * @param conversationId The ID of the conversation to leave
 * @returns Promise that resolves when the conversation is left
 */
export const leaveConversation = (conversationId: string): Promise<boolean> => {
  return new Promise((resolve, reject) => {
    if (!socket || !socket.connected) {
      debugError("Socket not connected. Cannot leave conversation.");
      reject(new Error("Socket not connected"));
      return;
    }

    try {
      // Define the expected response type
      interface LeaveConversationResponse {
        success?: boolean;
        error?: string;
        message?: string;
      }

      // Set up a timeout to prevent hanging if the server doesn't respond
      const timeoutId = setTimeout(() => {
        debugLog(
          `Timeout waiting for leave confirmation for conversation ${conversationId}, assuming success`
        );
        resolve(true); // Assume success after timeout to prevent blocking UI
      }, 2000); // 2 second timeout

      // Set up a one-time listener for the leave_conversation event
      const handleLeaveConversation = (data: SocketLeaveConversationEvent) => {
        if (data.conversationId === conversationId) {
          debugLog(`Received leave_conversation event for conversation ${conversationId}`);
          clearTimeout(timeoutId);
          socket?.off("leave_conversation", handleLeaveConversation);
          resolve(true);
        }
      };

      // Listen for the leave_conversation event
      socket.on("leave_conversation", handleLeaveConversation);

      // Emit the leave_conversation event with acknowledgment callback
      socket.emit(
        "leave_conversation", // This is the event name used in the socket server
        {
          conversationId,
        },
        (response: LeaveConversationResponse | null) => {
          clearTimeout(timeoutId);
          socket?.off("leave_conversation", handleLeaveConversation);

          if (response && typeof response === "object" && response.success === false) {
            debugError(
              `Error leaving conversation ${conversationId}: ${response.error || "Unknown error"}`
            );
            resolve(false);
          } else {
            debugLog(`Left conversation ${conversationId} successfully`);
            resolve(true);
          }
        }
      );

      debugLog(`Sent leave_conversation event for conversation ${conversationId}`);
    } catch (error) {
      debugError("Failed to leave conversation:", error);
      // Resolve with false instead of rejecting to prevent UI blocking
      resolve(false);
    }
  });
};

/**
 * Mark a notification as read using socket
 * @param notificationId The ID of the notification to mark as read
 * @returns Promise that resolves when the notification is marked as read
 */
export const markNotificationAsReadSocket = (notificationId: string): Promise<boolean> => {
  return new Promise((resolve, reject) => {
    if (!socket || !socket.connected) {
      debugError("Socket not connected. Cannot mark notification as read.");
      reject(new Error("Socket not connected"));
      return;
    }

    try {
      // Emit the notification_read event
      socket.emit("notification_read", { notificationId });
      debugLog(`Sent notification_read event for notification ${notificationId}`);
      resolve(true);
    } catch (error) {
      debugError("Failed to mark notification as read:", error);
      reject(error);
    }
  });
};

/**
 * Mark all notifications as read using socket
 * @returns Promise that resolves when all notifications are marked as read
 */
export const markAllNotificationsAsReadSocket = (): Promise<boolean> => {
  return new Promise((resolve, reject) => {
    if (!socket || !socket.connected) {
      debugError("Socket not connected. Cannot mark all notifications as read.");
      reject(new Error("Socket not connected"));
      return;
    }

    try {
      // Emit the all_notifications_read event
      socket.emit("all_notifications_read", {});
      debugLog("Sent all_notifications_read event");
      resolve(true);
    } catch (error) {
      debugError("Failed to mark all notifications as read:", error);
      reject(error);
    }
  });
};

/**
 * Set up a listener for the new_notification event
 * @param callback The callback to execute when a new notification is received
 * @returns A cleanup function to remove the listener
 */
export const listenForNewNotification = (
  callback: (data: SocketNotificationEvent) => void
): (() => void) => {
  if (!socket || !socket.connected) {
    debugError("Socket not connected. Cannot listen for new_notification events.");
    return () => {}; // Return empty cleanup function
  }

  // Add the listener
  socket.on("new_notification", callback);
  debugLog("Added new_notification listener");

  // Return a cleanup function
  return () => {
    if (socket) {
      socket.off("new_notification", callback);
      debugLog("Removed new_notification listener");
    }
  };
};

/**
 * Set up a listener for the notification_read event
 * @param callback The callback to execute when a notification is read
 * @returns A cleanup function to remove the listener
 */
export const listenForNotificationRead = (
  callback: (data: SocketNotificationReadEvent) => void
): (() => void) => {
  if (!socket || !socket.connected) {
    debugError("Socket not connected. Cannot listen for notification_read events.");
    return () => {}; // Return empty cleanup function
  }

  // Add the listener
  socket.on("notification_read", callback);
  debugLog("Added notification_read listener");

  // Return a cleanup function
  return () => {
    if (socket) {
      socket.off("notification_read", callback);
      debugLog("Removed notification_read listener");
    }
  };
};

/**
 * Set up a listener for the all_notifications_read event
 * @param callback The callback to execute when all notifications are read
 * @returns A cleanup function to remove the listener
 */
export const listenForAllNotificationsRead = (
  callback: (data: SocketAllNotificationsReadEvent) => void
): (() => void) => {
  if (!socket || !socket.connected) {
    debugError("Socket not connected. Cannot listen for all_notifications_read events.");
    return () => {}; // Return empty cleanup function
  }

  // Add the listener
  socket.on("all_notifications_read", callback);
  debugLog("Added all_notifications_read listener");

  // Return a cleanup function
  return () => {
    if (socket) {
      socket.off("all_notifications_read", callback);
      debugLog("Removed all_notifications_read listener");
    }
  };
};

/**
 * Debug function to log socket state
 * @returns Object with socket state information
 */
export const debugSocketState = () => {
  return {
    socketExists: socket !== null,
    connected: socket?.connected || false,
    connectionStatus,
    id: socket?.id || null,
  };
};
