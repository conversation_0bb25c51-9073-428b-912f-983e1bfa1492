"use client";

import Image from "next/image";
import React from "react";
import PrimaryButton from "@/components/Buttons/PrimaryButton";
import { PrimaryHeading } from "@/components/Headings/PrimaryHeading";

const FeaturedJobSuccessPage = () => {
  return (
    <div className="text-center pb-10">
      <div>
        <Image
          src={"/images/completed-image.gif"}
          alt="Success"
          width={500}
          height={500}
          className="mx-auto"
        />
        <PrimaryHeading>
          Thank You! Your Job Is <span>Featured Successfully!</span>
        </PrimaryHeading>
        <p className="text-2xl font-normal text-gray-100 mt-4 leading-9">
          Your job listing has been successfully featured and will now receive increased visibility.
        </p>
        <div className="mt-5 space-x-3">
          <PrimaryButton link="/company-dashboard/all-jobs" text="View All Jobs" />
        </div>
      </div>
    </div>
  );
};

export default FeaturedJobSuccessPage;
