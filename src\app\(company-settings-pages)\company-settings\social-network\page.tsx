"use client";

import React from "react";
import { toast } from "sonner";
import SocialNetworkForm from "@/components/forms/SocialNetworkForm";
import { useGetCompanyProfile } from "@/hooks/useQuery";

export default function SocialNetworkPage() {
  const { data: companyData, isLoading } = useGetCompanyProfile();
  const companyProfile = companyData?.data;

  if (isLoading) {
    return (
      <div className="text-center py-8">
        <div className="animate-spin rounded-full h-10 w-10 border-b-2 border-gray-900 mx-auto"></div>
        <p className="text-gray-500 mt-4">Loading social networks...</p>
      </div>
    );
  }

  if (!companyProfile) {
    toast.error("Failed to load company profile");
    return null;
  }

  return (
    <div>
      <SocialNetworkForm isButton={false} />
    </div>
  );
}
