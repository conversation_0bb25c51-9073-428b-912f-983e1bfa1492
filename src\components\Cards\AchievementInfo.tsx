"use client";
import React from "react";
import { DeleteIcon, EditIcon } from "../Icons";
import { ICompanyAchievement } from "@/types/query.types";

interface IAchievementInfo extends Omit<ICompanyAchievement, "_id"> {
  icon: React.ReactNode;
  onEdit: () => void;
  onDelete: () => void;
}

const AchievementInfo = ({
  title,
  date,
  eventOrInstitute,
  detail,
  icon,
  onEdit,
  onDelete,
}: IAchievementInfo) => {
  return (
    <div className="flex md:flex-nowrap flex-wrap shadow-lg border rounded-[20px] sm:p-10 p-5">
      <div className="mr-5">{icon}</div>
      <div>
        <h3 className="text-black-100 text-2xl font-bold mb-2">{title}</h3>
        <p className="text-gray-100 text-lg font-medium mb-2">{date}</p>
        <h4 className="text-xl text-black-100 font-medium mb-2">{eventOrInstitute}</h4>
        <p className="text-gray-100 text-lg font-medium">{detail}</p>
      </div>
      <div className="ml-auto flex gap-x-3">
        <button
          onClick={onEdit}
          className="text-gray-100 w-[40px] h-[40px] rounded-full bg-gray-300 flex justify-center items-center"
        >
          <EditIcon />
        </button>
        <button
          onClick={onDelete}
          className="text-gray-100 w-[40px] h-[40px] rounded-full bg-gray-300 flex justify-center items-center"
        >
          <DeleteIcon />
        </button>
      </div>
    </div>
  );
};

export default AchievementInfo;
