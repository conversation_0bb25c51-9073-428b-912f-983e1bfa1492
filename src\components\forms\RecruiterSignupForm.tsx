"use client";

import { useQueryClient } from "@tanstack/react-query";
import { Eye, EyeOff } from "lucide-react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { useForm, type SubmitHandler } from "react-hook-form";
import { toast } from "sonner";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useRegisterRecruiter } from "@/hooks/useMutation";
import { useUserStore } from "@/store/useUserStore";
import { UserRole } from "@/types/common.types";
import { IRecruiterRegisterRequestDto } from "@/types/mutation.types";

const inputStyles = "h-12 px-4 rounded-full border border-[#737373] w-full";

export default function RecruiterSignupForm() {
  const [showPassword, setShowPassword] = useState(false);
  const [agreeTerms, setAgreeTerms] = useState(false);
  const queryClient = useQueryClient();
  const router = useRouter();
  const { setToken, setCurrentUser } = useUserStore();

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitted },
    watch,
  } = useForm<IRecruiterRegisterRequestDto>();

  const { mutateAsync: registerMutate, isPending } = useRegisterRecruiter({
    onSuccess: (data) => {
      setToken(data.data.token);
      setCurrentUser(data.data.user);
      toast("Signup successful");
      queryClient.invalidateQueries({ queryKey: ["get-current-user"] });
      router.push("/company-profile-completion");
    },
    onError: (error) => {
      toast(error.response?.data?.message || "Signup failed");
    },
  });

  const onSubmit: SubmitHandler<IRecruiterRegisterRequestDto> = async (data) => {
    await registerMutate({
      ...data,
      confirmPassword: data.password,
      role: UserRole.RECRUITER,
      abn: Number(data.abn),
    });
  };

  const password = watch("password"); // Watch the password field for live validation

  const validatePassword = (password: string) => {
    const minLength = 8;
    const hasUpperCase = /[A-Z]/.test(password);
    const hasLowerCase = /[a-z]/.test(password);
    const hasNumber = /\d/.test(password);
    const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(password);

    const errors = [];
    if (password?.length < minLength) errors.push("At least 8 characters");
    if (!hasUpperCase) errors.push("At least one uppercase letter");
    if (!hasLowerCase) errors.push("At least one lowercase letter");
    if (!hasNumber) errors.push("At least one number");
    if (!hasSpecialChar) errors.push("At least one special character");

    return errors.length > 0 ? errors : true;
  };

  const passwordErrors = validatePassword(password);

  return (
    <>
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="companyName">Company Name*</Label>
          <Input
            id="companyName"
            className={inputStyles}
            placeholder="Enter your company name"
            {...register("companyName", {
              required: "Company name is required",
            })}
          />
          {errors.companyName && (
            <p className="text-red-500 text-sm">{errors.companyName.message}</p>
          )}
        </div>
        <div className="space-y-2">
          <Label htmlFor="companyEmail">Company Email*</Label>
          <Input
            id="companyEmail"
            type="email"
            className={inputStyles}
            placeholder="Enter your company email"
            {...register("companyEmail", {
              required: "Company email is required",
              pattern: {
                value: /\S+@\S+\.\S+/,
                message: "Invalid email address",
              },
            })}
          />
          {errors.companyEmail && (
            <p className="text-red-500 text-sm">{errors.companyEmail.message}</p>
          )}
        </div>
        <div className="mb-6">
          <Label htmlFor="abn" className="mb-3 block">
            ABN (Australian Business Number)
          </Label>
          <Input
            className={inputStyles}
            id="abn"
            maxLength={11}
            {...register("abn", {
              required: "ABN is required",
              pattern: {
                value: /^\d{11}$/,
                message: "ABN must be exactly 11 digits",
              },
              validate: (value) => {
                // Convert to string to check length
                const abnString = String(value);
                if (abnString.length !== 11) {
                  return "ABN must be exactly 11 digits";
                }
                // Check if it contains only digits
                if (!/^\d+$/.test(abnString)) {
                  return "ABN must contain only numbers";
                }
                return true;
              },
            })}
          />
          {errors.abn && <p className="text-red-500 text-sm">{errors.abn.message}</p>}
        </div>
        <div className="space-y-2">
          <Label htmlFor="password">Password*</Label>
          <div className="relative">
            <Input
              id="password"
              type={showPassword ? "text" : "password"}
              className={inputStyles}
              placeholder="Enter your password"
              {...register("password", {
                required: "Password is required",
                validate: (value) => validatePassword(value) === true || "Password is too weak",
              })}
            />
            <button
              type="button"
              onClick={() => setShowPassword(!showPassword)}
              className="absolute right-4 top-1/2 -translate-y-1/2 text-gray-500 hover:text-gray-700"
            >
              {showPassword ? <EyeOff size={20} /> : <Eye size={20} />}
            </button>
          </div>
          {errors.password && <p className="text-red-500 text-sm">{errors.password.message}</p>}
          {isSubmitted && Array.isArray(passwordErrors) && (
            <ul className="text-red-500 text-sm mt-2">
              {passwordErrors.map((error, index) => (
                <li key={index}>• {error}</li>
              ))}
            </ul>
          )}
        </div>
        <div className="flex items-center space-x-2">
          <Checkbox
            id="agreeTerms"
            className="border-orange-100"
            checked={agreeTerms}
            onCheckedChange={(checked) => setAgreeTerms(!!checked)}
          />
          <Label htmlFor="agreeTerms" className="text-sm">
            I agree with the{" "}
            <Link href={"/terms-and-conditions"} className="text-orange-100">
              Terms & Conditions
            </Link>{" "}
            &{" "}
            <Link href={"/privacy-policy"} className="text-orange-100">
              Privacy Policy
            </Link>
          </Label>
        </div>
        <Button
          type="submit"
          className="w-full bg-orange-100 text-base h-[48px] rounded-full"
          disabled={isPending || !agreeTerms}
        >
          {isPending ? "Signing up..." : "Sign Up"}
        </Button>
      </form>
      <div className="text-center mt-8">
        <p className="text-base text-gray-100 font-medium">
          Don&apos;t have an account?{" "}
          <Link className="text-orange-100" href={"/login"}>
            Log In
          </Link>{" "}
        </p>
      </div>
    </>
  );
}
