import Image from "next/image";
import React from "react";
import { DeleteIcon, EditIcon } from "../Icons";

interface ISocialNetworkCard {
  title: string;
  text: string;
  image: string;
  onDelete: () => void; // Callback function to handle delete action
}

const SocialNetworkCard = ({ title, text, image, onDelete }: ISocialNetworkCard) => {
  return (
    <div className="flex flex-wrap gap-x-5 gap-y-3 shadow-lg border rounded-[20px] sm:p-10 p-5">
      <div>
        <Image
          className="w-[52px] h-[52px] object-cover rounded-full"
          src={image}
          alt=""
          width={52}
          height={52}
        />
      </div>
      <div>
        <h3 className="text-black-100 text-2xl font-bold mb-2">{title}</h3>
        <p className="text-black-100 font-medium overflow-wrap-anywhere"> {text} </p>
      </div>
      <div className="ml-auto flex gap-x-3">
        <button className="text-gray-100 w-[40px] h-[40px] rounded-full bg-gray-300 flex justify-center items-center">
          <EditIcon />
        </button>
        <button
          onClick={onDelete}
          className="text-gray-100 w-[40px] h-[40px] rounded-full bg-gray-300 flex justify-center items-center"
        >
          <DeleteIcon />
        </button>
      </div>
    </div>
  );
};

export default SocialNetworkCard;
