"use client";

import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Trash2 } from "lucide-react";
import { useState } from "react";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useDeleteMessage } from "@/hooks/useMutation";

interface MessageActionsProps {
  messageId: string;
  conversationId: string;
  isCurrentUser: boolean;
}

export default function MessageActions({
  messageId,
  conversationId,
  isCurrentUser,
}: MessageActionsProps) {
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [deleteForEveryone, setDeleteForEveryone] = useState(false);

  // Using the updated useDeleteMessage hook that now uses socket events
  const { mutate: deleteMessage, isPending: isDeleting } = useDeleteMessage();

  // Only show actions for the current user's messages
  if (!isCurrentUser) return null;

  const handleDelete = () => {
    deleteMessage(
      {
        messageId,
        conversationId,
        deleteForEveryone,
      },
      {
        onSuccess: () => {
          setIsDeleteDialogOpen(false);
        },
        onError() {
          setIsDeleteDialogOpen(false);
        },
      }
    );
  };

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="ghost"
            size="sm"
            className="h-6 w-6 p-0 rounded-full opacity-0 group-hover:opacity-100 transition-opacity"
          >
            <MoreHorizontal className="h-4 w-4" />
            <span className="sr-only">Message actions</span>
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuItem
            className="text-destructive flex items-center"
            onClick={() => setIsDeleteDialogOpen(true)}
          >
            <Trash2 className="h-4 w-4 mr-2" />
            Delete
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      {/* Delete confirmation dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Message</DialogTitle>
            <DialogDescription>Are you sure you want to delete this message?</DialogDescription>
          </DialogHeader>

          <div className="flex items-center space-x-2 py-2">
            <input
              type="checkbox"
              id="deleteForEveryone"
              checked={deleteForEveryone}
              onChange={(e) => setDeleteForEveryone(e.target.checked)}
              className="rounded border-gray-300"
            />
            <label htmlFor="deleteForEveryone">Delete for everyone</label>
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsDeleteDialogOpen(false)}
              disabled={isDeleting}
            >
              Cancel
            </Button>
            <Button variant="destructive" onClick={handleDelete} disabled={isDeleting}>
              {isDeleting ? "Deleting..." : "Delete"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}
