import React from "react";
import { cn } from "@/lib/utils"; // Assuming you're using shadcn/ui utility

interface PrimaryHeadingProps {
  children: React.ReactNode;
  className?: string;
}

// Colors as constants to maintain consistency
const COLORS = {
  heading: "#262626",
  highlight: "#EC761E",
} as const;

/**
 * PrimaryHeading Component
 * 
 * A heading component that applies consistent styling and allows for highlighted spans
 * within the text. Any child <span> elements will be automatically highlighted.
 * 
 * @param {React.ReactNode} children - The content to be rendered within the heading
 * @param {string} [className] - Additional CSS classes to be applied
 * 
 * @example
 * <PrimaryHeading>
 *   Welcome to <span>our service</span>
 * </PrimaryHeading>
 */
export function PrimaryHeading({ children, className }: PrimaryHeadingProps) {
  // Base styles for the heading
  const baseStyles = "font-bold text-3xl lg:text-5xl lg:leading-[66px] tracking-[-0.04em]";

  // Process children to handle span elements
  const processedChildren = React.Children.map(children, (child) => {
    // Check if the child is a valid span element
    if (React.isValidElement(child) && child.type === "span") {
      // Type assertion to access props
      const spanElement = child as React.ReactElement<{ className?: string }>;
      
      return React.cloneElement(spanElement, {
        className: cn(
          "text-[#EC761E]",
          spanElement.props.className
        ),
      });
    }
    return child;
  });

  return (
    <h2
      className={cn(
        baseStyles,
        `text-[${COLORS.heading}]`,
        className
      )}
    >
      {processedChildren}
    </h2>
  );
}