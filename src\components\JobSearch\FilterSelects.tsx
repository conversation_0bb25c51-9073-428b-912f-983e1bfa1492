"use client";

import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useGetAllEnums } from "@/hooks/useQuery";
import type { IJobSearchParams } from "@/types/query.types";

import type { IJobFilterState } from "@/types/query.types";

interface FilterSelectsProps {
  filters: IJobFilterState;
  onFilterChange: (key: keyof IJobSearchParams, value: string | undefined) => void;
}

export function FilterSelects({ filters, onFilterChange }: FilterSelectsProps) {
  const { data: enumsData } = useGetAllEnums();

  const handleValueChange = (key: keyof IJobSearchParams, value: string) => {
    onFilterChange(key, value === "all" ? undefined : value);
  };

  const jobTypes = enumsData?.data.JOB_TYPE_ENUM
    ? Object.entries(enumsData.data.JOB_TYPE_ENUM).map(([key, _value]) => ({
        label: key.replace(/_/g, " "),
        value: key,
      }))
    : [];

  const experienceLevels = enumsData?.data.EXPERIENCE_RANGE_ENUM
    ? Object.entries(enumsData.data.EXPERIENCE_RANGE_ENUM).map(([key, _value]) => ({
        label: key.replace(/_/g, " "),
        value: key,
      }))
    : [];

  const careerLevels = enumsData?.data.CAREER_LEVEL_ENUM
    ? Object.entries(enumsData.data.CAREER_LEVEL_ENUM).map(([key, _value]) => ({
        label: key.replace(/_/g, " "),
        value: key,
      }))
    : [];

  const qualifications = enumsData?.data.QUALIFICATION_ENUM
    ? Object.entries(enumsData.data.QUALIFICATION_ENUM).map(([key, _value]) => ({
        label: key.replace(/_/g, " "),
        value: key,
      }))
    : [];

  const salaryTypes = enumsData?.data.SALARY_TYPE_ENUM
    ? Object.entries(enumsData.data.SALARY_TYPE_ENUM).map(([key, _value]) => ({
        label: key.replace(/_/g, " "),
        value: key,
      }))
    : [];

  return (
    <div className="flex overflow-x-auto gap-4 lg:p-4 w-full ">
      <Select
        value={filters.jobType || "all"}
        onValueChange={(value) => handleValueChange("jobType", value)}
      >
        <SelectTrigger className="w-full bg-white rounded-full h-[56px] px-6 text-gray-100 text-base">
          <SelectValue placeholder="Job Type" />
        </SelectTrigger>
        <SelectContent>
          <SelectGroup>
            <SelectItem value="all">All Job Types</SelectItem>
            {jobTypes.map(({ label, value }) => (
              <SelectItem key={value} value={value}>
                {label}
              </SelectItem>
            ))}
          </SelectGroup>
        </SelectContent>
      </Select>

      <Select
        value={filters.experienceLevel || "all"}
        onValueChange={(value) => handleValueChange("experienceLevel", value)}
      >
        <SelectTrigger className="w-full bg-white rounded-full h-[56px] px-6 text-gray-100 text-base">
          <SelectValue placeholder="Experience Level" />
        </SelectTrigger>
        <SelectContent>
          <SelectGroup>
            <SelectItem value="all">All Experience Levels</SelectItem>
            {experienceLevels.map(({ label, value }) => (
              <SelectItem key={value} value={value}>
                {label}
              </SelectItem>
            ))}
          </SelectGroup>
        </SelectContent>
      </Select>

      <Select
        value={filters.careerLevel || "all"}
        onValueChange={(value) => handleValueChange("careerLevel", value)}
      >
        <SelectTrigger className="w-full bg-white rounded-full h-[56px] px-6 text-gray-100 text-base">
          <SelectValue placeholder="Career Level" />
        </SelectTrigger>
        <SelectContent>
          <SelectGroup>
            <SelectItem value="all">All Career Levels</SelectItem>
            {careerLevels.map(({ label, value }) => (
              <SelectItem key={value} value={value}>
                {label}
              </SelectItem>
            ))}
          </SelectGroup>
        </SelectContent>
      </Select>

      <Select
        value={filters.qualification || "all"}
        onValueChange={(value) => handleValueChange("qualification", value)}
      >
        <SelectTrigger className="w-full bg-white rounded-full h-[56px] px-6 text-gray-100 text-base">
          <SelectValue placeholder="Qualification" />
        </SelectTrigger>
        <SelectContent>
          <SelectGroup>
            <SelectItem value="all">All Qualifications</SelectItem>
            {qualifications.map(({ label, value }) => (
              <SelectItem key={value} value={value}>
                {label}
              </SelectItem>
            ))}
          </SelectGroup>
        </SelectContent>
      </Select>

      <Select
        value={filters.salaryType || "all"}
        onValueChange={(value) => handleValueChange("salaryType", value)}
      >
        <SelectTrigger className="w-full bg-white rounded-full h-[56px] px-6 text-gray-100 text-base">
          <SelectValue placeholder="Salary Type" />
        </SelectTrigger>
        <SelectContent>
          <SelectGroup>
            <SelectItem value="all">All Salary Types</SelectItem>
            {salaryTypes.map(({ label, value }) => (
              <SelectItem key={value} value={value}>
                {label}
              </SelectItem>
            ))}
          </SelectGroup>
        </SelectContent>
      </Select>
    </div>
  );
}
