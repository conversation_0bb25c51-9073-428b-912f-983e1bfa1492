"use client";

import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useGetAllEnums } from "@/hooks/useQuery";
import type { IJobSearchParams } from "@/types/query.types";

interface FilterSelectsProps {
  filters: IJobSearchParams;
  onFilterChange: (key: keyof IJobSearchParams, value: string) => void;
}

export function FilterSelects({ filters, onFilterChange }: FilterSelectsProps) {
  const { data: enumsData, isLoading } = useGetAllEnums();

  if (isLoading) {
    return <div>Loading...</div>;
  }

  const jobTypes = enumsData?.data.JOB_TYPE_ENUM
    ? Object.entries(enumsData.data.JOB_TYPE_ENUM).map(([key, _value]) => ({
        label: key.replace(/_/g, " "),
        value: key,
      }))
    : [];

  const experienceLevels = enumsData?.data.EXPERIENCE_RANGE_ENUM
    ? Object.entries(enumsData.data.EXPERIENCE_RANGE_ENUM).map(([key, _value]) => ({
        label: key.replace(/_/g, " "),
        value: key,
      }))
    : [];

  const careerLevels = enumsData?.data.CAREER_LEVEL_ENUM
    ? Object.entries(enumsData.data.CAREER_LEVEL_ENUM).map(([key, _value]) => ({
        label: key.replace(/_/g, " "),
        value: key,
      }))
    : [];

  const qualifications = enumsData?.data.QUALIFICATION_ENUM
    ? Object.entries(enumsData.data.QUALIFICATION_ENUM).map(([key, _value]) => ({
        label: key.replace(/_/g, " "),
        value: key,
      }))
    : [];

  const salaryTypes = enumsData?.data.SALARY_TYPE_ENUM
    ? Object.entries(enumsData.data.SALARY_TYPE_ENUM).map(([key, _value]) => ({
        label: key.replace(/_/g, " "),
        value: key,
      }))
    : [];

  return (
    <div className="flex overflow-x-auto gap-4 lg:p-4 w-full max-w-5xl">
      <Select value={filters.jobType} onValueChange={(value) => onFilterChange("jobType", value)}>
        <SelectTrigger className="w-full bg-white rounded-full h-[56px] px-6 text-gray-100 text-base">
          <SelectValue placeholder="Job Type" />
        </SelectTrigger>
        <SelectContent>
          <SelectGroup>
            <SelectItem value="all">All Job Types</SelectItem>
            {jobTypes.map(({ label, value }) => (
              <SelectItem key={value} value={value}>
                {label}
              </SelectItem>
            ))}
          </SelectGroup>
        </SelectContent>
      </Select>

      <Select
        value={filters.experienceLevel}
        onValueChange={(value) => onFilterChange("experienceLevel", value)}
      >
        <SelectTrigger className="w-full bg-white rounded-full h-[56px] px-6 text-gray-100 text-base">
          <SelectValue placeholder="Experience Level" />
        </SelectTrigger>
        <SelectContent>
          <SelectGroup>
            <SelectItem value="all">All Experience Levels</SelectItem>
            {experienceLevels.map(({ label, value }) => (
              <SelectItem key={value} value={value}>
                {label}
              </SelectItem>
            ))}
          </SelectGroup>
        </SelectContent>
      </Select>

      <Select
        value={filters.careerLevel}
        onValueChange={(value) => onFilterChange("careerLevel", value)}
      >
        <SelectTrigger className="w-full bg-white rounded-full h-[56px] px-6 text-gray-100 text-base">
          <SelectValue placeholder="Career Level" />
        </SelectTrigger>
        <SelectContent>
          <SelectGroup>
            <SelectItem value="all">All Career Levels</SelectItem>
            {careerLevels.map(({ label, value }) => (
              <SelectItem key={value} value={value}>
                {label}
              </SelectItem>
            ))}
          </SelectGroup>
        </SelectContent>
      </Select>

      <Select
        value={filters.qualification}
        onValueChange={(value) => onFilterChange("qualification", value)}
      >
        <SelectTrigger className="w-full bg-white rounded-full h-[56px] px-6 text-gray-100 text-base">
          <SelectValue placeholder="Qualification" />
        </SelectTrigger>
        <SelectContent>
          <SelectGroup>
            <SelectItem value="all">All Qualifications</SelectItem>
            {qualifications.map(({ label, value }) => (
              <SelectItem key={value} value={value}>
                {label}
              </SelectItem>
            ))}
          </SelectGroup>
        </SelectContent>
      </Select>

      <Select
        value={filters.salaryType}
        onValueChange={(value) => onFilterChange("salaryType", value)}
      >
        <SelectTrigger className="w-full bg-white rounded-full h-[56px] px-6 text-gray-100 text-base">
          <SelectValue placeholder="Salary Type" />
        </SelectTrigger>
        <SelectContent>
          <SelectGroup>
            <SelectItem value="all">All Salary Types</SelectItem>
            {salaryTypes.map(({ label, value }) => (
              <SelectItem key={value} value={value}>
                {label}
              </SelectItem>
            ))}
          </SelectGroup>
        </SelectContent>
      </Select>
    </div>
  );
}
