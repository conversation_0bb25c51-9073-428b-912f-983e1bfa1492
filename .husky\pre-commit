echo "🔍 Running ESLint... Because even your code needs a shower sometimes! 🚿"
# pnpm exec eslint --fix .
pnpm exec lint-staged
if [ $? -ne 0 ]; then
  echo "❌ ESLint caught some issues! Fix them before they catch you! 😱"
  exit 1
fi

echo "✨ Running Prettier... Making your code shine brighter than my future! 💅"
# pnpm exec prettier --write .
if [ $? -ne 0 ]; then
  echo "🤯 Prettier failed? That’s rough, buddy. 😔"
  exit 1
fi

echo "✅ All checks passed! You're now free to commit like a responsible developer. 🚀"
exit 0
