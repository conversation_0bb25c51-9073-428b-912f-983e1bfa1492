/**
 * Format date in a consistent way for both server and client
 * @param dateString ISO date string
 * @returns Formatted date string in YYYY-MM-DD format
 */
export const formatDate = (dateString: string): string => {
  const date = new Date(dateString);
  // Use explicit format with numeric values to ensure consistency
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, "0")}-${String(date.getDate()).padStart(2, "0")}`;
};

/**
 * Format date with time in a consistent way for both server and client
 * @param dateString ISO date string
 * @returns Formatted date string in YYYY-MM-DD HH:MM format
 */
export const formatDateTime = (dateString: string): string => {
  const date = new Date(dateString);
  // Use explicit format with numeric values to ensure consistency
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, "0")}-${String(date.getDate()).padStart(2, "0")} ${String(date.getHours()).padStart(2, "0")}:${String(date.getMinutes()).padStart(2, "0")}`;
};

/**
 * Format date in a human-readable way
 * @param dateString ISO date string
 * @returns Formatted date string in Month DD, YYYY format
 */
export const formatHumanReadableDate = (dateString: string): string => {
  const date = new Date(dateString);
  const months = [
    "January",
    "February",
    "March",
    "April",
    "May",
    "June",
    "July",
    "August",
    "September",
    "October",
    "November",
    "December",
  ];

  return `${months[date.getMonth()]} ${date.getDate()}, ${date.getFullYear()}`;
};
