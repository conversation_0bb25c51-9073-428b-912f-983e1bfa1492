"use client";
import { useQueryClient } from "@tanstack/react-query";
import React, { useState } from "react";
import { toast } from "sonner";
import ProfessionalExperienceInfo from "../Cards/ProfessionalExperienceInfo";
import { BriefcaseIcon2 } from "../Icons";
import ProfessionalExperienceForm from "./ProfessionalExperienceForm";
import {
  <PERSON>alog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { useUpdateJobSeekerProfile } from "@/hooks/useMutation";
import { useGetJobSeekerProfile } from "@/hooks/useQuery";
import { ApiError } from "@/types/common.types";
import { IExperience } from "@/types/query.types";
import { formatHumanReadableDate } from "@/utils/date";

const ProfessionalExperienceUpdate = () => {
  const [open, setOpen] = useState(false);
  const [editingExperience, setEditingExperience] = useState<IExperience | null>(null);
  const queryClient = useQueryClient();
  const { data: profileData } = useGetJobSeekerProfile();
  const { mutate: updateProfile } = useUpdateJobSeekerProfile({
    onSuccess: () => {
      toast.success("Profile updated successfully");
      queryClient.invalidateQueries({ queryKey: ["get-jobseeker-profile"] });
    },
    onError: (error) => {
      toast.error(error.response?.data?.message || "Failed to update profile");
    },
  });

  const handleClose = () => {
    setOpen(false);
    // Use setTimeout to ensure the dialog is fully closed before resetting the editing state
    // This prevents the form from flashing with the previous data before closing
    setTimeout(() => {
      setEditingExperience(null);
    }, 300);
  };

  const handleSuccess = async (formData: IExperience[]) => {
    try {
      const existingExperiences = profileData?.data.experiences || [];
      const updatedExperiences = editingExperience
        ? existingExperiences.map((exp) => (exp._id === editingExperience._id ? formData[0] : exp))
        : [...existingExperiences, ...formData];

      updateProfile({
        experiences: updatedExperiences as IExperience[],
      });
      handleClose();
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to save professional experience";
      toast.error(errorMessage);
    }
  };

  const handleError = (error: unknown) => {
    const errorMessage =
      error instanceof Error ? error.message : "Failed to save professional experience";
    toast.error(errorMessage);
  };

  const handleEdit = (experience: IExperience) => {
    setEditingExperience(experience);
    setOpen(true);
  };

  const handleDelete = async (experienceId: string) => {
    try {
      const existingExperiences = profileData?.data.experiences || [];
      const updatedExperiences = existingExperiences.filter((exp) => exp._id !== experienceId);

      updateProfile({
        experiences: updatedExperiences,
      });
      toast.success("Experience deleted successfully");
    } catch (error) {
      toast.error((error as ApiError).response?.data.message || "Failed to delete experience");
    }
  };

  const experiences = profileData?.data.experiences || [];

  return (
    <div>
      <div className="sm:flex items-center justify-between mb-10">
        <div className="mb-4 sm:mb-0">
          <h2 className="text-blue-200 text-2xl font-bold">Professional Experience</h2>
          <h3 className="text-4xl font-medium text-black-100 mt-3">
            Tell us about your Experiences
          </h3>
        </div>
        <div>
          <Dialog
            open={open}
            onOpenChange={(isOpen) => {
              setOpen(isOpen);
              if (!isOpen) {
                // Use setTimeout to ensure the dialog is fully closed before resetting the editing state
                setTimeout(() => {
                  setEditingExperience(null);
                }, 300);
              }
            }}
          >
            <DialogTrigger
              className="border-orange-100 border rounded-full px-3 py-3 flex items-center gap-x-3 font-medium text-orange-100"
              onClick={() => setEditingExperience(null)}
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="30"
                height="30"
                fill="none"
                viewBox="0 0 30 30"
              >
                <path
                  stroke="#EC761E"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M15 29c7.732 0 14-6.268 14-14S22.732 1 15 1 1 7.268 1 15s6.268 14 14 14M10.333 15h9.334M15 10.333v9.333"
                ></path>
              </svg>
              Add Experience
            </DialogTrigger>
            <DialogContent className="md:max-w-[920px] max-h-[80vh] overflow-y-auto">
              <DialogHeader className="text-left">
                <DialogTitle className="text-2xl text-orange-100 font-bold mb-5">
                  Professional Experience Details
                </DialogTitle>
                <ProfessionalExperienceForm
                  goBack={handleClose}
                  onSuccess={handleSuccess}
                  onError={handleError}
                  defaultValues={editingExperience ? [editingExperience] : []}
                />
              </DialogHeader>
            </DialogContent>
          </Dialog>
        </div>
      </div>
      <div className="space-y-4">
        {experiences.map((experience: IExperience, index: number) => (
          <ProfessionalExperienceInfo
            key={experience._id || index}
            icon={<BriefcaseIcon2 />}
            date={`${formatHumanReadableDate(experience.startDate)} - ${formatHumanReadableDate(experience.endDate)}`}
            text={experience.designation}
            title={experience.organizationName}
            onEdit={() => handleEdit(experience)}
            onDelete={() => handleDelete(experience._id)}
          />
        ))}
        {experiences.length === 0 && (
          <div className="text-center text-gray-500 py-8">
            No professional experiences added yet. Click &apos;Add Experience&apos; to get started.
          </div>
        )}
      </div>
    </div>
  );
};

export default ProfessionalExperienceUpdate;
