"use client";

import dynamic from "next/dynamic";
import { Suspense } from "react";

interface Step {
  id: string;
  title: string;
}

interface SidebarProps {
  steps: Step[];
}

// Import SidebarContent with dynamic import to handle useSearchParams
const SidebarContent = dynamic(() => import("./SidebarContent"), {
  ssr: false,
});

const Sidebar = ({ steps }: SidebarProps) => {
  return (
    <Suspense
      fallback={
        <div className="rounded-3xl border border-gray-200 lg:p-10 p-4 h-full animate-pulse">
          <div className="h-8 w-48 bg-gray-200 rounded mb-10"></div>
          <div className="space-y-8">
            {Array.from({ length: 5 }).map((_, i) => (
              <div key={i} className="flex items-center">
                <div className="w-8 h-8 rounded-full bg-gray-200 mr-4"></div>
                <div className="h-4 w-24 bg-gray-200 rounded"></div>
              </div>
            ))}
          </div>
        </div>
      }
    >
      <SidebarContent steps={steps} />
    </Suspense>
  );
};

export default Sidebar;
