"use client";

import { debounce } from "lodash";
import { Send, Paperclip, X, FileText, Image as ImageIcon, Video } from "lucide-react";
import { useCallback, useEffect, useRef, useState } from "react";
import { useInView } from "react-intersection-observer";
import { toast } from "sonner";
import MessageItem from "@/components/Chat/MessageItem";
import TypingIndicator from "@/components/Chat/TypingIndicator";
import LoadingSpinner from "@/components/LoadingSpinner/LoadingSpinner";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useUploadMediaForChat } from "@/hooks/useMutation";
import { useGetConversationById, useGetMessagesInfinite } from "@/hooks/useQuery";
import { debugLog, debugError } from "@/lib/debug";
import { cn } from "@/lib/utils";
import {
  joinConversation,
  markAsRead,
  setTyping,
  sendMessage as sendMessageSocket,
} from "@/service/socket.service";
import { useChatStore, createPendingMessage, pendingMessageToIMessage } from "@/store/useChatStore";
import { useUserStore } from "@/store/useUserStore";
import { IMediaFile } from "@/types/query.types";

interface ChatViewProps {
  conversationId: string;
}

export default function ChatView({ conversationId }: ChatViewProps) {
  const { currentUser } = useUserStore();
  const { pendingMessages } = useChatStore();
  const [newMessage, setNewMessage] = useState("");
  const [isTyping, setIsTyping] = useState(false);
  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);
  // We need to track uploaded files to attach them to messages
  const [, setUploadedFiles] = useState<IMediaFile[]>([]);
  const [isUploading, setIsUploading] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const messageInputRef = useRef<HTMLInputElement>(null);
  const { ref: loadMoreRef, inView } = useInView();

  // Get conversation details
  const { data: conversationData } = useGetConversationById(conversationId);
  const conversation = conversationData?.data;

  // Get messages with infinite scroll
  const {
    data: messagesData,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    isLoading: isLoadingMessages,
    refetch, // Extract refetch function
  } = useGetMessagesInfinite({
    conversationId,
    limit: 20,
  });

  // State for tracking message sending status
  const [isSendingMessage, setIsSendingMessage] = useState(false);
  // Mutations
  const { mutate: uploadMedia } = useUploadMediaForChat();

  // Add a safety timeout to reset sending state if it gets stuck
  // This is just a fallback in case the socket events fail
  useEffect(() => {
    let timeoutId: NodeJS.Timeout | null = null;

    if (isSendingMessage) {
      // If the message is still sending after 5 seconds, reset the state
      // Reduced from 10 seconds to 5 seconds for better user experience
      timeoutId = setTimeout(() => {
        debugLog("Message sending timeout - resetting state");
        setIsSendingMessage(false);
      }, 5000);
    }

    return () => {
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
    };
  }, [isSendingMessage]);

  // Load more messages when scrolling up
  useEffect(() => {
    if (inView && hasNextPage && !isFetchingNextPage) {
      fetchNextPage();
    }
  }, [inView, hasNextPage, isFetchingNextPage, fetchNextPage]);

  // Scroll to bottom on initial load or when new messages arrive
  useEffect(() => {
    // Only run this effect on the client side
    if (typeof window === "undefined") return;

    if (messagesEndRef.current && !isFetchingNextPage) {
      messagesEndRef.current.scrollIntoView({ behavior: "smooth" });
    }
  }, [messagesData, isFetchingNextPage, pendingMessages]);

  // Helper function to clear pending messages for a conversation
  const clearPendingMessagesForConversation = useCallback((convId: string) => {
    if (!convId) return;

    const pendingMessages = useChatStore.getState().pendingMessages;
    const matchingPendingMessages = pendingMessages.filter((msg) => msg.conversationId === convId);

    // Remove all pending messages for this conversation
    matchingPendingMessages.forEach((msg) => {
      useChatStore.getState().removePendingMessage(msg.id);
    });
  }, []);

  // Handle visibility change (tab switching)
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.visibilityState === "visible" && conversationId) {
        // When the user comes back to this tab, clear any pending messages
        // and refetch the messages to ensure we have the latest data
        clearPendingMessagesForConversation(conversationId);

        // Refetch messages to get the latest data
        refetch();
      }
    };

    document.addEventListener("visibilitychange", handleVisibilityChange);

    return () => {
      document.removeEventListener("visibilitychange", handleVisibilityChange);
    };
  }, [conversationId, refetch, clearPendingMessagesForConversation]);

  // Join conversation socket room and clear any stale pending messages
  useEffect(() => {
    if (conversationId) {
      joinConversation(conversationId);

      // Clear any pending messages for this conversation when joining
      // This helps prevent duplication when switching tabs
      clearPendingMessagesForConversation(conversationId);
    }

    // Also clear pending messages when the component unmounts
    return () => {
      if (conversationId) {
        clearPendingMessagesForConversation(conversationId);
      }
    };
  }, [conversationId, clearPendingMessagesForConversation]);

  // Mark messages as read with improved handling
  useEffect(() => {
    let timeoutId: NodeJS.Timeout | undefined;

    if (messagesData?.pages && currentUser) {
      const allMessages = messagesData.pages.flatMap((page) => page.data.messages);

      // Filter for unread messages that are not from the current user
      const unreadMessages = allMessages.filter(
        (msg) =>
          !msg.read && msg.senderId !== currentUser._id && msg.senderType !== currentUser.role
      );

      // Use a small delay to avoid too many simultaneous requests
      if (unreadMessages.length > 0) {
        debugLog(`Marking ${unreadMessages.length} messages as read`);

        // Process messages in batches to avoid overwhelming the socket
        const markMessagesInBatches = async () => {
          const batchSize = 3; // Process 3 messages at a time

          for (let i = 0; i < unreadMessages.length; i += batchSize) {
            const batch = unreadMessages.slice(i, i + batchSize);

            // Process each batch in parallel
            await Promise.all(
              batch.map(async (msg) => {
                try {
                  // Use the updated markAsRead function that follows the documentation
                  const success = await markAsRead(msg._id);

                  if (success) {
                    debugLog(`Marked message ${msg._id} as read successfully`);

                    // The message_read event will be handled by the SocketProvider
                    // which will update the cache and UI automatically
                  } else {
                    debugLog(`Failed to mark message ${msg._id} as read (no success)`);
                  }
                } catch (error) {
                  debugLog(`Failed to mark message ${msg._id} as read: ${error}`);
                }
              })
            );

            // Small delay between batches to avoid overwhelming the socket
            if (i + batchSize < unreadMessages.length) {
              await new Promise((resolve) => setTimeout(resolve, 300));
            }
          }
        };

        // Start marking messages as read with a small delay
        // This ensures the UI is responsive when the conversation is first loaded
        timeoutId = setTimeout(() => {
          markMessagesInBatches();
        }, 500);
      }
    }

    // Clean up the timeout if the component unmounts
    return () => {
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
    };
  }, [messagesData, currentUser]);

  // Handle typing indicator with debounce
  // Use a more efficient approach with two debounced functions
  const debouncedStartTyping = useRef(
    debounce(() => {
      if (conversationId) {
        setTyping(conversationId, true).catch((error) => {
          debugError("Error setting typing indicator:", error);
        });
      }
    }, 300) // Shorter delay for starting typing
  ).current;

  const debouncedStopTyping = useRef(
    debounce(() => {
      if (conversationId) {
        setTyping(conversationId, false).catch((error) => {
          debugError("Error clearing typing indicator:", error);
        });
      }
    }, 1000) // Longer delay for stopping typing
  ).current;

  // Handle typing events
  useEffect(() => {
    if (newMessage.length > 0) {
      // User is typing
      if (!isTyping) {
        setIsTyping(true);
      }
      // Emit typing event (debounced to avoid too many events)
      debouncedStartTyping();
      // Cancel the stop typing debounce
      debouncedStopTyping.cancel();
    } else if (isTyping) {
      // User stopped typing
      setIsTyping(false);
      // Cancel the start typing debounce
      debouncedStartTyping.cancel();
      // Emit stop typing event (with delay)
      debouncedStopTyping();
    }
  }, [newMessage, isTyping, conversationId, debouncedStartTyping, debouncedStopTyping]);

  // Clean up debounce on unmount and reset states
  useEffect(() => {
    return () => {
      // Cancel both debounced functions
      debouncedStartTyping.cancel();
      debouncedStopTyping.cancel();

      // If user was typing when component unmounts, send stop typing event
      if (isTyping && conversationId) {
        setTyping(conversationId, false).catch((error) => {
          debugError("Error clearing typing indicator on unmount:", error);
        });
      }

      // Reset loading states when component unmounts
      // This prevents issues if the component is unmounted while a message is being sent
      setIsUploading(false);
      setIsSendingMessage(false);
    };
  }, [debouncedStartTyping, debouncedStopTyping, isTyping, conversationId]);

  // Watch for changes in pendingMessages to ensure we scroll to bottom
  useEffect(() => {
    // If there are pending messages for this conversation, scroll to bottom
    const hasPendingMessagesForThisConversation = pendingMessages.some(
      (msg) => msg.conversationId === conversationId
    );

    if (hasPendingMessagesForThisConversation && messagesEndRef.current) {
      // Use a short timeout to ensure the DOM has updated
      setTimeout(() => {
        messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
      }, 50);
    }
  }, [pendingMessages, conversationId]);

  // Handle file selection
  const handleFileSelect = () => {
    fileInputRef.current?.click();
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      // Convert FileList to array
      const filesArray = Array.from(e.target.files);

      // Check file size limit (3MB = 3 * 1024 * 1024 bytes)
      const MAX_FILE_SIZE = 3 * 1024 * 1024; // 3MB in bytes

      // Filter out files that are too large
      const validFiles = filesArray.filter((file) => {
        if (file.size > MAX_FILE_SIZE) {
          toast.error(`File "${file.name}" exceeds the 3MB size limit and will not be uploaded.`);
          return false;
        }
        return true;
      });

      // Update selected files
      setSelectedFiles((prev) => [...prev, ...validFiles]);

      // Show warning if some files were filtered out
      if (validFiles.length < filesArray.length) {
        toast.warning(`Some files were not selected because they exceed the 3MB size limit.`);
      }
    }
  };

  const removeSelectedFile = (index: number) => {
    setSelectedFiles((prev) => prev.filter((_, i) => i !== index));
  };

  // Handle message sending
  const handleSendMessage = async (e: React.FormEvent) => {
    e.preventDefault();

    if ((!newMessage.trim() && selectedFiles.length === 0) || !currentUser || !conversationId) {
      return;
    }

    // Ensure we scroll to the bottom after sending a message
    setTimeout(() => {
      messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
    }, 100);

    // Upload files first if any
    if (selectedFiles.length > 0) {
      setIsUploading(true);

      // Create a FormData object for the file upload
      // This follows the documentation for the upload media endpoint
      const formData = new FormData();
      selectedFiles.forEach((file) => {
        formData.append("file", file);
      });

      // Upload the files using the API endpoint
      uploadMedia(
        { file: selectedFiles, conversationId, content: newMessage },
        {
          onSuccess: (data) => {
            debugLog("Media uploaded successfully:", data.data.mediaFiles);

            // Format the media files according to the documentation
            const formattedMediaFiles = data.data.mediaFiles.map((file) => ({
              url: file.url,
              s3Key: file.s3Key,
              fileName: file.fileName,
              fileSize: file.fileSize,
            }));

            // Clear the selected files immediately
            setSelectedFiles([]);

            // Make sure uploading state is reset
            setIsUploading(false);

            // Send the message with the uploaded media files
            // This will handle its own loading state
            sendMessage(formattedMediaFiles);
          },
          onError: (error) => {
            debugLog("Failed to upload media:", error);
            toast.error("Failed to upload files. Please try again.");

            // Make sure uploading state is reset
            setIsUploading(false);

            // Re-enable the input field
            setIsSendingMessage(false);
          },
        }
      );
    } else {
      // Send message without media files
      sendMessage([]);
    }
  };

  const sendMessage = async (mediaFiles: IMediaFile[]) => {
    if (!currentUser || !conversationId) return;

    const messageContent = newMessage.trim();

    // Clear the input field immediately for better UX
    setNewMessage("");

    // Only set sending state for the button, not the input field
    setIsSendingMessage(true);

    // Create a pending message for optimistic UI update
    const pendingMessage = createPendingMessage(conversationId, messageContent, mediaFiles);
    useChatStore.getState().addPendingMessage(pendingMessage);

    // Prepare the message payload according to the documentation
    const messagePayload = {
      conversationId,
      content: messageContent,
      mediaFiles,
    };

    // Focus the input field immediately so user can type another message
    setTimeout(() => {
      messageInputRef.current?.focus();
    }, 0);

    // Make sure we're joined to the conversation before sending
    try {
      debugLog(`Ensuring we're joined to conversation ${conversationId} before sending message`);
      await joinConversation(conversationId);
    } catch (joinError) {
      debugError("Error joining conversation:", joinError);
      // Continue anyway, the sendMessageSocket function will try to join again
    }

    // Log detailed information about the message we're sending
    debugLog("Sending message with details:", {
      id: pendingMessage.id,
      conversationId,
      content: messageContent,
      mediaFilesCount: mediaFiles.length,
      currentUserId: currentUser._id,
      currentUserRole: currentUser.role,
    });

    // Send the message using socket event
    sendMessageSocket(messagePayload)
      .then((success) => {
        if (success) {
          debugLog(`Message sent successfully via socket`);
          // Reset uploaded files state
          setUploadedFiles([]);

          // Update the pending message status to "sent"
          useChatStore.getState().updatePendingMessageStatus(pendingMessage.id, "sent");

          // Force refetch messages to ensure we have the latest data
          // This helps in case the new_message event wasn't received
          setTimeout(() => {
            debugLog("Forcing refetch of messages after successful send");
            refetch();
          }, 1000);
        } else {
          debugLog(`Failed to send message via socket`);
          // Update the pending message status to show error
          useChatStore.getState().updatePendingMessageStatus(pendingMessage.id, "error");

          // Don't remove the pending message, just mark it as failed
          // This allows the user to see that the message failed to send
          toast.error("Failed to send message", {
            description: "Your message wasn't delivered. You can try again.",
            action: {
              label: "Retry",
              onClick: () => {
                // Remove the failed message
                useChatStore.getState().removePendingMessage(pendingMessage.id);
                // Re-add the content to the input field
                setNewMessage(messageContent);
              },
            },
          });
        }

        // Always enable the send button regardless of success/failure
        setIsSendingMessage(false);
      })
      .catch((error) => {
        debugLog(`Error sending message via socket:`, error);
        // Update the pending message status to show error
        useChatStore.getState().updatePendingMessageStatus(pendingMessage.id, "error");

        // Don't remove the pending message, just mark it as failed
        toast.error("Failed to send message", {
          description: "Your message wasn't delivered. You can try again.",
          action: {
            label: "Retry",
            onClick: () => {
              // Remove the failed message
              useChatStore.getState().removePendingMessage(pendingMessage.id);
              // Re-add the content to the input field
              setNewMessage(messageContent);
            },
          },
        });

        // Always enable the send button regardless of success/failure
        setIsSendingMessage(false);
      });
  };

  // Get partner name and avatar
  const getPartnerInfo = () => {
    if (!conversation) return { name: "Loading...", avatar: "/placeholder.svg?height=40&width=40" };

    const isCurrentUserJobSeeker = currentUser?.role === "JOBSEEKER";

    if (isCurrentUserJobSeeker) {
      return {
        name: conversation.recruiterProfileId?.companyProfile?.companyName || "Unknown",
        avatar:
          conversation.recruiterProfileId?.companyProfile?.profilePicture ||
          "/placeholder.svg?height=40&width=40",
      };
    } else {
      return {
        name:
          `${conversation.jobSeekerProfileId?.userProfile?.firstName || ""} ${
            conversation.jobSeekerProfileId?.userProfile?.lastName || ""
          }`.trim() || "Unknown",
        avatar:
          conversation.jobSeekerProfileId?.userProfile?.profilePicture ||
          "/placeholder.svg?height=40&width=40",
      };
    }
  };

  const { name: partnerName, avatar: partnerAvatar } = getPartnerInfo();

  // Combine server messages with pending messages
  const allMessages = () => {
    if (!messagesData) return [];

    // Debug the messages data
    debugLog("Messages data:", {
      pagesCount: messagesData.pages.length,
      messagesCount: messagesData.pages.flatMap((page) => page.data.messages).length,
    });

    const serverMessages = messagesData.pages.flatMap((page) => page.data.messages);

    // Create a map of server message content and timestamps for deduplication
    // This is more reliable than just checking IDs
    const serverMessageMap = new Map();
    serverMessages.forEach((msg) => {
      // Create a unique key based on content, sender, and approximate time
      // This helps identify messages that are duplicates even if they have different IDs
      const timeWindow = Math.floor(new Date(msg.createdAt).getTime() / 10000); // Group by 10-second windows
      const key = `${msg.content}-${msg.senderId}-${timeWindow}`;
      serverMessageMap.set(key, true);

      // Also add the message ID as a key for exact matching
      serverMessageMap.set(msg._id, true);
    });

    // Only include pending messages that don't have corresponding server messages
    const relevantPendingMessages = pendingMessages
      .filter((msg) => msg.conversationId === conversationId)
      // Exclude pending messages that match content and approximate time of server messages
      .filter((msg) => {
        // First check if we have a server message with the same ID
        // This handles the case where a pending message has been assigned a server ID
        if (serverMessageMap.has(msg.id)) {
          return false;
        }

        // Then check content and time window
        const timeWindow = Math.floor(new Date(msg.createdAt).getTime() / 10000);
        const key = `${msg.content}-${currentUser?._id}-${timeWindow}`;
        return !serverMessageMap.has(key);
      })
      .map((msg) => pendingMessageToIMessage(msg, currentUser?._id || "", currentUser?.role || ""));

    // Debug the combined messages
    debugLog("Combined messages:", {
      serverMessagesCount: serverMessages.length,
      pendingMessagesCount: relevantPendingMessages.length,
      totalMessagesCount: serverMessages.length + relevantPendingMessages.length,
    });

    // Combine and sort by date
    return [...serverMessages, ...relevantPendingMessages].sort(
      (a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
    );
  };

  // Render file preview
  const renderFilePreview = (file: File | IMediaFile, index: number, isUploaded = false) => {
    const fileName = "fileName" in file ? file.fileName : file.name;
    const fileSize = "fileSize" in file ? file.fileSize : file.size;
    const fileType = fileName.split(".").pop()?.toLowerCase() || "";
    const isImage = ["jpg", "jpeg", "png", "gif", "webp"].includes(fileType);
    const isVideo = ["mp4", "webm", "ogg"].includes(fileType);

    // We don't need the URL here since we're not displaying the file directly
    // But we'll keep this comment for future reference

    return (
      <div key={index} className="relative bg-gray-100 rounded-md p-2 mb-2 flex items-center">
        {isImage ? (
          <ImageIcon className="h-5 w-5 mr-2" />
        ) : isVideo ? (
          <Video className="h-5 w-5 mr-2" />
        ) : (
          <FileText className="h-5 w-5 mr-2" />
        )}
        <div className="flex-1 truncate">
          <p className="text-sm font-medium truncate">{fileName}</p>
          <p className="text-xs text-gray-500">{(fileSize / 1024).toFixed(1)} KB</p>
        </div>
        {!isUploaded && (
          <button
            type="button"
            onClick={() => removeSelectedFile(index)}
            className="text-red-500 hover:text-red-700"
          >
            <X className="h-4 w-4" />
          </button>
        )}
      </div>
    );
  };

  if (isLoadingMessages) {
    return (
      <div className="flex justify-center items-center h-full">
        <LoadingSpinner />
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full">
      <div className="p-4 border-b border-gray-200 flex items-center gap-3">
        <Avatar className="h-10 w-10">
          <AvatarImage src={partnerAvatar} alt={partnerName} />
          <AvatarFallback>{partnerName.charAt(0)}</AvatarFallback>
        </Avatar>
        <div>
          <h2 className="font-bold text-orange-100 text-xl">{partnerName}</h2>
          {conversation?.jobApplication?.job?.jobTitle && (
            <p className="text-sm text-gray-500">Job: {conversation.jobApplication.job.jobTitle}</p>
          )}
          <TypingIndicator conversationId={conversationId} />
        </div>
        {/* Typing indicator */}
      </div>

      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {/* Load more messages indicator */}
        {isFetchingNextPage ? (
          <div className="flex justify-center py-2">
            <LoadingSpinner size="sm" />
          </div>
        ) : hasNextPage ? (
          <div ref={loadMoreRef} className="flex justify-center py-2 text-sm text-gray-500">
            Scroll up to load more messages
          </div>
        ) : (
          <div className="text-center py-2 text-sm text-gray-500">Beginning of conversation</div>
        )}

        {/* Messages */}
        {allMessages().length > 0 ? (
          allMessages().map((message) => {
            // Check if the message is from the current user by comparing both senderType and senderId
            // This handles both cases: messages from the current user and messages from others
            const isCurrentUser =
              message.senderType === currentUser?.role || message.senderId === currentUser?._id;
            return (
              <MessageItem
                currentUserId={currentUser?._id}
                key={message._id}
                message={message}
                isCurrentUser={isCurrentUser}
              />
            );
          })
        ) : (
          <div className="flex justify-center items-center py-8">
            <div className="text-center text-gray-500">
              <p className="mb-2">No messages yet</p>
              <p className="text-sm">Start the conversation by sending a message below</p>
            </div>
          </div>
        )}

        {/* Anchor for scrolling to bottom */}
        <div ref={messagesEndRef} />
      </div>

      {/* File previews */}
      {selectedFiles.length > 0 && (
        <div className="px-4 pt-2">
          <div className="flex flex-wrap gap-2">
            {selectedFiles.map((file, index) => renderFilePreview(file, index))}
          </div>
        </div>
      )}

      {/* Message input */}
      <div className="p-4 border-t border-gray-200">
        <form onSubmit={handleSendMessage} className="flex items-center gap-2">
          <button
            type="button"
            onClick={handleFileSelect}
            className="text-orange-100 hover:text-orange-200"
          >
            <Paperclip className="h-5 w-5" />
          </button>
          <input
            type="file"
            ref={fileInputRef}
            onChange={handleFileChange}
            className="hidden"
            multiple
          />

          <div className="relative flex-1">
            <Input
              ref={messageInputRef}
              value={newMessage}
              onChange={(e) => setNewMessage(e.target.value)}
              placeholder="Type your message here..."
              className="bg-gray-300 rounded-3xl h-12 pr-12"
              disabled={isUploading} // Only disable during file uploads, not during message sending
            />
            <Button
              type="submit"
              size="icon"
              className={cn(
                "text-orange-100 bg-transparent absolute top-1/2 right-2 transform -translate-y-1/2",
                (isUploading || isSendingMessage) && "opacity-50 cursor-not-allowed"
              )}
              disabled={
                isUploading ||
                isSendingMessage ||
                (!newMessage.trim() && selectedFiles.length === 0)
              }
            >
              {isUploading ? (
                <LoadingSpinner size="sm" />
              ) : isSendingMessage ? (
                <Send className="h-4 w-4 text-gray-400" />
              ) : (
                <Send className="h-4 w-4" />
              )}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
}
