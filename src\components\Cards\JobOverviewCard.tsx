import React from "react";

interface JobOverviewCardProps {
  icon: React.ReactNode;
  title: string;
  titleAccording: string;
}

const JobOverviewCard: React.FC<JobOverviewCardProps> = ({ icon, title, titleAccording }) => {
  return (
    <div className="flex gap-x-3">
      <div>{icon}</div>
      <div>
        <h3 className="text-black-100 font-medium text-base mb-1">{title}</h3>
        <p className="text-gray-100 font-sm">{titleAccording}</p>
      </div>
    </div>
  );
};

export default JobOverviewCard;
