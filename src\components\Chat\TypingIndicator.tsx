"use client";

import { useEffect, useState } from "react";
import { useGetConversationById } from "@/hooks/useQuery";
import { cn } from "@/lib/utils";
import { useChatStore } from "@/store/useChatStore";
import { useUserStore } from "@/store/useUserStore";

interface TypingIndicatorProps {
  conversationId: string;
}

export default function TypingIndicator({ conversationId }: TypingIndicatorProps) {
  const { typingStatus } = useChatStore();
  const { currentUser } = useUserStore();
  const { data: conversationData } = useGetConversationById(conversationId);
  const [typingName, setTypingName] = useState<string | null>(null);
  const isTyping = typingStatus[conversationId] || false;

  useEffect(() => {
    if (!isTyping || !conversationData?.data || !currentUser) {
      setTypingName(null);
      return;
    }

    // Determine the name of the person typing based on the current user's role
    const isCurrentUserJobSeeker = currentUser.role === "JOBSEEKER";

    if (isCurrentUserJobSeeker) {
      // If current user is job seeker, the recruiter is typing
      setTypingName(
        conversationData.data.recruiterProfileId?.companyProfile?.companyName || "Someone"
      );
    } else {
      // If current user is recruiter, the job seeker is typing
      setTypingName(
        `${conversationData.data.jobSeekerProfileId?.userProfile?.firstName || ""} ${
          conversationData.data.jobSeekerProfileId?.userProfile?.lastName || ""
        }`.trim() || "Someone"
      );
    }
  }, [isTyping, conversationData, currentUser]);

  if (!isTyping || !typingName) {
    return null;
  }

  return (
    <div className="flex items-center text-sm text-gray-500">
      <div className="flex items-center">
        <span className="mr-2">Typing</span>
        <span className="flex">
          <span
            className={cn("h-1.5 w-1.5 bg-gray-500 rounded-full animate-bounce", "mr-1")}
            style={{ animationDelay: "0ms" }}
          />
          <span
            className={cn("h-1.5 w-1.5 bg-gray-500 rounded-full animate-bounce", "mr-1")}
            style={{ animationDelay: "150ms" }}
          />
          <span
            className={cn("h-1.5 w-1.5 bg-gray-500 rounded-full animate-bounce")}
            style={{ animationDelay: "300ms" }}
          />
        </span>
      </div>
    </div>
  );
}
