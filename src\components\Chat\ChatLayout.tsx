"use client";

import { usePara<PERSON>, useRouter } from "next/navigation";
import { useEffect } from "react";
import ChatList from "./ChatList";
import ChatView from "./ChatView";
import LoadingSpinner from "@/components/LoadingSpinner/LoadingSpinner";
import { useHydrateChatStore } from "@/hooks/useHydrateChatStore";
import { useGetConversations, useGetConversationById } from "@/hooks/useQuery";
import { debugLog, debugError } from "@/lib/debug";
import {
  joinConversation,
  isSocketConnected,
  SocketConnectionStatus,
} from "@/service/socket.service";
import { useChatStore } from "@/store/useChatStore";
import { useUserStore } from "@/store/useUserStore";

interface ChatLayoutProps {
  conversationId?: string;
}

export default function ChatLayout({ conversationId: propConversationId }: ChatLayoutProps) {
  const params = useParams();
  const router = useRouter();
  // Use the prop if provided, otherwise fall back to the URL parameter
  const conversationId = propConversationId || (params?.id as string);
  const { setActiveConversation, socketStatus } = useChatStore();
  const { token } = useUserStore();

  // Hydrate the chat store
  const storeHydrated = useHydrateChatStore();

  // Fetch all conversations
  const {
    data: conversationsData,
    isLoading: isLoadingConversations,
    isError: isConversationsError,
    refetch: refetchConversations,
  } = useGetConversations(
    {},
    {
      // Reduce refetching frequency to avoid unnecessary API calls
      refetchOnWindowFocus: false,
      refetchOnMount: true,
      refetchOnReconnect: false,
      staleTime: 30 * 1000, // Consider data fresh for 30 seconds
    }
  );

  // Fetch selected conversation details if ID is provided
  const { isLoading: isLoadingConversation } = useGetConversationById(conversationId, {
    enabled: !!conversationId,
    // Reduce refetching frequency to avoid unnecessary API calls
    refetchOnWindowFocus: false,
    refetchOnMount: true,
    refetchOnReconnect: false,
    staleTime: 60 * 1000, // Consider data fresh for 60 seconds
  });

  // Set active conversation in store and join socket room
  useEffect(() => {
    let retryTimer: NodeJS.Timeout | null = null;

    // Only run this effect on the client side
    if (typeof window === "undefined") return;

    if (conversationId && storeHydrated && token) {
      setActiveConversation(conversationId);

      // Only try to join if socket is connected or connecting
      if (
        socketStatus === SocketConnectionStatus.CONNECTED ||
        socketStatus === SocketConnectionStatus.CONNECTING
      ) {
        debugLog(`Joining conversation: ${conversationId}`);
        joinConversation(conversationId).catch((error) => {
          debugError(`Failed to join conversation: ${error.message}`);
        });
      } else {
        debugLog(`Not joining conversation yet. Socket status: ${socketStatus}`);
        // Try again after a delay if socket is not connected
        retryTimer = setTimeout(() => {
          if (isSocketConnected()) {
            debugLog(`Retrying join conversation: ${conversationId}`);
            joinConversation(conversationId).catch((error) => {
              debugError(`Failed to join conversation on retry: ${error.message}`);
            });
            if (retryTimer) {
              clearTimeout(retryTimer);
            }
          }
        }, 2000);
      }
    }

    return () => {
      if (conversationId) {
        setActiveConversation(null);
      }
      if (retryTimer) {
        clearTimeout(retryTimer);
      }
    };
  }, [conversationId, setActiveConversation, socketStatus, storeHydrated, token]);

  // Listen for changes in the lastMessages store to update the conversation list
  const { lastMessages } = useChatStore();
  useEffect(() => {
    // Only run this effect on the client side
    if (typeof window === "undefined") return;

    // When a new message arrives via socket, it updates the lastMessages in the store
    // We can use this to trigger a refetch of the conversations list, but only if needed

    // Get the most recent message timestamp
    const lastMessageTimestamps = Object.values(lastMessages)
      .filter(Boolean)
      .map((msg) => new Date(msg!.createdAt).getTime());

    if (lastMessageTimestamps.length === 0) return;

    const mostRecentMessageTime = Math.max(...lastMessageTimestamps);
    const currentTime = Date.now();
    const timeSinceLastMessage = currentTime - mostRecentMessageTime;

    // If the most recent message is less than 2 seconds old, refetch conversations
    // This ensures the conversation list is updated with the latest order
    if (timeSinceLastMessage < 2000) {
      debugLog("Recent message detected in store, refreshing conversation list");
      refetchConversations();
    }
  }, [lastMessages, refetchConversations]);

  // Handle chat selection
  const handleSelectChat = (conversationId: string) => {
    if (isSocketConnected()) {
      joinConversation(conversationId).catch((error) => {
        debugError(`Failed to join conversation on selection: ${error.message}`);
      });
    } else {
      debugLog(`Not joining conversation on selection. Socket not connected.`);
    }
    router.push(`/message?id=${conversationId}`);
  };

  // Show loading state
  if (isLoadingConversations) {
    return (
      <div className="container mx-auto flex justify-center items-center h-[90vh]">
        <LoadingSpinner />
      </div>
    );
  }

  // Show error state
  if (isConversationsError) {
    return (
      <div className="container mx-auto flex justify-center items-center h-[90vh]">
        <div className="text-center">
          <h2 className="text-xl font-bold text-red-500 mb-2">Error loading conversations</h2>
          <p className="text-gray-600 mb-4">There was a problem loading your conversations.</p>
          <button
            onClick={() => window.location.reload()}
            className="bg-orange-100 text-white px-4 py-2 rounded-md hover:bg-orange-200"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  // Get socket status color
  const getSocketStatusColor = () => {
    switch (socketStatus) {
      case SocketConnectionStatus.CONNECTED:
        return "bg-green-500";
      case SocketConnectionStatus.CONNECTING:
        return "bg-yellow-500";
      case SocketConnectionStatus.DISCONNECTED:
        return "bg-red-500";
      case SocketConnectionStatus.ERROR:
        return "bg-red-700";
      default:
        return "bg-gray-500";
    }
  };

  return (
    <div className="container mx-auto">
      <div className="flex h-[90vh]">
        <div className="w-1/3 border-r">
          {/* Socket status indicator */}
          <div className="p-2 flex items-center justify-between border-b">
            <h2 className="text-lg font-semibold">Conversations</h2>
            <div className="flex items-center">
              <span className="text-xs mr-2">Socket:</span>
              <span
                className={`inline-block w-3 h-3 rounded-full ${getSocketStatusColor()}`}
                title={`Socket status: ${socketStatus}`}
              ></span>
            </div>
          </div>
          <ChatList
            conversations={conversationsData?.data?.conversations || []}
            selectedConversationId={conversationId}
            onSelectConversation={handleSelectChat}
          />
        </div>
        <div className="w-2/3">
          {isLoadingConversation ? (
            <div className="flex justify-center items-center h-full">
              <LoadingSpinner />
            </div>
          ) : conversationId ? (
            <ChatView conversationId={conversationId} />
          ) : (
            <div className="flex justify-center items-center h-full text-gray-500">
              Select a conversation to start messaging
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
