import Image from "next/image";
import Link from "next/link";
import React from "react";
import PrimaryButton from "../Buttons/PrimaryButton";
import {
  BriefcaseIcon2,
  ChatDots,
  CurrencyIcon,
  DiplomaIcon,
  EnvelopeSimpleIcon,
  ExperienceIcon,
  FileArrowDown,
  InstagramIcon,
  LinkdinIcon,
  MapPin,
  MortarboardIcon,
  QualificationIcon,
  Trophy,
  XIcon,
} from "../Icons";
import CandidateDetailInfo from "./CandidateDetailInfo";

interface CompanyDetailProps {
  imageUrl: string;
  jobTitle: string;
  category: string;
  jobType: string;
  cityName: string;
  salaryRange: string;
  skillsExperience: string[];
  bioDescription: string;
  skillsTags: string[];
  designation: string;
  location: string;
  expectedSalary: string;
  experience: string;
  qualification: string;
  email: string;
  linkdinLink: string;
  instaLink: string;
  Xlink: string;
}

const CompanyDetail: React.FC<CompanyDetailProps> = ({
  imageUrl,
  jobTitle,
  jobType,
  cityName,
  salaryRange,
  bioDescription,
  skillsTags,
  designation,
  location,
  expectedSalary,
  experience,
  qualification,
  email,
  linkdinLink,
  instaLink,
  Xlink,
}) => {
  return (
    <>
      <div className={`rounded-[18px] border bg-offWhite-100 border-gray-200 p-[30px]`}>
        <div className="flex items-center">
          <div className="mr-4">
            <Image
              src={imageUrl}
              alt="Company Logo"
              width={80}
              height={80}
              className="w-[80px] h-[80px] rounded-full"
            />
          </div>
          <div>
            <h2 className="text-2xl font-medium text-black-100 mb-3">{jobTitle}</h2>
            <p className="text-orange-100">{designation}</p>
          </div>
          <div className="flex space-x-4 ml-auto">
            <span>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                fill="none"
                viewBox="0 0 24 24"
              >
                <path
                  fill="#262626"
                  d="M21.647 6.9a1.486 1.486 0 0 0-1.772.355l-3.157 3.403-3.356-7.528v-.01a1.5 1.5 0 0 0-2.724 0v.01l-3.356 7.528-3.157-3.403A1.5 1.5 0 0 0 1.53 8.543l2.126 9.738A1.5 1.5 0 0 0 5.13 19.5h13.74a1.5 1.5 0 0 0 1.474-1.219l2.126-9.738q-.002-.015.007-.03a1.486 1.486 0 0 0-.83-1.613m-2.77 11.07-.006.03H5.129l-.006-.03L3 8.25l.013.015 3.938 4.241a.75.75 0 0 0 1.235-.204L12 3.75l3.815 8.555a.75.75 0 0 0 1.235.204l3.938-4.241L21 8.25z"
                ></path>
              </svg>
            </span>
            <span>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                fill="none"
                viewBox="0 0 24 24"
              >
                <path
                  fill="#262626"
                  d="M20.23 11.079a.75.75 0 0 0-.468-.531L14.36 8.522l1.374-6.875a.75.75 0 0 0-1.283-.656l-10.5 11.25a.75.75 0 0 0 .28 1.219l5.404 2.026-1.371 6.867a.75.75 0 0 0 1.283.656l10.5-11.25a.75.75 0 0 0 .182-.68m-9.977 8.984.982-4.911a.75.75 0 0 0-.469-.85l-4.954-1.86 7.934-8.5-.981 4.91a.75.75 0 0 0 .469.85l4.95 1.857z"
                ></path>
              </svg>
            </span>
            <span>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                fill="none"
                viewBox="0 0 24 24"
              >
                <path
                  fill="#262626"
                  d="M17.25 3H6.75a1.5 1.5 0 0 0-1.5 1.5V21a.75.75 0 0 0 1.148.636L12 18.134l5.603 3.502A.75.75 0 0 0 18.75 21V4.5a1.5 1.5 0 0 0-1.5-1.5m0 1.5v10.647l-4.853-3.033a.75.75 0 0 0-.795 0L6.75 15.146V4.5zm-4.853 12.114a.75.75 0 0 0-.795 0L6.75 19.647v-2.732l5.25-3.28 5.25 3.28v2.732z"
                ></path>
              </svg>
            </span>
          </div>
        </div>
        <div className="flex gap-4 flex-wrap mt-6">
          <div className="bg-white border border-orange-100 text-orange-100 px-6 py-3 rounded-full">
            {jobType}
          </div>
          <div className="bg-white text-gray-100 border border-gray-100 px-6 py-3 rounded-full">
            {cityName}
          </div>
          <div className="bg-white text-gray-100 border border-gray-100 px-6 py-3 rounded-full">
            {salaryRange}
          </div>
          <div className="ml-auto flex gap-x-3 gap-y-5">
            <PrimaryButton text="Message" link={"#"} icon={<ChatDots />} />
            <PrimaryButton text="Download CV" link={"#"} icon={<FileArrowDown />} />
          </div>
        </div>
      </div>
      <div className="flex mt-8">
        <div className="mr-14">
          <div>
            <h2 className="text-2xl font-bold text-black-100 mb-3">Bio</h2>
            <p className="text-base font-normal text-gray-100 leading-6">{bioDescription}</p>
          </div>
          <div className="my-6">
            <h2 className="text-2xl font-bold text-black-100 mb-3">Education</h2>
            <CandidateDetailInfo
              date="September 2016 - June 2020"
              description="Bachelor of Marketing and Media"
              title="University of North Texas"
              icon={<MortarboardIcon />}
            />
          </div>
          <div className="my-6">
            <h2 className="text-2xl font-bold text-black-100 mb-3">Work Experience</h2>
            <div className="mb-3">
              <CandidateDetailInfo
                date="September 2022 - Present"
                description="Junior Graphic Designer"
                title="Junior Graphic Designer"
                icon={<BriefcaseIcon2 />}
                descriptionList={[
                  "Assisted in designing brand identities, marketing materials, and website layouts.",
                  "Created engaging social media graphics that increased client engagement by 35%.",
                  "Collaborated with the UI/UX team to develop user-friendly interfaces and prototypes.",
                  "Participated in brainstorming sessions to generate fresh and creative design concepts.",
                ]}
              />
            </div>
            <div className="mb-3">
              <CandidateDetailInfo
                date="September 2022 - Present"
                description="Junior Graphic Designer"
                title="Junior Graphic Designer"
                icon={<BriefcaseIcon2 />}
                descriptionList={[
                  "Assisted in designing brand identities, marketing materials, and website layouts.",
                  "Created engaging social media graphics that increased client engagement by 35%.",
                  "Collaborated with the UI/UX team to develop user-friendly interfaces and prototypes.",
                  "Participated in brainstorming sessions to generate fresh and creative design concepts.",
                ]}
              />
            </div>
          </div>
          <div className="my-6">
            <h2 className="text-2xl font-bold text-black-100 mb-3">Certifications & Diploma</h2>
            <div className="mb-3">
              <CandidateDetailInfo
                date="September 2022 - Present"
                description="University of North Texas"
                title="Adobe Certified Expert – Photoshop"
                icon={<DiplomaIcon />}
              />
            </div>
            <div className="mb-3">
              <CandidateDetailInfo
                date="September 2022 - Present"
                description="University of North Texas"
                title="Adobe Certified Expert – Photoshop"
                icon={<DiplomaIcon />}
              />
            </div>
          </div>
          <div className="my-6">
            <h2 className="text-2xl font-bold text-black-100 mb-3">Achievements</h2>
            <div className="mb-3">
              <CandidateDetailInfo
                date="September 2022"
                description=" Creative Studio NY "
                title="Designed a social media campaign that increased brand engagement by 35% "
                icon={<Trophy />}
              />
            </div>
            <div className="mb-3">
              <CandidateDetailInfo
                date="September 2022"
                description=" Creative Studio NY "
                title="Designed a social media campaign that increased brand engagement by 35% "
                icon={<Trophy />}
              />
            </div>
          </div>

          <div className="mb-6">
            <h2 className="text-2xl font-bold text-black-100 mb-3">Skills Tags</h2>
            <ul className="flex flex-wrap gap-4">
              {skillsTags.map((item, index) => (
                <li
                  key={index}
                  className="border border-gray-300 inline-block text-gray-100 text-base font-normal px-6 py-3 rounded-full"
                >
                  {item}
                </li>
              ))}
            </ul>
          </div>
        </div>
        <div className="space-y-7 shadow w-[300px] rounded-2xl border h-fit px-6 py-8">
          <div className="flex items-center">
            <div className="bg-offWhite-100 w-[60px] h-[60px] flex justify-center items-center rounded-full mr-3">
              <MapPin />
            </div>
            <div>
              <span className="text-black-100 font-medium block mb-1">Location</span>
              <span className="text-gray-100 font-normal block">{location}</span>
            </div>
          </div>
          <div className="flex items-center">
            <div className="bg-offWhite-100 w-[60px] h-[60px] flex justify-center items-center rounded-full mr-3">
              <CurrencyIcon />
            </div>
            <div>
              <span className="text-black-100 font-medium block mb-1">Expected Salary</span>
              <span className="text-gray-100 font-normal block">{expectedSalary}</span>
            </div>
          </div>
          <div className="flex items-center">
            <div className="bg-offWhite-100 w-[60px] h-[60px] flex justify-center items-center rounded-full mr-3">
              <ExperienceIcon />
            </div>
            <div>
              <span className="text-black-100 font-medium block mb-1">Experience</span>
              <span className="text-gray-100 font-normal block">{experience}</span>
            </div>
          </div>
          <div className="flex items-center">
            <div className="bg-offWhite-100 w-[60px] h-[60px] flex justify-center items-center rounded-full mr-3">
              <QualificationIcon />
            </div>
            <div>
              <span className="text-black-100 font-medium block mb-1">Qualification</span>
              <span className="text-gray-100 font-normal block">{qualification}</span>
            </div>
          </div>
          <div className="flex items-center">
            <div className="bg-offWhite-100 w-[60px] h-[60px] flex justify-center items-center rounded-full mr-3">
              <EnvelopeSimpleIcon />
            </div>
            <div>
              <span className="text-black-100 font-medium block mb-1">Email</span>
              <span className="text-gray-100 font-normal block">{email}</span>
            </div>
          </div>
          <div className="border-t pt-4 ">
            <h3 className="text-black-100 font-bold mb-4">Social Profiles</h3>
            <div className="flex text-orange-100 gap-x-4">
              <Link href={linkdinLink}>
                <LinkdinIcon />
              </Link>
              <Link href={instaLink}>
                <InstagramIcon />
              </Link>
              <Link href={Xlink}>
                <XIcon />
              </Link>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default CompanyDetail;
