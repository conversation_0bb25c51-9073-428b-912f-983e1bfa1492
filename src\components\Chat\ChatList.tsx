"use client";

import { useQueryClient } from "@tanstack/react-query";
import { formatDistanceToNow } from "date-fns";
import { ArrowLeft, Search } from "lucide-react";
import Link from "next/link";
import { useState, useEffect } from "react";
import ConversationActions from "@/components/Chat/ConversationActions";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Input } from "@/components/ui/input";
import { debugLog } from "@/lib/debug";
import { cn } from "@/lib/utils";
import { useChatStore } from "@/store/useChatStore";
import { useUserStore } from "@/store/useUserStore";
import { UserRole } from "@/types/common.types";
import { IConversation, IDeletedBy } from "@/types/query.types";

interface ChatListProps {
  conversations: IConversation[];
  selectedConversationId: string | null;
  onSelectConversation: (conversationId: string) => void;
}

export default function ChatList({
  conversations,
  selectedConversationId,
  onSelectConversation,
}: ChatListProps) {
  const [searchQuery, setSearchQuery] = useState("");
  const { currentUser } = useUserStore();
  const { lastMessages } = useChatStore();
  const queryClient = useQueryClient();

  // Filter conversations based on search query
  const filteredConversations = conversations.filter((conversation) => {
    const searchString = searchQuery.toLowerCase();
    const jobSeekerName =
      `${conversation.jobSeekerProfileId.userProfile.firstName} ${conversation.jobSeekerProfileId.userProfile.lastName}`.toLowerCase();
    const recruiterName = conversation.recruiterProfileId.companyProfile.companyName.toLowerCase();
    const jobTitle = conversation.jobApplication?.job.jobTitle.toLowerCase() || "";

    return (
      jobSeekerName.includes(searchString) ||
      recruiterName.includes(searchString) ||
      jobTitle.includes(searchString)
    );
  });

  // Get last message for a conversation, prioritizing store over API data
  const getLastMessage = (conversation: IConversation) => {
    // First check if we have a last message in the store
    const storeLastMessage = lastMessages[conversation._id];
    if (storeLastMessage) {
      debugLog(`[ChatList] Using store last message for conversation ${conversation._id}`);
      return storeLastMessage;
    }

    // If not in store, check if we have messages for this conversation in the store
    // const { messages } = useChatStore.getState();
    // const conversationMessages = messages[conversation._id] || [];

    // if (conversationMessages.length > 0) {
    //   // Get the most recent message
    //   const mostRecentMessage = [...conversationMessages].sort(
    //     (a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
    //   )[0];

    //   // Update the lastMessages store with this message
    //   useChatStore.getState().updateLastMessage(conversation._id, mostRecentMessage);

    //   debugLog(
    //     `[ChatList] Using most recent message from store for conversation ${conversation._id}`
    //   );
    //   return mostRecentMessage;
    // }

    // Fall back to API data
    if (conversation.lastMessage) {
      // If API data has a last message, store it in the chat store for future use
      useChatStore.getState().updateLastMessage(conversation._id, conversation.lastMessage);
      debugLog(`[ChatList] Storing API last message in store for conversation ${conversation._id}`);
    } else {
      debugLog(`[ChatList] No last message available for conversation ${conversation._id}`);
    }

    return conversation.lastMessage;
  };

  // Log current user role and conversations for debugging
  useEffect(() => {
    debugLog("[ChatList] Current user role:", currentUser?.role);
    debugLog("[ChatList] Current user ID:", currentUser?._id);
    debugLog("[ChatList] Is job seeker:", currentUser?.role === UserRole.JOBSEEKER);

    // Log conversations and their deletedBy arrays
    if (conversations && conversations.length > 0) {
      debugLog("[ChatList] Number of conversations:", conversations.length);

      // Log conversations with deletedBy arrays
      const conversationsWithDeletedBy = conversations.filter(
        (conv) => conv.deletedBy && conv.deletedBy.length > 0
      );

      if (conversationsWithDeletedBy.length > 0) {
        debugLog(
          "[ChatList] Conversations with deletedBy arrays:",
          conversationsWithDeletedBy.map((conv) => ({
            id: conv._id,
            deletedBy: conv.deletedBy,
          }))
        );
      } else {
        debugLog("[ChatList] No conversations with deletedBy arrays found");
      }
    } else {
      debugLog("[ChatList] No conversations available");
    }
  }, [currentUser?.role, currentUser?._id, conversations]);

  // Handle conversation deletion
  const handleConversationDeleted = (conversationId?: string) => {
    // Log that we're handling a conversation deletion
    debugLog(
      "[ChatList] Handling conversation deletion",
      conversationId ? `for ID: ${conversationId}` : ""
    );

    // Force immediate refetch of the conversations list to update UI
    // This is still needed to get the updated deletedBy array from the API
    debugLog("[ChatList] Forcing immediate refetch of conversations list");
    queryClient.refetchQueries({ queryKey: ["get-conversations"] });

    // If we have a specific conversationId, update the store
    if (conversationId) {
      debugLog(`[ChatList] Updating store for deleted conversation ID: ${conversationId}`);

      // Mark the conversation as deleted in the store
      useChatStore.getState().deleteConversation(conversationId);

      // Clear messages for this conversation from the store
      useChatStore.getState().clearMessages(conversationId);

      // Remove the conversation data from the cache completely
      // This is still needed to ensure we don't have stale data in the cache
      queryClient.removeQueries({ queryKey: ["get-messages", conversationId] });
      queryClient.removeQueries({ queryKey: ["get-messages-infinite", conversationId] });

      // If this was the selected conversation, deselect it
      if (selectedConversationId === conversationId) {
        debugLog(`[ChatList] Deselecting deleted conversation: ${conversationId}`);
        onSelectConversation(""); // Pass empty string to deselect
      }
    }

    // Note: After refetching, the filter in the render function will automatically
    // hide conversations where the current user's ID is in the deletedBy array
  };

  return (
    <div className="flex flex-col h-full">
      <div className="p-4 border-b border-gray-200 flex items-center gap-2">
        <Link
          href={`${currentUser?.role === "RECRUITER" ? "/company-dashboard/all-jobs" : "/dashboard/applied-jobs"}`}
        >
          <ArrowLeft className="h-5 w-6 text-gray-500" />
        </Link>
        <h1 className="text-3xl font-bold text-black-100">All Messages</h1>
      </div>

      <div className="p-4">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-100" />
          <Input
            placeholder="Search conversations"
            className="pl-10 bg-gray-300 rounded-lg h-10"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
      </div>

      <div className="flex-1 overflow-y-auto">
        {filteredConversations.map((conversation) => {
          const lastMessage = getLastMessage(conversation);
          const isSelected = conversation._id === selectedConversationId;
          const isDeleted =
            conversation.deletedBy?.some(
              (deletedBy: IDeletedBy) => deletedBy.userId === currentUser?._id
            ) || false;

          if (isDeleted) return null;

          return (
            <div
              key={conversation._id}
              className={cn(
                "relative flex items-center gap-3 p-3 cursor-pointer hover:bg-zinc-300",
                isSelected && "bg-zinc-300"
              )}
              onClick={() => onSelectConversation(conversation._id)}
            >
              {/* Conversation Actions Menu */}

              <ConversationActions
                conversationId={conversation._id}
                conversationName={
                  currentUser?.role === UserRole.RECRUITER
                    ? `${conversation.jobSeekerProfileId.userProfile.firstName} ${conversation.jobSeekerProfileId.userProfile.lastName}`
                    : conversation.recruiterProfileId.companyProfile.companyName
                }
                onDeleted={handleConversationDeleted}
              />
              <div className="relative">
                <Avatar className="h-12 w-12">
                  {currentUser?.role === UserRole.RECRUITER ? (
                    <AvatarImage
                      src={conversation.recruiterProfileId.companyProfile.profilePicture}
                      alt={conversation.recruiterProfileId.companyProfile.companyName}
                    />
                  ) : (
                    <AvatarImage
                      src={conversation.jobSeekerProfileId.userProfile?.profilePicture}
                      alt={conversation.jobSeekerProfileId.userProfile?.firstName}
                    />
                  )}
                  <AvatarFallback>
                    {currentUser?.role === UserRole.RECRUITER
                      ? conversation.recruiterProfileId.companyProfile.companyName.charAt(0)
                      : conversation.jobSeekerProfileId.userProfile?.firstName.charAt(0)}
                  </AvatarFallback>
                </Avatar>
              </div>

              <div className="flex-1 min-w-0">
                <div>
                  <h3 className="font-medium truncate">
                    {currentUser?.role === UserRole.RECRUITER
                      ? `${conversation.jobSeekerProfileId.userProfile.firstName} ${conversation.jobSeekerProfileId.userProfile.lastName}`
                      : conversation.recruiterProfileId.companyProfile.companyName}
                  </h3>
                </div>
                {lastMessage && (
                  <p className="text-sm text-gray-500 truncate">
                    {lastMessage.content || "Media message"}
                  </p>
                )}
                {lastMessage && (
                  <span className="text-xs text-gray-500">
                    {formatDistanceToNow(new Date(lastMessage.createdAt), {
                      addSuffix: true,
                    })}
                  </span>
                )}
              </div>
            </div>
          );
        })}

        {filteredConversations.length === 0 && (
          <div className="p-4 text-center text-gray-500">No conversations yet</div>
        )}
      </div>
    </div>
  );
}
