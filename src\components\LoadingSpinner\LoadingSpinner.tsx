import React from "react";

interface LoadingSpinnerProps {
  size?: "sm" | "small" | "medium" | "large";
  color?: string;
}

const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  size = "medium",
  color = "orange-100",
}) => {
  const sizeClasses = {
    sm: "w-4 h-4",
    small: "w-5 h-5",
    medium: "w-8 h-8",
    large: "w-12 h-12",
  };

  return (
    <div className="flex justify-center items-center">
      <div
        className={`animate-spin rounded-full border-t-2 border-b-2 border-${color} ${sizeClasses[size]}`}
      ></div>
    </div>
  );
};

export default LoadingSpinner;
