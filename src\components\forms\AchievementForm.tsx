"use client";

import React, { useEffect } from "react";
import { useForm, type SubmitHandler } from "react-hook-form";
import { Input } from "../ui/input";
import { Label } from "../ui/label";
import { Textarea } from "../ui/textarea";
import { IAchievement } from "@/types/query.types";

const inputClasses = "h-12 px-4 rounded-full border border-gray-300 w-full";

interface AchievementFormProps {
  goBack: () => void;
  onSuccess: (formData: IAchievement[]) => void;
  onError: (error: unknown) => void;
  defaultValues?: IAchievement[];
  isButton?: boolean;
}

const AchievementForm = ({
  goBack,
  onSuccess,
  onError,
  defaultValues = [],
  isButton = true,
}: AchievementFormProps) => {
  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm<IAchievement>({
    defaultValues: {
      title: "",
      instituteName: "",
      details: "",
      date: "",
    },
  });

  // Get current date in YYYY-MM-DD format for validation
  const today = new Date().toISOString().split("T")[0];

  // Format date from ISO string to YYYY-MM-DD for date inputs
  const formatDateForInput = (dateString: string | undefined): string => {
    if (!dateString) return "";
    const date = new Date(dateString);
    if (isNaN(date.getTime())) return "";
    return date.toISOString().split("T")[0] || "";
  };

  // Set form values when editing an existing achievement
  useEffect(() => {
    if (defaultValues.length > 0 && defaultValues[0]) {
      const achievement = defaultValues[0];
      reset({
        ...achievement,
        date: formatDateForInput(achievement.date),
      });
    } else {
      // Clear form when adding a new achievement
      reset({
        title: "",
        instituteName: "",
        details: "",
        date: "",
      });
    }
  }, [defaultValues, reset]);

  const onSubmit: SubmitHandler<IAchievement> = async (data) => {
    try {
      const formattedData = {
        ...data,
        _id: defaultValues[0]?._id || "", // Include _id if editing existing entry
        date: new Date(data.date).toISOString(),
      };

      onSuccess([formattedData]);
    } catch (error: unknown) {
      onError(error);
    }
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      <div className="mb-6">
        <Label htmlFor="title" className="mb-3 block">
          Title
        </Label>
        <Input
          className={inputClasses}
          id="title"
          {...register("title", {
            required: "Title is required",
          })}
        />
        {errors.title && <p className="text-red-500 text-sm">{errors.title.message}</p>}
      </div>

      <div className="grid sm:grid-cols-2 gap-6 mb-6">
        <div>
          <Label htmlFor="date" className="mb-3 block">
            Date
          </Label>
          <Input
            className={`${inputClasses} w-full block text-gray-100`}
            type="date"
            id="date"
            max={today}
            {...register("date", {
              required: "Date is required",
              validate: (value) => {
                if (!value) return true;
                const selectedDate = new Date(value);
                const currentDate = new Date();
                currentDate.setHours(0, 0, 0, 0);

                if (selectedDate > currentDate) {
                  return "Date cannot be in the future";
                }

                return true;
              },
            })}
          />
          {errors.date && <p className="text-red-500 text-sm">{errors.date.message}</p>}
        </div>
        <div>
          <Label htmlFor="instituteName" className="mb-3 block">
            Event/Institute
          </Label>
          <Input
            className={inputClasses}
            id="instituteName"
            {...register("instituteName", {
              required: "Event/Institute name is required",
            })}
          />
          {errors.instituteName && (
            <p className="text-red-500 text-sm">{errors.instituteName.message}</p>
          )}
        </div>
      </div>

      <div className="mb-6">
        <Label htmlFor="details" className="mb-3 block">
          Add Some detail <span className="text-[#8B8B8D] text-sm">(optional)</span>
        </Label>
        <Textarea
          id="details"
          className="min-h-[195px] px-4 py-2 rounded-3xl border border-gray-300"
          placeholder="Add description..."
          {...register("details")}
        />
      </div>
      {isButton && (
        <div className="flex flex-wrap gap-y-3 gap-x-5 mt-10">
          <button
            onClick={(e) => {
              e.preventDefault();
              goBack();
            }}
            type="button"
            className="font-bold py-4 px-10 font-base rounded-full inline-flex items-center justify-center space-x-2 bg-gray-300 text-gray-100"
          >
            Go Back
          </button>
          <button
            type="submit"
            className="font-bold py-4 px-10 font-base rounded-full inline-flex items-center justify-center space-x-2 bg-orange-100 text-white"
          >
            Save Details
          </button>
        </div>
      )}
    </form>
  );
};

export default AchievementForm;
