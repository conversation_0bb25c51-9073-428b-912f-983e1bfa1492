"use client";

import { useChatStore } from "@/store/useChatStore";
// import { Badge } from "@/components/ui/badge";

export default function SocketStatus() {
  const { socketStatus } = useChatStore();

  // Only show in development mode
  if (process.env.NODE_ENV !== "development") {
    return null;
  }

  // const getStatusColor = () => {
  //   switch (socketStatus) {
  //     case SocketConnectionStatus.CONNECTED:
  //       return "bg-green-500";
  //     case SocketConnectionStatus.CONNECTING:
  //       return "bg-yellow-500";
  //     case SocketConnectionStatus.DISCONNECTED:
  //       return "bg-red-500";
  //     case SocketConnectionStatus.ERROR:
  //       return "bg-red-700";
  //     default:
  //       return "bg-gray-500";
  //   }
  // };

  return (
    <div className="fixed bottom-4 right-4 z-50">
      Socket: {socketStatus}
      {/* <Badge className={`${getStatusColor()} text-white`}>
      </Badge> */}
    </div>
  );
}
