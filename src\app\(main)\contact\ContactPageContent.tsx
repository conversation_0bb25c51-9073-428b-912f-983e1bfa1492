"use client";

import Image from "next/image";
import ContactForm from "../../../components/forms/ContactForm";
import ContactCard from "@/components/Cards/ContactCard";
import { PrimaryHeading } from "@/components/Headings/PrimaryHeading";
import { EmailIcon, MapTrifold } from "@/components/Icons";
import CTASection from "@/components/Sections/CTASection";
import MobileAppSection from "@/components/Sections/MobileAppSection";
import { useGetContactUsPage } from "@/hooks/useQuery";

const ContactPageContent = () => {
  const { data: contactUsPageData, isLoading, isError } = useGetContactUsPage();

  if (isLoading) {
    return (
      <div className="container mx-auto py-20 text-center">
        <div className="animate-pulse space-y-4">
          <div className="h-10 bg-gray-200 rounded w-1/2 mx-auto" />
          <div className="h-8 bg-gray-200 rounded w-2/3 mx-auto" />
          <div className="h-[400px] bg-gray-200 rounded-lg mt-10" />
        </div>
      </div>
    );
  }

  if (isError) {
    return (
      <div className="container mx-auto py-20 text-center text-red-500">
        Failed to load Contact Us content. Please try again later.
      </div>
    );
  }

  return (
    <>
      <section className="lg:pt-36 pt-14 lg:pb-20 pb-14 relative">
        <div className="absolute w-full left-0 z-[-1]">
          <Image alt="" src={"/images/spiral.png"} width={837} height={1920} className="w-full" />
        </div>
        <div className="container mx-auto">
          <div className="grid md:grid-cols-2 grid-cols-1 gap-x-16 gap-y-6">
            <div>
              <PrimaryHeading>
                <span> {contactUsPageData?.data.page.heading}</span>
              </PrimaryHeading>
              <p className="text-lg font-normal text-gray-100 mt-3 mb-6">
                {contactUsPageData?.data.page.description}
              </p>
              <ContactForm />
            </div>

            <div>
              <Image
                src={contactUsPageData?.data.page.image || "/images/contact-main.png"}
                alt="contact"
                width={720}
                height={798}
              />
            </div>
          </div>
        </div>
      </section>
      <section className="mb-20">
        <div className="container mx-auto">
          <div className="grid lg:grid-cols-2 gap-10 mb-20">
            <ContactCard description="<EMAIL>" icon={<EmailIcon />} title="Email" />
            {/* <ContactCard
              description="******-YES-JOBS"
              icon={<HeadsetIcon />}
              title="Phone:"
            /> */}
            <ContactCard
              description="The address is The address is suite2
Level 13
977 ann street"
              icon={<MapTrifold />}
              title="Address"
            />
          </div>
          <Image src={"/images/fgmap.png"} alt="" width={1520} height={750} />
        </div>
      </section>
      <CTASection />
      <MobileAppSection />
    </>
  );
};

export default ContactPageContent;
