"use client";

import { <PERSON><PERSON><PERSON>ir<PERSON>, Loader2 } from "lucide-react";
import React from "react";
import { toast } from "sonner";
import { PlusIcon } from "../Icons";
import { Alert, AlertDescription } from "@/components/ui/alert";

interface IUploadCVCardProps {
  onUpload: (file: File) => void;
  isUploading: boolean;
  uploadProgress?: number;
  isError?: boolean;
}

const UploadCVCard = ({
  onUpload,
  isUploading,
  uploadProgress = 0,
  isError = false,
}: IUploadCVCardProps) => {
  const handleCVUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    // Validate file size (e.g., max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      toast.error("File size should be less than 5MB");
      return;
    }

    // Validate file type (e.g., .pdf, .doc, .docx)
    const allowedTypes = [
      "application/pdf",
      "application/msword",
      "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
    ];
    if (!allowedTypes.includes(file.type)) {
      toast.error("Please upload a valid CV file (.pdf, .doc, .docx)");
      return;
    }

    onUpload(file);
  };

  return (
    <div className="flex flex-col">
      <div className="text-center sm:text-left sm:flex sm:h-[170px] sm:px-10 px-5 py-5 sm:py-0 justify-between items-center flex-shrink-0 border-2 rounded-[20px] border-dashed border-gray-300 bg-gray-200">
        <div className="mb-4 sm:mb-0">
          <p className="text-xl text-gray-100 font-normal">Upload CV file .pdf, .doc, .docx</p>
          {isUploading && (
            <div className="mt-2 flex items-center gap-2">
              <Loader2 className="h-4 w-4 animate-spin text-orange-100" />
              <span className="text-sm text-orange-100">
                {uploadProgress > 0
                  ? `Uploading... ${Math.round(uploadProgress)}%`
                  : "Preparing upload..."}
              </span>
            </div>
          )}
        </div>
        <div>
          <label
            htmlFor="cv-upload"
            className={`${isUploading ? "opacity-50 cursor-not-allowed" : "cursor-pointer"}`}
          >
            <PlusIcon isCenter />
            <input
              id="cv-upload"
              type="file"
              accept=".pdf,.doc,.docx"
              className="hidden"
              onChange={handleCVUpload}
              disabled={isUploading}
            />
          </label>
        </div>
      </div>

      {isError && (
        <Alert variant="destructive" className="mt-2">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            There was an error uploading your CV. Please try again.
          </AlertDescription>
        </Alert>
      )}

      {uploadProgress > 0 && uploadProgress < 100 && (
        <div className="w-full bg-gray-300 rounded-full h-2.5 mt-2">
          <div
            className="bg-orange-100 h-2.5 rounded-full transition-all duration-300"
            style={{ width: `${uploadProgress}%` }}
          ></div>
        </div>
      )}
    </div>
  );
};

export default UploadCVCard;
