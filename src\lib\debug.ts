/**
 * Debug utility functions for development-only logging
 */

/**
 * Type for console-loggable values
 * This represents all the types that can be safely logged to the console
 */
type LoggableValue =
  | string
  | number
  | boolean
  | null
  | undefined
  | Error
  | Date
  | RegExp
  | symbol
  | bigint
  | object
  | Array<unknown>
  | Map<unknown, unknown>
  | Set<unknown>
  | Record<string, unknown>
  | unknown; // Include unknown to handle cases where we can't determine the type

/**
 * Log a message to the console in development mode only
 * @param message The message to log
 * @param args Additional arguments to log
 */
export const debugLog = (message: string, ...args: LoggableValue[]): void => {
  if (process.env.NODE_ENV === "development") {
    console.log(message, ...args);
  }
};

/**
 * Log an error to the console in development mode only
 * @param message The error message to log
 * @param args Additional arguments to log
 */
export const debugError = (message: string, ...args: LoggableValue[]): void => {
  if (process.env.NODE_ENV === "development") {
    console.log(message, ...args);
  }
};

/**
 * Log a warning to the console in development mode only
 * @param message The warning message to log
 * @param args Additional arguments to log
 */
export const debugWarn = (message: string, ...args: LoggableValue[]): void => {
  if (process.env.NODE_ENV === "development") {
    console.log(message, ...args);
  }
};
