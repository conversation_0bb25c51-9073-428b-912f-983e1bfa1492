"use client";

import Image from "next/image";
import CTASection from "@/components/Sections/CTASection";
import MobileAppSection from "@/components/Sections/MobileAppSection";
import { useGetTermsAndConditionsPage } from "@/hooks/useQuery";
import { formatDate } from "@/utils/formatDate";

const TermsAndConditionsContent = () => {
  const { data: termsAndConditionsPageData } = useGetTermsAndConditionsPage();
  return (
    <>
      <header className="py-20 relative">
        <div className="absolute w-full top-0 left-0 z-[-1]">
          <Image
            alt="Background pattern"
            src="/images/spiral.png"
            width={837}
            height={1920}
            className="w-full"
            priority
          />
        </div>
        <div className="container mx-auto text-center">
          <h1 className="font-bold lg:text-5xl text-3xl leading-tight text-orange-100 mt-5 mx-auto">
            {termsAndConditionsPageData?.data.page.heading}
          </h1>
          <p className="text-gray-100 py-6 max-w-3xl mx-auto">
            <time dateTime={termsAndConditionsPageData?.data.page.lastUpdated}>
              {" "}
              Last Updated: {formatDate(termsAndConditionsPageData?.data.page.lastUpdated) || ""}
            </time>
          </p>
        </div>
      </header>

      <main className="pb-20">
        <div className="container mx-auto">
          <article className="bg-white rounded-2xl p-8 lg:p-12 shadow-sm">
            <div
              dangerouslySetInnerHTML={{
                __html: termsAndConditionsPageData?.data.page.HTMLContent || "",
              }}
            />
          </article>
        </div>
      </main>

      <CTASection />
      <MobileAppSection />
    </>
  );
};

export default TermsAndConditionsContent;
