"use client";

import { useQueryClient } from "@tanstack/react-query";
import { useRouter, useSearchParams } from "next/navigation";
import { useState, useEffect, useCallback, useMemo, Suspense } from "react";

// Local imports
import SearchBar from "../JobSearch/SearchBar";
import { FilterSelectsCandidate } from "../Sections/FilterSelectsCandidate";
import { JobSeekerPagination } from "./JobSeekerPagination";
import CandidateCardSimple from "@/components/Cards/CandidateCardSimple";
import { Button } from "@/components/ui/button";
import { DEFAULT_IMAGE } from "@/constants/app.constants";
import { useGetAllJobSeekers } from "@/hooks/useQuery";
import { formatDate } from "@/lib/utils";
import type { IJobSeeker, IJobSeekerSearchParams } from "@/types/query.types";

// Define the interface for the filter state
interface IJobSeekerFilterState extends IJobSeekerSearchParams {
  jobSeekerId?: string; // Separate jobSeekerId from search params
}

export default function JobSeekerListingPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  // We'll use queryClient later for invalidating queries
  const queryClient = useQueryClient();

  // Initialize filter state from URL parameters
  const initialFilters: IJobSeekerFilterState = {
    search: searchParams.get("search") || undefined,
    page: searchParams.get("page") ? parseInt(searchParams.get("page")!) : 1,
    limit: searchParams.get("limit") ? parseInt(searchParams.get("limit")!) : 10,
    // Parse coordinates if they exist in URL
    coordinates: searchParams.get("coordinates")
      ? (() => {
          try {
            // Try to parse as JSON first
            const coordsStr = searchParams.get("coordinates")!;
            if (coordsStr.startsWith("[") && coordsStr.endsWith("]")) {
              return JSON.parse(coordsStr) as [number, number];
            } else {
              // Fallback to comma-separated format
              return coordsStr.split(",").map(Number) as [number, number];
            }
          } catch (err) {
            console.error("Error parsing coordinates:", err);
            return undefined;
          }
        })()
      : undefined,
    maxDistance: searchParams.get("maxDistance")
      ? parseInt(searchParams.get("maxDistance")!)
      : undefined,
    jobType: searchParams.get("jobType") || undefined,
    jobCategory: searchParams.get("jobCategory") || undefined,
    jobSeekerId: searchParams.get("jobSeekerId") || undefined,
  };

  // State for search parameters and selected job
  const [searchState, setSearchState] = useState<IJobSeekerSearchParams>(initialFilters);
  const [pendingFilters, setPendingFilters] = useState<IJobSeekerFilterState>(initialFilters);
  const [selectedJobSeeker, setSelectedJobSeeker] = useState<IJobSeeker | null>(null);

  // Fetch job data
  const { data, isLoading, isError } = useGetAllJobSeekers(searchState);

  // Use useMemo to avoid recreating the jobSeekers array on every render
  const jobSeekers = useMemo(() => data?.data.allJobSeekers || [], [data?.data.allJobSeekers]);
  const pagination = data?.data.pagination;

  // Use useCallback for updateUrl function to avoid recreating it on every render
  const updateUrl = useCallback(
    (params: IJobSeekerFilterState) => {
      const urlParams = new URLSearchParams();
      const { jobSeekerId, ...searchParams } = params;

      Object.entries(searchParams).forEach(([key, value]) => {
        if (value) {
          if (key === "coordinates" && Array.isArray(value)) {
            // Format coordinates as JSON string for the URL
            urlParams.set(key, JSON.stringify(value));
          } else {
            urlParams.set(key, String(value));
          }
        }
      });

      // Add jobSeekerId to URL without including it in search params
      const url = urlParams.toString()
        ? `?${urlParams.toString()}${jobSeekerId ? `#${jobSeekerId}` : ""}`
        : jobSeekerId
          ? `#${jobSeekerId}`
          : "";

      router.push(`/for-recruiters${url}`, { scroll: false });
    },
    [router]
  );

  // Handle filter changes (stored in pending state)
  const handleFilterChange = (key: keyof IJobSeekerSearchParams, value: string | undefined) => {
    setPendingFilters((prev) => ({
      ...prev,
      [key]: value,
    }));
  };

  // Handle search submission
  const handleSearch = (params: { search: string; coordinates?: [number, number] }) => {
    setPendingFilters((prev) => ({
      ...prev,
      search: params.search,
      coordinates: params.coordinates,
      page: 1, // Reset to first page on new search
    }));
  };

  // Handle filter application
  const handleApplyFilters = () => {
    setSearchState((prev) => ({
      ...pendingFilters,
      page: pendingFilters.page || prev.page, // Keep current page if not specified
    }));
    updateUrl(pendingFilters);

    // Invalidate the query to ensure fresh data
    queryClient.invalidateQueries({ queryKey: ["get-all-jobseekers"] });
  };

  // Handle clear filters
  const handleClearFilters = () => {
    const clearedFilters = {
      search: "",
      page: 1,
      limit: 10,
    };
    setPendingFilters(clearedFilters);
    setSearchState(clearedFilters);
    updateUrl(clearedFilters);
  };

  // Handle pagination
  const handlePageChange = (page: number) => {
    const newFilters = {
      ...searchState,
      page,
    };
    setSearchState(newFilters);
    setPendingFilters(newFilters);
    updateUrl(newFilters);
  };

  // Handle per page change
  const handlePerPageChange = (limit: number) => {
    const newFilters = {
      ...searchState,
      limit,
      page: 1, // Reset to first page when changing items per page
    };
    setSearchState(newFilters);
    setPendingFilters(newFilters);
    updateUrl(newFilters);
  };

  // Handle job selection
  const handleJobSeekerSelect = (jobSeeker: IJobSeeker) => {
    setSelectedJobSeeker(jobSeeker);
    // Update URL with selected job ID
    updateUrl({
      ...pendingFilters,
      jobSeekerId: jobSeeker._id,
    });
  };

  // Effect to set selected job from URL hash
  useEffect(() => {
    const jobSeekerId = window.location.hash.replace("#", "");
    if (jobSeekerId && jobSeekers.length > 0) {
      const foundJobSeeker = jobSeekers.find((js) => js._id === jobSeekerId);
      if (foundJobSeeker) {
        setSelectedJobSeeker(foundJobSeeker);
      }
    } else if (jobSeekers.length > 0 && !selectedJobSeeker) {
      // Auto-select first job if none is selected
      const firstJobSeeker = jobSeekers[0];
      if (firstJobSeeker) {
        setSelectedJobSeeker(firstJobSeeker);
      }
    }
  }, [jobSeekers, selectedJobSeeker]);

  // Helper function to format salary range
  const formatSalaryRange = (jobSeeker: IJobSeeker) => {
    if (!jobSeeker.jobPreferences.salaryRangeStart && !jobSeeker.jobPreferences.salaryRangeEnd) {
      return "Not specified";
    }

    const start = jobSeeker.jobPreferences.salaryRangeStart
      ? `$${jobSeeker.jobPreferences.salaryRangeStart.toLocaleString()}`
      : "";

    const end = jobSeeker.jobPreferences.salaryRangeEnd
      ? `$${jobSeeker.jobPreferences.salaryRangeEnd.toLocaleString()}`
      : "";

    return start && end ? `${start} - ${end}` : start || end;
  };

  return (
    <Suspense fallback={<div>Loading...</div>}>
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold mb-4">Find Candidates</h1>
          <SearchBar onSearch={handleSearch} />
        </div>

        <div className="flex flex-col lg:flex-row gap-6">
          <div className="w-full lg:w-2/3">
            <div className="mb-6 overflow-x-auto">
              <FilterSelectsCandidate
                filters={pendingFilters}
                onFilterChange={handleFilterChange}
              />
              <div className="flex gap-4 mt-4">
                <Button
                  onClick={handleApplyFilters}
                  className="bg-orange-100 hover:bg-orange-200 text-white"
                >
                  Apply Filters
                </Button>
                <Button
                  onClick={handleClearFilters}
                  variant="outline"
                  className="border-orange-100 text-orange-100 hover:bg-orange-50"
                >
                  Clear Filters
                </Button>
              </div>
            </div>

            {isLoading ? (
              <div className="flex justify-center items-center h-64">
                <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-orange-100"></div>
              </div>
            ) : isError ? (
              <div className="text-center py-8 text-red-500">
                Error loading candidates. Please try again later.
              </div>
            ) : jobSeekers.length === 0 ? (
              <div className="text-center py-8">No candidates found matching your criteria.</div>
            ) : (
              <>
                <div className="grid grid-cols-1 gap-4 mb-6">
                  {jobSeekers.map((jobSeeker) => (
                    <div
                      key={jobSeeker._id}
                      onClick={() => handleJobSeekerSelect(jobSeeker)}
                      className={`cursor-pointer transition-all ${
                        selectedJobSeeker?._id === jobSeeker._id
                          ? "border-2 border-orange-100"
                          : "border border-gray-200 hover:border-orange-50"
                      }`}
                    >
                      <CandidateCardSimple
                        imageUrl={jobSeeker.userProfile.profilePicture || DEFAULT_IMAGE}
                        name={`${jobSeeker.userProfile.firstName} ${jobSeeker.userProfile.lastName}`}
                        jobTitle={
                          jobSeeker.jobPreferences.jobCategory?.[0]?.replace(/_/g, " ") ||
                          "Not specified"
                        }
                        location={jobSeeker.userProfile.location.city}
                        skills={jobSeeker.skills}
                        onClick={() => handleJobSeekerSelect(jobSeeker)}
                      />
                    </div>
                  ))}
                </div>

                {pagination && (
                  <div className="flex flex-col sm:flex-row justify-between items-center mt-6 mb-8">
                    <div className="text-sm text-gray-500 mb-4 sm:mb-0">
                      Showing {(pagination.currentPage - 1) * pagination.pageSize + 1} -{" "}
                      {Math.min(
                        pagination.currentPage * pagination.pageSize,
                        pagination.totalRecords
                      )}{" "}
                      of {pagination.totalRecords} candidates
                    </div>
                    <JobSeekerPagination
                      currentPage={pagination.currentPage}
                      totalPages={pagination.pages}
                      onPageChange={handlePageChange}
                      perPage={pagination.pageSize}
                      onPerPageChange={handlePerPageChange}
                    />
                  </div>
                )}
              </>
            )}
          </div>

          <div className="w-full lg:w-1/3 bg-white rounded-lg shadow-sm p-6">
            {selectedJobSeeker ? (
              <div className="space-y-6">
                <div className="flex items-center space-x-4">
                  <img
                    src={selectedJobSeeker.userProfile.profilePicture || DEFAULT_IMAGE}
                    alt={`${selectedJobSeeker.userProfile.firstName} ${selectedJobSeeker.userProfile.lastName}`}
                    className="w-20 h-20 rounded-full object-cover"
                  />
                  <div>
                    <h2 className="text-xl font-bold">
                      {selectedJobSeeker.userProfile.firstName}{" "}
                      {selectedJobSeeker.userProfile.lastName}
                    </h2>
                    <p className="text-gray-600">
                      {selectedJobSeeker.jobPreferences.jobCategory?.[0]?.replace(/_/g, " ") ||
                        "Not specified"}
                    </p>
                    <p className="text-gray-500">
                      {selectedJobSeeker.userProfile.location.city},{" "}
                      {selectedJobSeeker.userProfile.location.country}
                    </p>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <p className="text-gray-500">Job Type</p>
                    <p className="font-medium">
                      {selectedJobSeeker.jobPreferences.jobType?.replace(/_/g, " ") ||
                        "Not specified"}
                    </p>
                  </div>
                  <div>
                    <p className="text-gray-500">Salary Expectation</p>
                    <p className="font-medium">{formatSalaryRange(selectedJobSeeker)}</p>
                  </div>
                </div>

                <div>
                  <h3 className="font-semibold mb-2">Bio</h3>
                  <p className="text-gray-700">
                    {selectedJobSeeker.userProfile.shortBio || "No bio provided."}
                  </p>
                </div>

                <div>
                  <h3 className="font-semibold mb-2">Skills</h3>
                  <div className="flex flex-wrap gap-2">
                    {selectedJobSeeker.skills.map((skill, index) => (
                      <span
                        key={index}
                        className="bg-gray-100 text-gray-800 px-3 py-1 rounded-full text-sm"
                      >
                        {skill}
                      </span>
                    ))}
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <p className="text-gray-500">Phone</p>
                    <p className="font-medium">
                      {selectedJobSeeker.userProfile.phoneNo || "Not provided"}
                    </p>
                  </div>
                  <div>
                    <p className="text-gray-500">Date of Birth</p>
                    <p className="font-medium">
                      {selectedJobSeeker.userProfile.dob
                        ? formatDate(selectedJobSeeker.userProfile.dob)
                        : "Not provided"}
                    </p>
                  </div>
                </div>

                {(selectedJobSeeker.userProfile.websiteUrl ||
                  selectedJobSeeker.userProfile.portfolioUrl) && (
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    {selectedJobSeeker.userProfile.websiteUrl && (
                      <div>
                        <p className="text-gray-500">Website</p>
                        <a
                          href={selectedJobSeeker.userProfile.websiteUrl}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-orange-100 hover:underline font-medium"
                        >
                          Visit Website
                        </a>
                      </div>
                    )}
                    {selectedJobSeeker.userProfile.portfolioUrl && (
                      <div>
                        <p className="text-gray-500">Portfolio</p>
                        <a
                          href={selectedJobSeeker.userProfile.portfolioUrl}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-orange-100 hover:underline font-medium"
                        >
                          View Portfolio
                        </a>
                      </div>
                    )}
                  </div>
                )}

                <div className="pt-4 border-t border-gray-200">
                  <Button className="w-full bg-orange-100 hover:bg-orange-200 text-white">
                    Contact Candidate
                  </Button>
                </div>
              </div>
            ) : (
              <div className="p-8 text-center border rounded-lg">
                <p className="text-gray-500">Select a candidate to view details</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </Suspense>
  );
}
