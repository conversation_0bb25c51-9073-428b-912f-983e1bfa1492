"use client";
import React, { useState, useEffect, useMemo } from "react";

// import CompanyJobCard from "@/components/Cards/CompanyJobCard";
import DeleteJobModal from "./DeleteJobModal";
import CompanyJobCardtwo from "@/components/Cards/CompanyJobCardtwo";
import Pagination from "@/components/Pagination/Pagination";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"; // Import Shadcn Dialog components
import { DEFAULT_IMAGE } from "@/constants/app.constants";
import { useDeleteJob, useUpdateJobStatus, useBoostJob } from "@/hooks/useMutation"; // Import useBoostJob
import { useGetRecruiterJobs, useGetAdminSettings } from "@/hooks/useQuery"; // Import useGetAdminSettings
import { formatDate } from "@/lib/utils";
import { IBoostJobResponseDto } from "@/types/mutation.types"; // Import IBoostJobResponseDto

const JobList: React.FC = () => {
  const [page, setPage] = useState(1);
  const [limit] = useState(10);
  const [jobToDelete, setJobToDelete] = useState<string | null>(null);

  // State for boost job modal
  const [isBoostModalOpen, setIsBoostModalOpen] = useState(false);
  const [jobToBoostId, setJobToBoostId] = useState<string | null>(null);
  const [selectedWeeks, setSelectedWeeks] = useState(1); // Default to 1 week

  const { data, isLoading, isError, refetch } = useGetRecruiterJobs({ page, limit });
  const { mutate: deleteJob } = useDeleteJob();
  const { mutate: updateJobStatus, isPending } = useUpdateJobStatus();
  const { mutate: boostJob, isPending: isBoosting } = useBoostJob(); // Use useBoostJob

  // Fetch admin settings for featured job price
  const { data: adminSettingsData } = useGetAdminSettings();
  const featuredJobPricePerWeek = adminSettingsData?.data?.FeaturedJobPricePerWeek || 0;

  // Calculate boost price dynamically
  const boostPrice = useMemo(() => {
    return featuredJobPricePerWeek * selectedWeeks;
  }, [featuredJobPricePerWeek, selectedWeeks]);

  const jobs = data?.data?.jobs || [];
  const pagination = data?.data?.pagination;

  useEffect(() => {
    refetch();
  }, [page, refetch]);

  const handlePageChange = (newPage: number) => {
    setPage(newPage);
  };

  const toggleJobStatus = (jobId: string, isJobActive: boolean) => {
    updateJobStatus({ jobId, isJobActive });
  };

  const handleDelete = (jobId: string) => {
    setJobToDelete(jobId); // Open confirmation modal
  };

  const confirmDelete = () => {
    if (jobToDelete) {
      deleteJob(jobToDelete, {
        onSuccess: () => {
          setJobToDelete(null); // Close modal after successful deletion
        },
      });
    }
  };

  const cancelDelete = () => {
    setJobToDelete(null); // Close modal without deleting
  };

  // Handle boost job button click
  const handleBoostJob = (jobId: string) => {
    setJobToBoostId(jobId);
    setIsBoostModalOpen(true);
  };

  // Confirm boost job
  const confirmBoost = () => {
    if (jobToBoostId) {
      boostJob(
        { jobId: jobToBoostId, weeks: selectedWeeks },
        {
          onSuccess: (data: IBoostJobResponseDto) => {
            setIsBoostModalOpen(false); // Close modal after successful boost
            setJobToBoostId(null);
            setSelectedWeeks(1); // Reset selected weeks
            if (data?.data?.url) {
              // Access url via data.data.url
              // window.location.href = data.data.url;
              window.open(data.data.url, "_blank");
              // router.push(data.data.url);
            }
          },
        }
      );
    }
  };

  // Cancel boost job
  const cancelBoost = () => {
    setIsBoostModalOpen(false);
    setJobToBoostId(null);
    setSelectedWeeks(1); // Reset selected weeks
  };

  if (isLoading) {
    return <div>Loading...</div>;
  }

  if (isError) {
    return <div>Failed to load jobs.</div>;
  }

  if (!jobs.length) {
    return <div>No jobs found</div>;
  }

  return (
    <>
      {jobs.map((job) => (
        <CompanyJobCardtwo
          key={job._id}
          jobId={job._id} // Pass jobId to CompanyJobCardtwo
          imageUrl={DEFAULT_IMAGE}
          jobTitle={job.jobTitle}
          companyName={job.recruiterProfile?.companyName || "Company"}
          cityName={job.location?.city || ""}
          salaryRange={`$${job.salaryRangeStart} - $${job.salaryRangeEnd}`}
          // viewJob={job.isJobActive}
          jobType={job.jobType}
          jobClosed={!job.isJobActive}
          handleDelete={() => handleDelete(job._id)}
          toggleJobStatus={() => toggleJobStatus(job._id, !job.isJobActive)}
          handleBoostJob={handleBoostJob} // Pass the new handler
          editJobLink={`/company-dashboard/post-job/${job._id}`}
          viewJobLink={`/company-dashboard/all-jobs/${job._id}`}
          category={job.jobCategory}
          dateCreated={formatDate(job.createdAt)}
          expireDate={formatDate(job.applicationDeadline)}
          shortlistedApplicants={job.shortlistedApplicantsCount || 0}
          totalApplicants={job.applicantsCount || 0}
          isLoading={isPending || isBoosting} // Pass the loading state to the card
          premiumExpireAt={job.premiumExpireAt} // Pass premiumExpireAt
        />
      ))}

      {pagination && pagination.pages > 1 && (
        <div className="flex justify-center mt-8">
          <Pagination
            currentPage={page}
            totalPages={pagination.pages}
            onPageChange={handlePageChange}
          />
        </div>
      )}

      {jobToDelete && <DeleteJobModal cancelDelete={cancelDelete} confirmDelete={confirmDelete} />}

      <Dialog open={isBoostModalOpen} onOpenChange={setIsBoostModalOpen}>
        <DialogContent className="w-full max-w-2xl p-0">
          <DialogHeader className="p-6 pb-0">
            <DialogTitle className="text-xl font-semibold">
              Need better reach for your job post? Boost it today and get noticed instantly!
            </DialogTitle>
            <DialogDescription className="text-gray-700">
              Current price per week: ${featuredJobPricePerWeek}
            </DialogDescription>
          </DialogHeader>
          <div className="p-6 pt-0">
            <div className="mb-6">
              <label htmlFor="weeks" className="block text-sm font-medium text-gray-700 mb-2">
                Select duration:
              </label>
              <select
                id="weeks"
                className="mt-1 block w-full pl-3 pr-10 py-2 text-base sm:text-sm rounded-md border-orange-100 border text-orange-100 h-[44px] cursor-pointer"
                value={selectedWeeks}
                onChange={(e) => setSelectedWeeks(Number(e.target.value))}
              >
                <option value={1}>1 Week</option>
                <option value={2}>2 Weeks</option>
                <option value={3}>3 Weeks</option>
                <option value={4}>4 Weeks</option>
              </select>
            </div>
            <p className="text-lg font-bold mb-6">Total Price: ${boostPrice}</p>
          </div>
          <DialogFooter className="p-6 pt-0 flex justify-end gap-4">
            <button
              onClick={cancelBoost}
              className="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300"
            >
              Cancel
            </button>
            <button
              onClick={confirmBoost}
              className="px-4 py-2 bg-orange-100 text-white rounded-md hover:bg-orange-200"
            >
              Confirm Boost
            </button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default JobList;
