"use client";
import { useQueryClient } from "@tanstack/react-query";
import { useRouter } from "next/navigation";
import React, { useState } from "react";
import { toast } from "sonner";
import AchievementInfo from "../Cards/AchievementInfo";
import { AchievementIcon } from "../Icons";
import AchievementForm from "./AchievementForm";
import {
  <PERSON>alog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { useUpdateJobSeekerProfile } from "@/hooks/useMutation";
import { useGetJobSeekerProfile } from "@/hooks/useQuery";
import { ApiError } from "@/types/common.types";
import { IAchievement } from "@/types/query.types";
import { formatHumanReadableDate } from "@/utils/date";

const AchievementsForms = ({ isButton = true }: { isButton?: boolean }) => {
  const router = useRouter();
  const [open, setOpen] = useState(false);
  const [editingAchievement, setEditingAchievement] = useState<IAchievement | null>(null);
  const queryClient = useQueryClient();
  const { data: profileData } = useGetJobSeekerProfile();
  const { mutate: updateProfile, isPending } = useUpdateJobSeekerProfile({
    onSuccess: () => {
      toast.success("Profile updated successfully");
      queryClient.invalidateQueries({ queryKey: ["get-jobseeker-profile"] });
    },
    onError: (error) => {
      toast.error(error.response?.data?.message || "Failed to update profile");
    },
  });

  const handleClose = () => {
    setOpen(false);
    // Use setTimeout to ensure the dialog is fully closed before resetting the editing state
    // This prevents the form from flashing with the previous data before closing
    setTimeout(() => {
      setEditingAchievement(null);
    }, 300);
  };

  const handleSuccess = async (formData: IAchievement[]) => {
    try {
      const existingAchievements = profileData?.data.achievements || [];
      const updatedAchievements = editingAchievement
        ? existingAchievements.map((achievement) =>
            achievement._id === editingAchievement._id ? formData[0] : achievement
          )
        : [...existingAchievements, ...formData];

      updateProfile({
        achievements: updatedAchievements as IAchievement[],
      });
      handleClose();
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Failed to save achievement";
      toast.error(errorMessage);
    }
  };

  const handleError = (error: unknown) => {
    const errorMessage = error instanceof Error ? error.message : "Failed to save achievement";
    toast.error(errorMessage);
  };

  const handleEdit = (achievement: IAchievement) => {
    setEditingAchievement(achievement);
    setOpen(true);
  };

  const handleDelete = async (achievementId: string) => {
    try {
      const existingAchievements = profileData?.data.achievements || [];
      const updatedAchievements = existingAchievements.filter(
        (achievement) => achievement._id !== achievementId
      );

      updateProfile({
        achievements: updatedAchievements,
      });
      toast.success("Achievement deleted successfully");
    } catch (error) {
      toast.error((error as ApiError).response?.data.message || "Failed to delete achievement");
    }
  };

  const achievements = profileData?.data.achievements || [];

  return (
    <div>
      <div className="md:flex items-center justify-between mb-10">
        <div className="mb-4 md:mb-0">
          <h2 className="text-blue-200 text-2xl font-bold">Achievements</h2>
          <h3 className="text-4xl font-medium text-black-100 mt-3">
            Tell us about your Achievements
          </h3>
        </div>
        <div>
          <Dialog
            open={open}
            onOpenChange={(isOpen) => {
              setOpen(isOpen);
              if (!isOpen) {
                // Use setTimeout to ensure the dialog is fully closed before resetting the editing state
                setTimeout(() => {
                  setEditingAchievement(null);
                }, 300);
              }
            }}
          >
            <DialogTrigger
              className="border-orange-100 border rounded-full px-3 py-3 flex items-center gap-x-3 font-medium text-orange-100"
              onClick={() => setEditingAchievement(null)}
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="30"
                height="30"
                fill="none"
                viewBox="0 0 30 30"
              >
                <path
                  stroke="#EC761E"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M15 29c7.732 0 14-6.268 14-14S22.732 1 15 1 1 7.268 1 15s6.268 14 14 14M10.333 15h9.334M15 10.333v9.333"
                ></path>
              </svg>
              Add Achievement
            </DialogTrigger>
            <DialogContent className="md:max-w-[920px] max-h-[80vh] overflow-y-auto">
              <DialogHeader className="text-left">
                <DialogTitle className="text-2xl text-orange-100 font-bold mb-5">
                  Achievement Details
                </DialogTitle>
                <AchievementForm
                  goBack={handleClose}
                  onSuccess={handleSuccess}
                  onError={handleError}
                  defaultValues={editingAchievement ? [editingAchievement] : []}
                />
              </DialogHeader>
            </DialogContent>
          </Dialog>
        </div>
      </div>
      <div className="space-y-4">
        {achievements.map((achievement: IAchievement, index: number) => (
          <AchievementInfo
            key={achievement._id || index}
            icon={<AchievementIcon />}
            date={achievement.date && formatHumanReadableDate(achievement.date)}
            detail={achievement.details}
            title={achievement.title}
            eventOrInstitute={achievement.instituteName}
            onEdit={() => handleEdit(achievement)}
            onDelete={() => handleDelete(achievement._id)}
          />
        ))}
        {achievements.length === 0 && (
          <div className="text-center text-gray-500 py-8">
            No achievements added yet. Click &quot;Add Achievement&quot; to get started.
          </div>
        )}
        {isButton && (
          <div className="flex gap-5">
            <div className={`flex flex-wrap gap-y-3 gap-x-5 mt-10`}>
              <button
                disabled={isPending}
                onClick={() => router.back()}
                type="submit"
                className="font-bold py-4 px-10 text-black font-base rounded-full inline-flex items-center justify-center space-x-2 bg-[#E7E7E7]"
              >
                Go Back
              </button>
            </div>
            <div className={`flex flex-wrap gap-y-3 gap-x-5 mt-10`}>
              <button
                disabled={isPending}
                onClick={() => router.push("?stepId=job-preferences")}
                className="font-bold py-4 px-10 font-base rounded-full inline-flex items-center justify-center space-x-2 bg-orange-100 text-white"
              >
                {isPending ? "Loading..." : "Save Details"}
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default AchievementsForms;
