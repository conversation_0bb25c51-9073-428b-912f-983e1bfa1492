import Link from "next/link";
import React from "react";
import { HandShakeIcon } from "../Icons";

interface IpricingDescription {
  text: string;
}

interface IPricingsCard {
  priceName: string;
  Price: string;
  duration: string;
  pricingDescription: IpricingDescription[];
  highlight?: boolean;
}

const PricingsCard = ({
  priceName,
  Price,
  duration,
  pricingDescription,
  highlight = false,
}: IPricingsCard) => {
  return (
    <div
      className={`${
        highlight ? "bg-orange-100 border-orange-100" : ""
      }  border border-gray-300 rounded-xl sm:flex py-7 px-5`}
    >
      <div className="sm:border-r sm:border-gray-300 sm:pr-5 mb-4 sm:mb-0">
        <div className="flex gap-x-2 mb-3">
          <span className="bg-black-100 text-white text-xs rounded-full px-3 py-2">
            {priceName}
          </span>
          <span
            className={`${
              highlight ? "bg-white text-orange-100" : "bg-orange-100 text-white"
            }  w-8 h-8 rounded-full  flex justify-center items-center`}
          >
            <HandShakeIcon />
          </span>
        </div>
        <h3 className={`${highlight ? "text-white" : "text-orange-100"} text-4xl  font-bold mb-2`}>
          ${Price}
        </h3>
        <p className={`${highlight ? "text-white" : "text-gray-100"}  text-sm font-medium`}>
          {duration}
        </p>
      </div>
      <div className="flex-grow sm:pl-5">
        <ul className="mb-5">
          {pricingDescription.map((item, index) => {
            return (
              <li
                key={index}
                className={`${highlight ? "text-white" : "text-gray-100"}  flex  text-sm pb-3 last:pb-0`}
              >
                {" "}
                <span className="mt-[2px] mr-2">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="15"
                    height="14"
                    fill="none"
                    viewBox="0 0 15 14"
                  >
                    <path
                      fill="currentColor"
                      d="M7.938 1.313a5.687 5.687 0 1 0 0 11.374 5.687 5.687 0 0 0 0-11.374m2.497 4.684L7.371 9.06a.437.437 0 0 1-.619 0L5.44 7.747a.438.438 0 0 1 .62-.619l1.003 1.003 2.752-2.753a.438.438 0 0 1 .62.619"
                    ></path>
                  </svg>
                </span>
                {item.text}
              </li>
            );
          })}
        </ul>
        <Link
          href={"#"}
          className={`${highlight ? "bg-white text-orange-100" : "bg-orange-100 text-white"} rounded-xl  font-semibold text-sm block text-center py-3 w-full`}
        >
          Get started
        </Link>
      </div>
    </div>
  );
};

export default PricingsCard;
