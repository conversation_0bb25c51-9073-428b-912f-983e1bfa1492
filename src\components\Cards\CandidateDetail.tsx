"use client";
import Image from "next/image";
import Link from "next/link";
import { useRouter } from "next/navigation";
import React, { useState } from "react";
import { toast } from "sonner";
import PrimaryButton from "../Buttons/PrimaryButton";
import {
  BriefcaseIcon2,
  ChatDots,
  CurrencyIcon,
  DiplomaIcon,
  EnvelopeSimpleIcon,
  ExperienceIcon,
  FileArrowDown,
  MapPin,
  MortarboardIcon,
  QualificationIcon,
  Trophy,
} from "../Icons";
import LoadingSpinner from "../LoadingSpinner/LoadingSpinner";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "../ui/tooltip";
import CandidateDetailInfo from "./CandidateDetailInfo";
import { useCreateConversation, useSaveCandidate, useUnsaveCandidate } from "@/hooks/useMutation";
interface CandidateDetailProps {
  imageUrl: string;
  jobTitle: string;
  category: string;
  jobType: string;
  cityName: string;
  downLoadCvUrl: string;
  salaryRange: string;
  candidateId: string;
  skillsExperience: string[];
  bioDescription: string;
  skillsTags: string[];
  designation: string;
  location: string;
  expectedSalary: string;
  experience: string;
  qualification: string;
  email: string;
  isProMember?: boolean;
  isAllCompanies?: boolean;
  isSaved?: boolean;
  proMembershipEndsAt?: string;

  // Optional dynamic data for education, work experience, and achievements
  academicExperiences?: {
    _id: string;
    instituteName: string;
    startDate: string;
    endDate: string;
    degree: string;
  }[];
  workExperiences?: {
    _id: string;
    organizationName: string;
    designation: string;
    startDate: string;
    endDate: string;
    jobDetails: string;
  }[];
  achievements?: {
    _id: string;
    title: string;
    instituteName: string;
    date: string;
  }[];
  certificates?: {
    _id: string;
    instituteName: string;
    startDate: string;
    endDate: string;
    certificate: string;
  }[];
  socialNetworks?: Array<{
    networkName: string;
    networkUrl: string;
  }>;
  proTrialEndsAt?: string;
}

const CandidateDetail: React.FC<CandidateDetailProps> = ({
  imageUrl,
  jobTitle,
  jobType,
  cityName,
  salaryRange,
  bioDescription,
  skillsTags,
  designation,
  location,
  expectedSalary,
  experience,
  qualification,
  email,
  socialNetworks,
  downLoadCvUrl,
  candidateId,
  proTrialEndsAt,
  academicExperiences = [],
  workExperiences = [],
  achievements = [],
  certificates = [],
  proMembershipEndsAt,
  isAllCompanies = false,
  isSaved = false,
}) => {
  const router = useRouter();
  const [isCreatingConversation, setIsCreatingConversation] = useState(false);
  const { mutate: createConversation } = useCreateConversation({
    onSuccess: (data) => {
      toast.success(data.message || "Conversation created successfully");

      // The useCreateConversation hook will automatically join the conversation
      // via socket, which will trigger the join_conversation event

      // Navigate to the message page with the new conversation ID
      router.push(`/message?id=${data.data._id}`);
      setIsCreatingConversation(false);
    },
    onError: (error) => {
      toast.error(error.response?.data?.message || "Failed to create conversation");
      setIsCreatingConversation(false);
    },
  });
  const handleCreateConversation = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    if (!candidateId) {
      toast.error("Cannot create conversation: Missing candidate ID");
      return;
    }

    setIsCreatingConversation(true);
    createConversation({
      type: "direct",
      jobSeekerProfileId: candidateId, // Using candidateId as jobApplicationId for direct messages
    });
  };
  const { mutate: saveCandidate, isPending: isSaving } = useSaveCandidate();
  const { mutate: unsaveCandidate, isPending: isUnsaving } = useUnsaveCandidate();

  const handleToggleSaveCandidate = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    if (!candidateId) {
      return;
    }

    if (isSaved) {
      // Unsave candidate
      unsaveCandidate(candidateId, {
        onSuccess: () => {
          toast.success("Candidate removed from saved list");
        },
        onError: () => {
          toast.error("Failed to remove candidate from saved list");
        },
      });
    } else {
      // Save candidate
      saveCandidate(candidateId, {
        onSuccess: () => {
          toast.success("Candidate saved successfully");
        },
        onError: () => {
          toast.error("Failed to save candidate");
        },
      });
    }
  };
  return (
    <>
      <div className="rounded-[18px] border bg-offWhite-100 border-gray-200 lg:p-[30px] p-4">
        <div className="flex items-center flex-wrap">
          <div className="mr-4">
            <Image
              src={imageUrl}
              alt="Company Logo"
              width={80}
              height={80}
              className="w-[80px] h-[80px] rounded-full"
            />
          </div>
          <div>
            <h2 className="text-2xl font-medium text-black-100 mb-3">{jobTitle}</h2>
            <p className="text-orange-100">{designation}</p>
          </div>
          <div className="flex space-x-4 ml-auto">
            {(() => {
              const endsAt = proMembershipEndsAt ?? proTrialEndsAt;
              const isProMember = endsAt ? new Date(endsAt) > new Date() : false;

              return (
                isProMember && (
                  <span>
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="30"
                      height="30"
                      fill="none"
                      viewBox="0 0 30 30"
                    >
                      <rect width="30" height="30" fill="#007AB5" rx="15"></rect>
                      <path
                        fill="#fff"
                        d="M21.431 11.6a.99.99 0 0 0-1.181.237l-2.104 2.268-2.238-5.018V9.08a1 1 0 0 0-1.816 0v.006l-2.238 5.018-2.104-2.268a1 1 0 0 0-1.73.859l1.418 6.492a1 1 0 0 0 .982.812h9.16a1 1 0 0 0 .983-.812l1.417-6.492q-.001-.01.004-.02a.99.99 0 0 0-.553-1.076m-1.847 7.38-.003.02h-9.162l-.003-.02L9 12.5l.009.01 2.625 2.828a.5.5 0 0 0 .823-.137L15 9.5l2.543 5.703a.5.5 0 0 0 .824.136l2.625-2.827L21 12.5z"
                      ></path>
                    </svg>
                  </span>
                )
              );
            })()}
            {!isAllCompanies && (
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <button
                      onClick={handleToggleSaveCandidate}
                      disabled={isSaving || isUnsaving}
                      className={`focus:outline-none transition-transform duration-200 ${isSaving || isUnsaving ? "opacity-50 cursor-not-allowed" : ""}`}
                    >
                      <span>
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="24"
                          height="24"
                          fill="none"
                          viewBox="0 0 24 24"
                        >
                          <path
                            fill={isSaved ? "#FF6B00" : "#262626"}
                            d={
                              isSaved
                                ? "M17.25 3H6.75a1.5 1.5 0 0 0-1.5 1.5V21a.75.75 0 0 0 1.148.636L12 18.134l5.603 3.502A.75.75 0 0 0 18.75 21V4.5a1.5 1.5 0 0 0-1.5-1.5z"
                                : "M17.25 3H6.75a1.5 1.5 0 0 0-1.5 1.5V21a.75.75 0 0 0 1.148.636L12 18.134l5.603 3.502A.75.75 0 0 0 18.75 21V4.5a1.5 1.5 0 0 0-1.5-1.5m0 1.5v10.647l-4.853-3.033a.75.75 0 0 0-.795 0L6.75 15.146V4.5zm-4.853 12.114a.75.75 0 0 0-.795 0L6.75 19.647v-2.732l5.25-3.28 5.25 3.28v2.732z"
                            }
                          ></path>
                        </svg>
                      </span>
                    </button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>{isSaved ? "Remove from Saved" : "Save Candidate"}</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            )}
          </div>
        </div>
        <div className="flex gap-4 flex-wrap mt-6">
          <div className="bg-white border border-orange-100 text-orange-100 px-6 py-3 rounded-full">
            {jobType}
          </div>
          <div className="bg-white text-gray-100 border border-gray-100 px-6 py-3 rounded-full">
            {cityName}
          </div>
          <div className="bg-white text-gray-100 border border-gray-100 px-6 py-3 rounded-full">
            {salaryRange}
          </div>
          <div className="ml-auto flex flex-wrap gap-x-3 gap-y-5">
            <button
              onClick={handleCreateConversation}
              disabled={isCreatingConversation}
              className={
                "py-3 px-8 rounded-full inline-flex items-center justify-center space-x-2 bg-orange-100 text-white"
              }
            >
              {isCreatingConversation ? (
                <>
                  <LoadingSpinner size="small" /> Creating chat...
                </>
              ) : (
                <>
                  <span className="text">Message</span>
                </>
              )}

              <ChatDots />
            </button>
            {/* <PrimaryButton text="Message" link="#" icon={<ChatDots />} /> */}
            <PrimaryButton
              props={{ download: true, target: "_blank" }}
              text="Download CV"
              link={downLoadCvUrl}
              icon={<FileArrowDown />}
            />
          </div>
        </div>
      </div>
      <div className="lg:flex mt-8">
        <div className="mr-14 w-[70%]">
          <div>
            <h2 className="text-2xl font-bold text-black-100 mb-3">Bio</h2>
            <p className="text-base font-normal text-gray-100 leading-6">{bioDescription}</p>
          </div>
          <div className="my-6">
            <h2 className="text-2xl font-bold text-black-100 mb-3">Education</h2>
            {academicExperiences.length > 0 ? (
              academicExperiences.map((education) => (
                <div className="mb-3" key={education._id}>
                  <CandidateDetailInfo
                    date={`${new Date(education.startDate).toLocaleDateString("en-US", {
                      month: "long",
                      year: "numeric",
                    })} - ${
                      education.endDate
                        ? new Date(education.endDate).toLocaleDateString("en-US", {
                            month: "long",
                            year: "numeric",
                          })
                        : "Present"
                    }`}
                    description={education.degree}
                    title={education.instituteName}
                    icon={<MortarboardIcon />}
                  />
                </div>
              ))
            ) : (
              <CandidateDetailInfo
                date="Not specified"
                description="No education information available"
                title="No Education Data"
                icon={<MortarboardIcon />}
              />
            )}
          </div>
          <div className="my-6">
            <h2 className="text-2xl font-bold text-black-100 mb-3">Work Experience</h2>
            {workExperiences.length > 0 ? (
              workExperiences.map((experience) => (
                <div className="mb-3" key={experience._id}>
                  <CandidateDetailInfo
                    date={`${new Date(experience.startDate).toLocaleDateString("en-US", {
                      month: "long",
                      year: "numeric",
                    })} - ${
                      experience.endDate
                        ? new Date(experience.endDate).toLocaleDateString("en-US", {
                            month: "long",
                            year: "numeric",
                          })
                        : "Present"
                    }`}
                    description={experience.designation}
                    title={experience.organizationName}
                    icon={<BriefcaseIcon2 />}
                    descriptionList={experience.jobDetails ? [experience.jobDetails] : undefined}
                  />
                </div>
              ))
            ) : (
              <CandidateDetailInfo
                date="Not specified"
                description="No work experience information available"
                title="No Work Experience Data"
                icon={<BriefcaseIcon2 />}
              />
            )}
          </div>
          <div className="my-6">
            <h2 className="text-2xl font-bold text-black-100 mb-3">Certifications & Diploma</h2>
            {certificates.length > 0 ? (
              certificates.map((certificate) => (
                <div className="mb-3" key={certificate._id}>
                  <CandidateDetailInfo
                    date={`${new Date(certificate.startDate).toLocaleDateString("en-US", {
                      month: "long",
                      year: "numeric",
                    })} - ${
                      certificate.endDate
                        ? new Date(certificate.endDate).toLocaleDateString("en-US", {
                            month: "long",
                            year: "numeric",
                          })
                        : "Present"
                    }`}
                    description={certificate.instituteName}
                    title={certificate.certificate}
                    icon={<DiplomaIcon />}
                  />
                </div>
              ))
            ) : (
              <CandidateDetailInfo
                date="Not specified"
                description="No certification information available"
                title="No Certification Data"
                icon={<DiplomaIcon />}
              />
            )}
          </div>
          <div className="my-6">
            <h2 className="text-2xl font-bold text-black-100 mb-3">Achievements</h2>
            {achievements.length > 0 ? (
              achievements.map((achievement) => (
                <div className="mb-3" key={achievement._id}>
                  <CandidateDetailInfo
                    date={
                      achievement.date
                        ? new Date(achievement.date).toLocaleDateString("en-US", {
                            month: "long",
                            year: "numeric",
                          })
                        : "Not specified"
                    }
                    description={achievement.instituteName}
                    title={achievement.title}
                    icon={<Trophy />}
                  />
                </div>
              ))
            ) : (
              <CandidateDetailInfo
                date="Not specified"
                description="No achievement information available"
                title="No Achievement Data"
                icon={<Trophy />}
              />
            )}
          </div>

          <div className="mb-6">
            <h2 className="text-2xl font-bold text-black-100 mb-3">Skills Tags</h2>
            <ul className="flex flex-wrap gap-4">
              {skillsTags.map((item, index) => (
                <li
                  key={index}
                  className="border border-gray-300 inline-block text-gray-100 text-base font-normal px-6 py-3 rounded-full"
                >
                  {item}
                </li>
              ))}
            </ul>
          </div>
        </div>
        <div className="space-y-7 shadow w-[30%] rounded-2xl border h-fit px-6 py-8">
          <div className="flex items-center">
            <div>
              <div className="bg-offWhite-100 w-[60px] grow h-[60px] flex justify-center items-center rounded-full mr-3">
                <MapPin />
              </div>
            </div>
            <div>
              <span className="text-black-100 font-medium block mb-1">Location</span>
              <span className="text-gray-100 font-normal block">{location}</span>
            </div>
          </div>
          <div className="flex items-center">
            <div>
              <div className="bg-offWhite-100 w-[60px] h-[60px] flex justify-center items-center rounded-full mr-3">
                <CurrencyIcon />
              </div>
            </div>
            <div>
              <span className="text-black-100 font-medium block mb-1">Expected Salary</span>
              <span className="text-gray-100 font-normal block">{expectedSalary}</span>
            </div>
          </div>
          <div className="flex items-center">
            <div>
              <div className="bg-offWhite-100 w-[60px] h-[60px] flex justify-center items-center rounded-full mr-3">
                <ExperienceIcon />
              </div>
            </div>
            <div>
              <span className="text-black-100 font-medium block mb-1">Experience</span>
              <span className="text-gray-100 font-normal block">{experience}</span>
            </div>
          </div>
          <div className="flex items-center">
            <div>
              <div className="bg-offWhite-100 w-[60px] h-[60px] flex justify-center items-center rounded-full mr-3">
                <QualificationIcon />
              </div>
            </div>
            <div>
              <span className="text-black-100 font-medium block mb-1">Qualification</span>
              <span className="text-gray-100 font-normal block">{qualification}</span>
            </div>
          </div>
          <div className="flex items-center">
            <div>
              <div className="bg-offWhite-100 w-[60px] h-[60px] flex justify-center items-center rounded-full mr-3">
                <EnvelopeSimpleIcon />
              </div>
            </div>
            <div>
              <span className="text-black-100 font-medium block mb-1">Email</span>
              <span className="text-gray-100 font-normal block break-words whitespace-normal">
                {email}
              </span>
            </div>
          </div>
          {socialNetworks?.length ? (
            <>
              <div className="border-t pt-4 ">
                <h3 className="text-black-100 font-bold mb-4">Social Profiles</h3>
                <div className="flex flex-wrap text-orange-100 gap-x-4"></div>
                {socialNetworks?.map((network) => (
                  <div key={network?.networkName}>
                    <Link
                      href={network?.networkUrl}
                      className="text-orange-100"
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      {network?.networkName}
                    </Link>
                  </div>
                ))}
              </div>
            </>
          ) : (
            <></>
          )}
        </div>
      </div>
    </>
  );
};

export default CandidateDetail;
