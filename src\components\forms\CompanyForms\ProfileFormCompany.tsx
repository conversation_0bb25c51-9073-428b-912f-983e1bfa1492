import { Camera } from "lucide-react";
import React from "react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";

const ProfileFormCompany = () => {
  const inputClasses = "h-12 px-4 rounded-full border border-gray-300 w-full";

  return (
    <>
      <Label
        htmlFor="profile-picture"
        className="inline-flex items-center space-x-4 cursor-pointer"
      >
        <div className="w-24 h-24 bg-gray-200 rounded-full flex items-center justify-center">
          <Camera className="w-8 h-8 text-gray-100" />
        </div>
        <div>
          <div className="text-black-100 text-lg font-normal underline">
            Company Profile Picture
          </div>
          <Input id="profile-picture" type="file" className="mt-1 hidden" />
        </div>
      </Label>
      <div className="lg:w-[80%] mt-8">
        <div className="grid md:grid-cols-2 gap-7 ">
          <div>
            <Label htmlFor="first-name" className="mb-3 block">
              Company Name
            </Label>
            <Input className={inputClasses} id="first-name" />
          </div>
          <div>
            <Label htmlFor="email" className="mb-3 block">
              Company Email
            </Label>
            <Input className={inputClasses} id="email" type="email" />
          </div>

          <div>
            <Label htmlFor="phone" className="mb-3 block">
              Company Phone No
            </Label>
            <Input className={inputClasses} id="phone" type="tel" />
          </div>
          <div>
            <Label htmlFor="dob" className="mb-3 block">
              Founded Date
            </Label>
            <Input className={`${inputClasses} w-full block text-gray-100`} id="dob" type="date" />
          </div>
          <div>
            <Label htmlFor="address" className="mb-3 block">
              Company Size
            </Label>
            <Input className={inputClasses} id="address" />
          </div>
          <div>
            <Label htmlFor="website" className="mb-3 block">
              Website URL
            </Label>
            <Input className={inputClasses} id="website" type="url" />
          </div>
          <div>
            <Label htmlFor="city" className="mb-3 block">
              Friendly Address
            </Label>
            <Input className={inputClasses} id="city" />
          </div>
          <div>
            <Label htmlFor="city" className="mb-3 block">
              City
            </Label>
            <Input className={inputClasses} id="city" />
          </div>
          <div>
            <Label htmlFor="country" className="mb-3 block">
              Country
            </Label>
            <Input className={inputClasses} id="country" />
          </div>
        </div>
      </div>
    </>
  );
};

export default ProfileFormCompany;
