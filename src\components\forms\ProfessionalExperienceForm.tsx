"use client";

import { useEffect } from "react";
import { useForm, type SubmitHandler } from "react-hook-form";
import { Checkbox } from "../ui/checkbox";
import { Input } from "../ui/input";
import { Label } from "../ui/label";
import { Textarea } from "../ui/textarea";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useGetAllEnums } from "@/hooks/useQuery";
import type { IExperience } from "@/types/query.types";

const inputClasses = "h-12 px-4 rounded-full border border-gray-300 w-full";

interface ProfessionalExperienceFormProps {
  goBack: () => void;
  onSuccess: (formData: IExperience[]) => void;
  onError: (error: unknown) => void;
  defaultValues?: IExperience[];
}

const ProfessionalExperienceForm = ({
  goBack,
  onSuccess,
  onError,
  defaultValues = [],
}: ProfessionalExperienceFormProps) => {
  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    reset,
    watch,
  } = useForm<IExperience & { isCurrentlyWorking: boolean }>({
    defaultValues: {
      organizationName: "",
      designation: "",
      startDate: "",
      endDate: "",
      jobType: "",
      jobDetails: "",
      isPresent: false,
      isCurrentlyWorking: false,
    },
  });

  const { data: enumsData, isLoading: isEnumsLoading } = useGetAllEnums();

  // Get job types from enums data
  const jobTypes = enumsData?.data?.JOB_TYPE_ENUM
    ? Object.entries(enumsData.data.JOB_TYPE_ENUM).map(([key, value]) => ({
        label: key.replace(/_/g, " "),
        value,
      }))
    : [];

  // Watch for currently working status
  const isCurrentlyWorking = watch("isCurrentlyWorking");

  // Get current date in YYYY-MM-DD format for validation
  const today = new Date().toISOString().split("T")[0];

  // Format date from ISO string to YYYY-MM-DD for date inputs
  const formatDateForInput = (dateString: string | undefined): string => {
    if (!dateString) return "";
    const date = new Date(dateString);
    if (isNaN(date.getTime())) return "";
    return date.toISOString().split("T")[0] || "";
  };

  // Watch the start date to use in end date validation
  const startDate = watch("startDate");

  // Set form values when editing an existing experience
  useEffect(() => {
    if (defaultValues.length > 0 && defaultValues[0]) {
      const experience = defaultValues[0];
      reset({
        ...experience,
        startDate: formatDateForInput(experience.startDate),
        endDate: experience.isPresent ? "" : formatDateForInput(experience.endDate),
        isCurrentlyWorking: experience.isPresent,
      });

      // Set the job type for the Select component
      if (experience.jobType) {
        setValue("jobType", experience.jobType);
      }
    } else {
      // Clear form when adding a new experience
      reset({
        organizationName: "",
        designation: "",
        startDate: "",
        endDate: "",
        jobType: "",
        jobDetails: "",
        isPresent: false,
        isCurrentlyWorking: false,
      });
    }
  }, [defaultValues, reset, setValue]);

  const onSubmit: SubmitHandler<IExperience & { isCurrentlyWorking: boolean }> = async (data) => {
    try {
      const formattedData = {
        ...data,
        _id: defaultValues[0]?._id || "",
        startDate: new Date(data.startDate).toISOString(),
        isPresent: data.isCurrentlyWorking,
      };

      // Only include endDate if not currently working
      if (!data.isCurrentlyWorking) {
        formattedData.endDate = new Date(data.endDate).toISOString();
      } else {
        // Remove endDate from payload when currently working
        delete formattedData?.endDate;
      }

      onSuccess([formattedData]);
    } catch (error: unknown) {
      onError(error);
    }
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      <div className="mb-6">
        <Label htmlFor="organizationName" className="mb-3 block">
          Organization Name
        </Label>
        <Input
          className={inputClasses}
          id="organizationName"
          {...register("organizationName", {
            required: "Organization name is required",
          })}
        />
        {errors.organizationName && (
          <p className="text-red-500 text-sm">{errors.organizationName.message}</p>
        )}
      </div>

      <div className="grid sm:grid-cols-2 gap-6 mb-6">
        <div>
          <Label htmlFor="designation" className="mb-3 block">
            Designation
          </Label>
          <Input
            className={inputClasses}
            id="designation"
            {...register("designation", {
              required: "Designation is required",
            })}
          />
          {errors.designation && (
            <p className="text-red-500 text-sm">{errors.designation.message}</p>
          )}
        </div>
        <div>
          <Label className="mb-3 block">Job Type</Label>
          {isEnumsLoading ? (
            <div className="h-[46px] w-full bg-gray-100 rounded-full animate-pulse"></div>
          ) : (
            <Select
              onValueChange={(value) => setValue("jobType", value)}
              defaultValue={defaultValues[0]?.jobType || ""}
            >
              <SelectTrigger className="w-full bg-white rounded-full h-[46px] px-6 text-gray-100 text-base">
                <SelectValue placeholder="Job Type" />
              </SelectTrigger>
              <SelectContent>
                <SelectGroup>
                  {jobTypes.map(({ label, value }) => (
                    <SelectItem key={value} value={value}>
                      {label}
                    </SelectItem>
                  ))}
                </SelectGroup>
              </SelectContent>
            </Select>
          )}
          {errors.jobType && <p className="text-red-500 text-sm">{errors.jobType.message}</p>}
        </div>
      </div>

      <div className="grid sm:grid-cols-2 gap-6 mb-6">
        <div>
          <Label htmlFor="startDate" className="mb-3 block">
            Select Duration Starts
          </Label>
          <Input
            className={`${inputClasses} w-full block text-gray-100`}
            type="date"
            id="startDate"
            max={today}
            {...register("startDate", {
              required: "Start date is required",
              validate: (value) => {
                if (!value) return true;
                const selectedDate = new Date(value);
                const currentDate = new Date();
                currentDate.setHours(0, 0, 0, 0);

                if (selectedDate > currentDate) {
                  return "Date cannot be in the future";
                }

                return true;
              },
            })}
          />
          {errors.startDate && <p className="text-red-500 text-sm">{errors.startDate.message}</p>}
        </div>
        <div>
          <Label htmlFor="endDate" className="mb-3 block">
            Select Duration Ends
          </Label>
          <div className="space-y-2">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="isCurrentlyWorking"
                checked={isCurrentlyWorking}
                onCheckedChange={(checked) => {
                  setValue("isCurrentlyWorking", checked as boolean);
                  if (checked) {
                    setValue("endDate", "");
                  }
                }}
              />
              <label
                htmlFor="isCurrentlyWorking"
                className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
              >
                I currently work here
              </label>
            </div>
            {!isCurrentlyWorking && (
              <Input
                className={`${inputClasses} w-full block text-gray-100`}
                type="date"
                id="endDate"
                max={today}
                disabled={isCurrentlyWorking}
                {...register("endDate", {
                  required: !isCurrentlyWorking ? "End date is required" : false,
                  validate: (value) => {
                    if (!value || isCurrentlyWorking) return true;

                    // Check if end date is in the future
                    const selectedDate = new Date(value);
                    const currentDate = new Date();
                    currentDate.setHours(0, 0, 0, 0);

                    if (selectedDate > currentDate) {
                      return "Date cannot be in the future";
                    }

                    // Check if end date is after start date
                    if (startDate && new Date(value) <= new Date(startDate)) {
                      return "End date must be after start date";
                    }

                    return true;
                  },
                })}
              />
            )}
          </div>
          {errors.endDate && <p className="text-red-500 text-sm">{errors.endDate.message}</p>}
        </div>
      </div>

      <div className="mb-6">
        <Label htmlFor="jobDetails" className="mb-3 block">
          Add your job role detail <span className="text-[#8B8B8D] text-sm">(optional)</span>
        </Label>
        <Textarea
          id="jobDetails"
          className="min-h-[195px] px-4 py-2 rounded-3xl border border-gray-300"
          placeholder="Add description..."
          {...register("jobDetails")}
        />
      </div>

      <div className="flex flex-wrap gap-y-3 gap-x-5 mt-10">
        <button
          onClick={(e) => {
            e.preventDefault();
            goBack();
          }}
          type="button"
          className="font-bold py-4 px-10 font-base rounded-full inline-flex items-center justify-center space-x-2 bg-gray-300 text-gray-100"
        >
          Go Back
        </button>
        <button
          type="submit"
          className="font-bold py-4 px-10 font-base rounded-full inline-flex items-center justify-center space-x-2 bg-orange-100 text-white"
        >
          Save Details
        </button>
      </div>
    </form>
  );
};

export default ProfessionalExperienceForm;
