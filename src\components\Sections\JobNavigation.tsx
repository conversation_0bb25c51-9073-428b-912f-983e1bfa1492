"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";
import { cn } from "@/lib/utils";

interface InavItems {
  href: string;
  label: string;
}

export default function JobNavigation({ navItems }: { navItems: InavItems[] }) {
  const pathname = usePathname();

  return (
    <div className="md:bg-gray-400 md:inline-flex rounded-full">
      {navItems.map((item) => (
        <Link
          key={item.href}
          href={item.href}
          className={cn(
            "block md:inline xl:px-10 px-5 py-4 text-center md:text-left mb-2 md:mb-0 font-medium text-lg rounded-full transition-colors",
            pathname === item.href
              ? "bg-orange-100 text-white"
              : "bg-gray-400 md:bg-transparent text-gray-100 hover:bg-gray-300"
          )}
        >
          {item.label}
        </Link>
      ))}
    </div>
  );
}
