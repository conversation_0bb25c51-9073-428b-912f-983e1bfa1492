import { create } from "zustand";
import { persist } from "zustand/middleware";
import { INotification } from "@/types/query.types";

/**
 * Notification store interface
 * Manages the state of notifications functionality
 */
interface NotificationStore {
  // State
  notifications: INotification[];
  unreadCount: number;
  hasHydrated: boolean;

  // Actions
  setNotifications: (notifications: INotification[]) => void;
  addNotification: (notification: INotification) => void;
  markAsRead: (notificationId: string) => void;
  markAllAsRead: () => void;
  setUnreadCount: (count: number) => void;
  updateNotification: (notificationId: string, updates: Partial<INotification>) => void;
  removeNotification: (notificationId: string) => void;
  clearNotifications: () => void;
  setHydrated: () => void;
}

/**
 * Notification store implementation using Zustand
 * Uses persist middleware to save state to localStorage
 */
export const useNotificationStore = create<NotificationStore>()(
  persist(
    (set, get) => ({
      // Initial state
      notifications: [],
      unreadCount: 0,
      hasHydrated: false,

      // Actions
      setNotifications: (notifications) => {
        set({ notifications });
      },

      addNotification: (notification) => {
        const { notifications } = get();
        const exists = notifications.find((n) => n._id === notification._id);

        if (!exists) {
          set({
            notifications: [notification, ...notifications],
            unreadCount: notification.isRead ? get().unreadCount : get().unreadCount + 1,
          });
        }
      },

      markAsRead: (notificationId) => {
        const { notifications, unreadCount } = get();
        const updatedNotifications = notifications.map((notification) => {
          if (notification._id === notificationId && !notification.isRead) {
            return { ...notification, isRead: true };
          }
          return notification;
        });

        const wasUnread = notifications.find((n) => n._id === notificationId && !n.isRead);

        set({
          notifications: updatedNotifications,
          unreadCount: wasUnread ? Math.max(0, unreadCount - 1) : unreadCount,
        });
      },

      markAllAsRead: () => {
        const { notifications } = get();
        const updatedNotifications = notifications.map((notification) => ({
          ...notification,
          isRead: true,
        }));

        set({
          notifications: updatedNotifications,
          unreadCount: 0,
        });
      },

      setUnreadCount: (count) => {
        set({ unreadCount: count });
      },

      updateNotification: (notificationId, updates) => {
        const { notifications } = get();
        const updatedNotifications = notifications.map((notification) =>
          notification._id === notificationId ? { ...notification, ...updates } : notification
        );

        set({ notifications: updatedNotifications });
      },

      removeNotification: (notificationId) => {
        const { notifications, unreadCount } = get();
        const notificationToRemove = notifications.find((n) => n._id === notificationId);
        const updatedNotifications = notifications.filter((n) => n._id !== notificationId);

        set({
          notifications: updatedNotifications,
          unreadCount:
            notificationToRemove && !notificationToRemove.isRead
              ? Math.max(0, unreadCount - 1)
              : unreadCount,
        });
      },

      clearNotifications: () => {
        set({
          notifications: [],
          unreadCount: 0,
        });
      },

      setHydrated: () => {
        set({ hasHydrated: true });
      },
    }),
    {
      name: "notification-store",
      partialize: (state) => ({
        notifications: state.notifications,
        unreadCount: state.unreadCount,
      }),
      onRehydrateStorage: () => (state) => {
        if (state) {
          state.setHydrated();
        }
      },
    }
  )
);
