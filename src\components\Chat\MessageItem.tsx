import { format } from "date-fns";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, Check<PERSON>he<PERSON>, FileText } from "lucide-react";
import Image from "next/image";
import { memo } from "react";
import MessageActions from "./MessageActions";
import { Toolt<PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { cn } from "@/lib/utils";
import type { IMessage } from "@/types/query.types";

interface MessageItemProps {
  message: IMessage;
  isCurrentUser: boolean;
  currentUserId?: string;
}

const MessageItem = memo(function MessageItem({
  message,
  isCurrentUser,
  currentUserId,
}: MessageItemProps) {
  // Check if message is hidden for current user (either deleted for everyone or deleted just for this user)
  const isHiddenForUser =
    !message.deletedForEveryone && message.deletedBy?.includes(currentUserId || "");

  // If message should be hidden for this user, don't render anything
  if (isHiddenForUser) {
    return null;
  }

  const isDeleted = message.deletedForE<PERSON><PERSON><PERSON>;

  return (
    <div className={cn("flex group", isCurrentUser ? "justify-end" : "justify-start")}>
      <div
        className={cn(
          "max-w-[70%] rounded-lg py-3 px-4 relative",
          isCurrentUser
            ? "bg-orange-100 text-white rounded-br-none"
            : "bg-offWhite-100 text-gray-800 rounded-bl-none",
          isDeleted && "opacity-70"
        )}
      >
        {/* Message actions (only for current user's messages) */}
        {!isDeleted && (
          <div className="absolute top-1 right-1">
            <MessageActions
              messageId={message._id}
              conversationId={message.conversationId}
              isCurrentUser={isCurrentUser}
            />
          </div>
        )}

        {/* Message content */}
        {isDeleted ? (
          <p className="italic text-gray-500">This message was deleted</p>
        ) : (
          <>
            {message.content && <p className="mb-1">{message.content}</p>}

            {message.mediaFiles && message.mediaFiles.length > 0 && (
              <div className="space-y-2 mt-2">
                {message.mediaFiles.map((file, index) => {
                  const fileType = file.fileName.split(".").pop()?.toLowerCase() || "";
                  const isImage = ["jpg", "jpeg", "png", "gif", "webp"].includes(fileType);
                  const isVideo = ["mp4", "webm", "ogg"].includes(fileType);

                  if (isImage) {
                    return (
                      <a href={file.url} target="_blank" rel="noopener noreferrer" key={index}>
                        <Image
                          src={file.url || "/placeholder.svg"}
                          alt={file.fileName}
                          width={300}
                          height={200}
                          className="max-w-full rounded-md max-h-60 object-contain"
                          unoptimized // Use unoptimized for external URLs
                        />
                      </a>
                    );
                  } else if (isVideo) {
                    return (
                      <video key={index} controls className="max-w-full rounded-md max-h-60">
                        <source src={file.url} type={`video/${fileType}`} />
                        Your browser does not support the video tag.
                      </video>
                    );
                  } else {
                    return (
                      <a
                        href={file.url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="flex items-center p-2 bg-gray-100 rounded-md hover:bg-gray-200"
                        key={index}
                      >
                        <FileText className="h-5 w-5 mr-2" />
                        <span className="text-sm truncate">{file.fileName}</span>
                      </a>
                    );
                  }
                })}
              </div>
            )}
          </>
        )}

        {/* Message timestamp and read status */}
        <div className="flex items-center text-xs mt-1 gap-1">
          <span>{format(new Date(message.createdAt), "h:mm a")}</span>
          {isCurrentUser && (
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <span className="flex items-center gap-1 ml-1">
                    {message.status === "error" ? (
                      <span className="flex items-center text-red-500">
                        <AlertCircle size={14} className="inline" />
                      </span>
                    ) : message.read ? (
                      <span className="flex items-center text-white">
                        <CheckCheck size={14} className="inline" />
                      </span>
                    ) : (
                      <span className="flex items-center text-white">
                        <Check size={14} className="inline" />
                      </span>
                    )}
                  </span>
                </TooltipTrigger>
                <TooltipContent side="top">
                  {message.status === "error" ? "Failed to send" : message.read ? "Read" : "Sent"}
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          )}
        </div>
      </div>
    </div>
  );
});

export default MessageItem;
