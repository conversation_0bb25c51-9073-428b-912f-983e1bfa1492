"use client";
import React, { useState } from "react";
import CompanySettingsNav from "@/components/Sections/CompanySettingsNav";

const CompanySettingsLayout = () => {
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);

  const toggleSidebar = () => {
    setIsSidebarOpen(!isSidebarOpen);
  };
  return (
    <>
      <button
        onClick={toggleSidebar}
        className="lg:hidden  bg-orange-100 text-white px-4 py-3 inline-block mb-4 rounded-full"
      >
        Profile Menu
      </button>
      <div
        className={`fixed inset-0  bg-black bg-opacity-50 transition-opacity ${
          isSidebarOpen ? "opacity-100 z-40" : "opacity-0 pointer-events-none"
        } lg:hidden`}
        onClick={toggleSidebar}
      ></div>
      <div
        className={`fixed inset-y-0 left-0  w-[350px] bg-white shadow-lg transform transition-transform ${
          isSidebarOpen ? "translate-x-0 overflow-y-auto z-50" : "-translate-x-full"
        } lg:relative lg:translate-x-0 lg:w-[350px] pt-10 lg:px-10 px-5 pb-14`}
      >
        <div className="lg:hidden flex items-center justify-between pb-3 mb-3 border-b">
          <button onClick={toggleSidebar} className="text-gray-800 focus:outline-none">
            <svg
              className="w-6 h-6"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M6 18L18 6M6 6l12 12"
              ></path>
            </svg>
          </button>
        </div>
        <h2 className="text-3xl font-bold text-black-100 mb-10">Settings</h2>
        <CompanySettingsNav />
      </div>
    </>
  );
};

export default CompanySettingsLayout;
