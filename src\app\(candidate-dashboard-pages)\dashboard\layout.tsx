import type { <PERSON>ada<PERSON> } from "next";
import type React from "react";
import SidebarLayout from "./SidebarLayout";
import Header from "@/components/layout/Header";

export const metadata: Metadata = {
  title: "Authentication",
  description: "Login and register pages",
};

export default function CandidateDashboardInnerLayout({ children }: { children: React.ReactNode }) {
  return (
    <>
      <Header dashboardPages={true} />
      <SidebarLayout>{children}</SidebarLayout>
    </>
  );
}
