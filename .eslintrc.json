{"extends": ["eslint:recommended", "plugin:@typescript-eslint/recommended", "prettier"], "parser": "@typescript-eslint/parser", "plugins": ["@typescript-eslint", "import"], "rules": {"@typescript-eslint/no-explicit-any": "error", "@typescript-eslint/ban-ts-comment": "error", "@typescript-eslint/no-unused-vars": "error", "@typescript-eslint/no-unsafe-assignment": "error", "@typescript-eslint/no-unsafe-call": "error", "@typescript-eslint/no-unsafe-member-access": "error", "@typescript-eslint/no-unsafe-return": "error", "@typescript-eslint/no-var-requires": "error", "@typescript-eslint/prefer-readonly": "error", "@typescript-eslint/prefer-readonly-parameter-types": "error", "@typescript-eslint/prefer-readonly-property": "error", "@typescript-eslint/explicit-module-boundary-types": "error", "no-console": "warn", "import/order": ["error", {"alphabetize": {"order": "asc"}}]}}