"use client";

import { Camera } from "lucide-react";
import { useForm, type SubmitHandler } from "react-hook-form";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../ui/select";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { useUserContext } from "@/context/userStore";
import { useProfileCompletion } from "@/hooks/api";
import { toast } from "@/hooks/use-toast";
import { ApiError } from "@/types/common.types";

type ProfileInputs = {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  dob: string;
  address: string;
  city: string;
  country: string;
  website?: string; // Optional
  portfolio?: string; // Optional
  bio?: string; // Optional
  profilePicture?: FileList;
};

const inputClasses = "h-12 px-4 rounded-full border border-gray-300 w-full";

const ProfileForm = () => {
  const { user } = useUserContext();
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<ProfileInputs>({
    defaultValues: {
      firstName: user?.firstName || "",
      lastName: user?.lastName || "",
      email: user?.email || "",
    },
  });
  const { mutateAsync: completeProfile, isPending } = useProfileCompletion();

  const onSubmit: SubmitHandler<ProfileInputs> = async (data) => {
    const formData = new FormData();
    formData.append("firstName", data.firstName);
    formData.append("lastName", data.lastName);
    formData.append("email", user?.email || ""); // Use user email
    formData.append("phone", data.phone);
    formData.append("dob", data.dob);
    formData.append("address", data.address);
    formData.append("city", data.city);
    formData.append("country", data.country);
    if (data.website) formData.append("website", data.website);
    if (data.portfolio) formData.append("portfolio", data.portfolio);
    if (data.bio) formData.append("bio", data.bio);
    if (data.profilePicture?.[0]) {
      formData.append("profilePicture", data.profilePicture[0]);
    }

    try {
      await completeProfile(formData);
      toast({
        variant: "default",
        title: "Profile updated successfully",
      });
    } catch (error) {
      toast({
        variant: "destructive",
        title: (error as ApiError).response?.data?.message || "Failed to update profile",
      });
    }
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
      <Label
        htmlFor="profile-picture"
        className="inline-flex items-center space-x-4 cursor-pointer"
      >
        <div className="w-24 h-24 bg-gray-200 rounded-full flex items-center justify-center">
          <Camera className="w-8 h-8 text-gray-100" />
        </div>
        <div>
          <div className="text-black-100 text-lg font-normal underline">Profile Picture</div>
          <Input
            id="profile-picture"
            type="file"
            className="mt-1 hidden"
            {...register("profilePicture")}
          />
        </div>
      </Label>
      <div className="xl:w-[80%]">
        <div className="grid lg:grid-cols-2 gap-7">
          <div>
            <Label htmlFor="first-name" className="mb-3 block">
              First Name
            </Label>
            <Input
              className={inputClasses}
              id="first-name"
              {...register("firstName", { required: "First name is required" })}
            />
            {errors.firstName && <p className="text-red-500 text-sm">{errors.firstName.message}</p>}
          </div>
          <div>
            <Label htmlFor="last-name" className="mb-3 block">
              Last Name
            </Label>
            <Input
              className={inputClasses}
              id="last-name"
              {...register("lastName", { required: "Last name is required" })}
            />
            {errors.lastName && <p className="text-red-500 text-sm">{errors.lastName.message}</p>}
          </div>
          <div>
            <Label htmlFor="email" className="mb-3 block">
              Email
            </Label>
            <Input
              className={`${inputClasses} bg-gray-100 cursor-not-allowed`}
              id="email"
              type="email"
              readOnly
              {...register("email")}
            />
            {errors.email && <p className="text-red-500 text-sm">{errors.email.message}</p>}
          </div>
          <div>
            <Label htmlFor="phone" className="mb-3 block">
              Phone No
            </Label>
            <Input
              className={inputClasses}
              id="phone"
              type="tel"
              {...register("phone", { required: "Phone number is required" })}
            />
            {errors.phone && <p className="text-red-500 text-sm">{errors.phone.message}</p>}
          </div>
          <div>
            <Label htmlFor="dob" className="mb-3 block">
              Date of Birth
            </Label>
            <Input
              className={`${inputClasses} w-full block text-gray-100`}
              id="dob"
              type="date"
              {...register("dob", { required: "Date of birth is required" })}
            />
            {errors.dob && <p className="text-red-500 text-sm">{errors.dob.message}</p>}
          </div>
          <div>
            <Label htmlFor="address" className="mb-3 block">
              Friendly Address
            </Label>
            <Input
              className={inputClasses}
              id="address"
              {...register("address", { required: "Address is required" })}
            />
            {errors.address && <p className="text-red-500 text-sm">{errors.address.message}</p>}
          </div>
          <div>
            <Label className="mb-3 block">City</Label>
            <Select>
              <SelectTrigger className="w-full bg-white rounded-full h-[46px] px-6 text-gray-100 text-base">
                <SelectValue placeholder="City" />
              </SelectTrigger>
              <SelectContent>
                <SelectGroup>
                  <SelectItem value="City1">City1</SelectItem>
                  <SelectItem value="City2">City2</SelectItem>
                  <SelectItem value="City3">City3</SelectItem>
                  <SelectItem value="City4">City4</SelectItem>
                </SelectGroup>
              </SelectContent>
            </Select>
          </div>
          <div>
            <Label className="mb-3 block">Country</Label>
            <Select>
              <SelectTrigger className="w-full bg-white rounded-full h-[46px] px-6 text-gray-100 text-base">
                <SelectValue placeholder="Country" />
              </SelectTrigger>
              <SelectContent>
                <SelectGroup>
                  <SelectItem value="Country1">Country1</SelectItem>
                  <SelectItem value="Country2">Country2</SelectItem>
                  <SelectItem value="Country3">Country3</SelectItem>
                  <SelectItem value="Country4">Country4</SelectItem>
                </SelectGroup>
              </SelectContent>
            </Select>
          </div>
          <div>
            <Label htmlFor="website" className="mb-3 block">
              Website URL (optional)
            </Label>
            <Input className={inputClasses} id="website" type="url" {...register("website")} />
          </div>
          <div>
            <Label htmlFor="portfolio" className="mb-3 block">
              Portfolio URL (optional)
            </Label>
            <Input className={inputClasses} id="portfolio" type="url" {...register("portfolio")} />
          </div>
        </div>
        <div className="mt-7">
          <Label htmlFor="bio" className="mb-3 block">
            Short Bio (optional)
          </Label>
          <Textarea
            className="min-h-[100px] px-4 py-2 rounded-3xl border border-gray-300"
            id="bio"
            {...register("bio")}
          />
        </div>

        <div className={`flex flex-wrap gap-y-3 gap-x-5 mt-10`}>
          <button
            disabled={isPending}
            type="submit"
            className="font-bold py-4 px-10 font-base rounded-full inline-flex items-center justify-center space-x-2 bg-orange-100 text-white"
          >
            {isPending ? "Loading..." : "Next Step"}
          </button>
        </div>
      </div>
    </form>
  );
};

export default ProfileForm;
