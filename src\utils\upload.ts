import axiosInstance from "@/lib/axios";

export const uploadFile = async (
  file: File,
  endpoint: string,
  keyName: string,
  onProgress?: (progress: number) => void
) => {
  const formData = new FormData();
  formData.append(keyName, file);

  try {
    const response = await axiosInstance.post(`${endpoint}`, formData, {
      headers: {
        "Content-Type": "multipart/form-data",
      },
      onUploadProgress: (progressEvent) => {
        if (onProgress && progressEvent.total) {
          const progress = (progressEvent.loaded / progressEvent.total) * 100;
          onProgress(progress);
        }
      },
    });

    return response.data;
  } catch (error) {
    console.error("Upload error:", error);
    throw error;
  }
};

export const uploadMultipleFiles = async (
  files: File[],
  endpoint: string,
  keyName: string,
  onProgress?: (progress: number) => void
) => {
  const formData = new FormData();
  files.forEach((file) => {
    formData.append(keyName, file);
  });

  try {
    const response = await axiosInstance.post(`${endpoint}`, formData, {
      headers: {
        "Content-Type": "multipart/form-data",
      },
      onUploadProgress: (progressEvent) => {
        if (onProgress && progressEvent.total) {
          const progress = (progressEvent.loaded / progressEvent.total) * 100;
          onProgress(progress);
        }
      },
    });

    return response.data;
  } catch (error) {
    console.error("Upload error:", error);
    throw error;
  }
};
