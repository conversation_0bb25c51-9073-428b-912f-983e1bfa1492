import Image from "next/image";
import React from "react";
import PrimaryButton from "../Buttons/PrimaryButton";
import CountingCard from "../Cards/CountingCard";
import CustomTag from "../CustomTag";
import { ArrowIcon } from "../Icons";

const AboutUsHome = () => {
  return (
    <section className="bg-offWhite-100 lg:py-24 py-14">
      <div className="container mx-auto">
        <div className="lg:flex justify-between">
          <div className="lg:w-[550px] lg:text-left mb-3 lg:mb-0 text-center">
            <CustomTag>About Us</CustomTag>
          </div>
          <div>
            <div className="grid lg:grid-cols-4 md:grid-cols-2 grid-cols-1 gap-6">
              <div>
                <CountingCard count="420K" text="Job List" />
              </div>
              <div>
                <CountingCard count="700K" text="Successful Hires" />
              </div>
              <div>
                <CountingCard count="18K" text="Trusted Companies" />
              </div>
              <div>
                <CountingCard count="4.8/5" text="Rating" />
              </div>
            </div>
            <p className="text-black-100 text-lg my-14">
              YesJobs started with a simple idea: to make finding a job and hiring talent as easy as
              possible. Since our inception, we’ve grown into a trusted platform used by thousands
              of candidates and companies across industries. Whether you&apos;re looking for your
              next opportunity or the perfect hire, YesJobs is here to help.
            </p>
            <div className="flex flex-wrap gap-6 justify-between items-center">
              <div>
                <PrimaryButton link="/about-us" text="Learn More" icon={<ArrowIcon />} />
              </div>
              <div>
                <Image src={"/images/EdgeKart.png"} alt="EdgeKart Logo" width={136} height={37} />
              </div>
              <div>
                <Image src={"/images/gold-fish.png"} alt="gold-fish Logo" width={115} height={40} />
              </div>
              <div>
                <Image
                  src={"/images/pacific-trim.png"}
                  alt="pacific-trim Logo"
                  width={163}
                  height={48}
                />
              </div>
              <div>
                <Image src={"/images/wheelapp.png"} alt="wheelapp Logo" width={192} height={47} />
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default AboutUsHome;
