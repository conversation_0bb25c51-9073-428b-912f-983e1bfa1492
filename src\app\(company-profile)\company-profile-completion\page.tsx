import { Suspense } from "react";
import MultiStepper from "@/components/MultiStepper";
import Sidebar from "@/components/Sections/Sidebar";
import AboutCompanyForm from "@/components/forms/CompanyForms/AboutCompanyForm";
import AchivementsCompanyForm from "@/components/forms/CompanyForms/AchivementsCompanyForm";
import CompanyPhotosUnified from "@/components/forms/CompanyForms/CompanyPhotosUnified";
import PerkBenefitsUnified from "@/components/forms/CompanyForms/PerkBenefitsUnified";
import TellUsAboutCompany from "@/components/forms/CompanyForms/TellUsAboutCompany";
import SocialNetworkForm from "@/components/forms/SocialNetworkForm";

const steps = [
  {
    id: "about-company",
    title: "Tell Us About Company",
    component: TellUsAboutCompany,
  },
  {
    id: "company-details",
    title: "About Company",
    component: AboutCompanyForm,
  },
  {
    id: "social-networks",
    title: "Social Networks",
    component: SocialNetworkForm,
  },
  {
    id: "company-photos",
    title: "Company Photos",
    component: CompanyPhotosUnified,
  },
  {
    id: "perks-benefits",
    title: "Perks & Benefits",
    component: PerkBenefitsUnified,
  },
  {
    id: "achievements",
    title: "Achievements",
    component: AchivementsCompanyForm,
  },
];

export default function CompanyProfileCompletionPage() {
  // const router = useRouter();
  // const { currentUser } = useUserStore();
  // const { data: userData } = useGetCurrentUser({
  //   enabled: !!currentUser,
  // });

  return (
    <Suspense fallback={<div>Loading...</div>}>
      <div className="xl:p-6">
        <div className="lg:flex ">
          {/* Step Navigation */}
          <div className="xl:w-1/4 lg:w-[30%] mb-6 lg:mb-0">
            <Sidebar steps={steps} />
          </div>

          {/* Form Content */}
          <div className="xl:w-3/4 lg:w-[70%]">
            <div className="bg-white  rounded-lg ">
              <MultiStepper steps={steps} />
            </div>
          </div>
        </div>
      </div>
    </Suspense>
  );
}
