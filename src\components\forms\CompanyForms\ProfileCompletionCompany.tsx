// "use client";

// import { useState } from "react";

// import Sidebar from "../../Sections/Sidebar";
// import { useRouter } from "next/navigation";
// import TellUsAboutCompany from "./TellUsAboutCompany";

// import SocialNetwork from "./SocialNetworkCompany";
// import CompanyPhotos from "@/components/forms/CompanyForms/CompanyPhotos";
// import PerkBenefits from "./PerkBenefits";
// import AchivementsCompanyForm from "./AchivementsCompanyForm";
// import AboutCompanyForm from "./AboutCompanyForm";

// const steps = [
//   "Tell us about Company",
//   "About Company",
//   "Social Network",
//   "Company Photos",
//   "Perk & Benefits",
//   "Achievements",
// ];

// export default function ProfileCompletionCompany() {
//   const [currentStep, setCurrentStep] = useState(0);
//   const router = useRouter();

//   const handleNextClick = () => {
//     if (currentStep === steps.length - 1) {
//       router.push("/profile-completed");
//     } else {
//       setCurrentStep(Math.min(steps.length - 1, currentStep + 1));
//     }
//   };

//   return (
//     <div className="xl:p-6">
//       <div className="lg:flex gap-6">
//         {/* Step Navigation */}
//         <div className="xl:w-1/4 lg:w-[30%] mb-6 lg:mb-0">
//           <Sidebar steps={steps} currentStep={currentStep} />
//         </div>

//         {/* Form Content */}
//         <div className="xl:w-3/4 lg:w-[70%]">
//           <div className="bg-white p-6 rounded-lg ">
//             {currentStep === 0 && <TellUsAboutCompany />}
//             {currentStep === 1 && <AboutCompanyForm />}
//             {currentStep === 2 && <SocialNetwork />}
//             {currentStep === 3 && <CompanyPhotos />}
//             {currentStep === 4 && <PerkBenefits />}
//             {currentStep === 5 && <AchivementsCompanyForm />}

//             <div className={`flex flex-wrap gap-y-3 gap-x-5 mt-10`}>
//               <button
//                 onClick={() => setCurrentStep(Math.max(0, currentStep - 1))}
//                 disabled={currentStep === 0}
//                 className="font-bold py-4 px-10 font-base rounded-full inline-flex items-center justify-center space-x-2 bg-gray-300 text-gray-100"
//               >
//                 Go Back
//               </button>
//               <button
//                 onClick={handleNextClick}
//                 // disabled={currentStep === steps.length - 1}
//                 className="font-bold py-4 px-10 font-base rounded-full inline-flex items-center justify-center space-x-2 bg-orange-100 text-white"
//               >
//                 {currentStep === steps.length - 1
//                   ? "Complete Profile"
//                   : "Next Step"}
//               </button>
//             </div>
//           </div>
//         </div>
//       </div>
//     </div>
//   );
// }
