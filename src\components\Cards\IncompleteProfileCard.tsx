import React from "react";

const IncompleteProfileCard = () => {
  return (
    <div className="shadow-2xl rounded-xl p-6">
      <h2 className="text-base font-medium mb-2">Complete your profile</h2>
      <div className="w-full bg-gray-200 rounded-full h-2.5 mb-4">
        <div className="bg-orange-100 h-2.5 rounded-full" style={{ width: "16.67%" }}></div>
      </div>
      <div className="flex justify-between items-center mb-4">
        <span className="font-medium text-sm text-orange-100">2 of 12 Data</span>
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="25"
          height="25"
          fill="none"
          viewBox="0 0 25 25"
        >
          <path
            stroke="#EC761E"
            d="M12.5 22.5c5.523 0 10-4.477 10-10s-4.477-10-10-10-10 4.477-10 10 4.477 10 10 10Z"
          ></path>
          <path
            stroke="#EC761E"
            strokeLinecap="round"
            strokeLinejoin="round"
            d="m9 13 2 2 5-5"
          ></path>
        </svg>
      </div>
      <p className="font-normal text-sm leading-6 text-gray-100">
        Complete your profile now and let us help you
      </p>
    </div>
  );
};

export default IncompleteProfileCard;
