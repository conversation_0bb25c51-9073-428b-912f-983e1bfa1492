/conversations/ POST
Request body
{
  "type": "application",
  "jobApplicationId": "6807a1747dac5c8168f838f4"
}
OR 
{
  "type": "direct",
  "jobSeekerProfileId": "67f7da0979ceba70f2af69db"
}
	
Response body
{
  "success": true,
  "message": "Conversation created successfully",
  "data": {
    "jobApplication": "6807a1747dac5c8168f838f4",
    "recruiterProfileId": "67f7da0d79ceba70f2af6a07",
    "jobSeekerProfileId": "67f7da0979ceba70f2af69db",
    "isDirectMessage": false,
    "deletedBy": [],
    "_id": "6808e6503dcfd182f9e22e06",
    "createdAt": "2025-04-23T13:08:32.448Z",
    "updatedAt": "2025-04-23T13:08:32.448Z",
    "__v": 0
  }
}


/conversations/ GET
Response body
{
    "success": true,
    "message": "Conversations retrieved successfully",
    "data": {
      "conversations": [
        {
          "_id": "6808e6503dcfd182f9e22e06",
          "jobApplication": {
            "_id": "6807a1747dac5c8168f838f4",
            "job": {
              "_id": "6807a1657dac5c8168f838d4",
              "jobTitle": "backend-developer",
              "jobDescription": "Quia et quia tempor ",
              "applicationDeadline": "2025-12-31T00:00:00.000Z"
            },
            "status": "SHORTLISTED",
            "createdAt": "2025-04-22T14:02:28.622Z"
          },
          "recruiterProfileId": {
            "companyProfile": {
              "companyName": "Freeman and Mcconnell Co",
              "profilePicture": "https://yes-jobs-dev.s3.ap-southeast-2.amazonaws.com/profiles/user-67f7da0d79ceba70f2af6a05/1745313742147-company-3.png"
            },
            "_id": "67f7da0d79ceba70f2af6a07"
          },
          "jobSeekerProfileId": {
            "userProfile": {
              "firstName": "John",
              "lastName": "Doe",
              "profilePicture": "https://images.pexels.com/photos/19305961/pexels-photo-19305961/free-photo-of-a-woman-in-a-leather-skirt-leaning-against-a-wall.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1"
            },
            "_id": "67f7da0979ceba70f2af69db"
          },
          "isDirectMessage": false,
          "deletedBy": [],
          "createdAt": "2025-04-23T13:08:32.448Z",
          "updatedAt": "2025-04-23T13:08:32.448Z",
          "__v": 0
        },
        {
          "_id": "6808c74f3dcfd182f9e2275b",
          "jobApplication": {
            "_id": "6807b618c8b33e80ff3fb01e",
            "job": {
              "_id": "6807b464c8b33e80ff3fad16",
              "jobTitle": "Minus eligendi anim ",
              "jobDescription": "Odio sequi molestiae",
              "applicationDeadline": "2025-04-25T00:00:00.000Z"
            },
            "status": "PENDING",
            "createdAt": "2025-04-22T15:30:32.842Z"
          },
          "recruiterProfileId": {
            "companyProfile": {
              "companyName": "Freeman and Mcconnell Co",
              "profilePicture": "https://yes-jobs-dev.s3.ap-southeast-2.amazonaws.com/profiles/user-67f7da0d79ceba70f2af6a05/1745313742147-company-3.png"
            },
            "_id": "67f7da0d79ceba70f2af6a07"
          },
          "jobSeekerProfileId": {
            "userProfile": {
              "firstName": "John",
              "lastName": "Doe",
              "profilePicture": "https://images.pexels.com/photos/19305961/pexels-photo-19305961/free-photo-of-a-woman-in-a-leather-skirt-leaning-against-a-wall.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1"
            },
            "_id": "67f7da0979ceba70f2af69db"
          },
          "isDirectMessage": false,
          "deletedBy": [],
          "createdAt": "2025-04-23T10:56:15.196Z",
          "updatedAt": "2025-04-23T10:56:15.196Z",
          "__v": 0
        },
        {
          "_id": "6807efee4398104d67b9dd9b",
          "jobApplication": {
            "_id": "6807e8acb4e3dae37322df55",
            "job": {
              "_id": "6807e87fb4e3dae37322df4f",
              "jobTitle": "Junior Frontend Developer",
              "jobDescription": "We are looking for a passionate Junior Frontend Developer to join our remote team and help build beautiful web applications.",
              "applicationDeadline": "2025-05-10T23:59:59.000Z"
            },
            "status": "PENDING",
            "createdAt": "2025-04-22T19:06:20.416Z"
          },
          "recruiterProfileId": {
            "companyProfile": {
              "companyName": "Freeman and Mcconnell Co",
              "profilePicture": "https://yes-jobs-dev.s3.ap-southeast-2.amazonaws.com/profiles/user-67f7da0d79ceba70f2af6a05/1745313742147-company-3.png"
            },
            "_id": "67f7da0d79ceba70f2af6a07"
          },
          "jobSeekerProfileId": {
            "userProfile": {
              "firstName": "John",
              "lastName": "Doe",
              "profilePicture": "https://images.pexels.com/photos/19305961/pexels-photo-19305961/free-photo-of-a-woman-in-a-leather-skirt-leaning-against-a-wall.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1"
            },
            "_id": "67f7da0979ceba70f2af69db"
          },
          "isDirectMessage": false,
          "deletedBy": [],
          "createdAt": "2025-04-22T19:37:18.184Z",
          "updatedAt": "2025-04-22T20:06:26.573Z",
          "__v": 0,
          "lastMessage": {
            "_id": "6807f6c23dcfd182f9e2266f",
            "senderId": "67f7da0d79ceba70f2af6a05",
            "content": "Hello From Recruiter",
            "deletedForEveryone": false,
            "createdAt": "2025-04-22T20:06:26.553Z"
          }
        }
      ],
      "pagination": {
        "skip": 0,
        "limit": 10,
        "currentPage": 1,
        "pages": 1,
        "hasNextPage": false,
        "totalRecords": 3,
        "pageSize": 10
      }
    }
  }

/conversations/ GET
conversationId: "6808e6503dcfd182f9e22e06" string
/conversations/{conversationId}
{
    "success": true,
    "message": "Conversation retrieved successfully",
    "data": {
      "_id": "6808e6503dcfd182f9e22e06",
      "jobApplication": {
        "_id": "6807a1747dac5c8168f838f4",
        "job": {
          "_id": "6807a1657dac5c8168f838d4",
          "jobTitle": "backend-developer",
          "jobDescription": "Quia et quia tempor ",
          "applicationDeadline": "2025-12-31T00:00:00.000Z"
        },
        "status": "SHORTLISTED",
        "createdAt": "2025-04-22T14:02:28.622Z"
      },
      "recruiterProfileId": {
        "companyProfile": {
          "companyName": "Freeman and Mcconnell Co",
          "profilePicture": "https://yes-jobs-dev.s3.ap-southeast-2.amazonaws.com/profiles/user-67f7da0d79ceba70f2af6a05/1745313742147-company-3.png"
        },
        "_id": "67f7da0d79ceba70f2af6a07"
      },
      "jobSeekerProfileId": {
        "userProfile": {
          "firstName": "John",
          "lastName": "Doe",
          "profilePicture": "https://images.pexels.com/photos/19305961/pexels-photo-19305961/free-photo-of-a-woman-in-a-leather-skirt-leaning-against-a-wall.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1"
        },
        "_id": "67f7da0979ceba70f2af69db"
      },
      "isDirectMessage": false,
      "deletedBy": [],
      "createdAt": "2025-04-23T13:08:32.448Z",
      "updatedAt": "2025-04-23T13:08:32.448Z",
      "__v": 0
    }
  } 


/messages/ POST
Request body
{
    "conversationId": "6808e6503dcfd182f9e22e06",
    "content": "this is message",
    "mediaIds": [
        "string"
    ]
}
Response body
{
    "success": true,
    "message": "Message sent successfully",
    "data": {
      "conversationId": "6808e6503dcfd182f9e22e06",
      "senderId": "67f7da0d79ceba70f2af6a05",
      "senderType": "RECRUITER",
      "content": "this is message",
      "mediaFiles": [],
      "deletedBy": [],
      "deletedForEveryone": false,
      "read": false,
      "_id": "6808e7a33dcfd182f9e22e1d",
      "createdAt": "2025-04-23T13:14:11.408Z",
      "updatedAt": "2025-04-23T13:14:11.408Z",
      "__v": 0
    }
  }

/messages/{conversationId} GET

conversationId: "6808e6503dcfd182f9e22e06" string
{
    "success": true,
    "message": "Messages retrieved successfully",
    "data": {
      "messages": [
        {
          "_id": "6808e7a33dcfd182f9e22e1d",
          "conversationId": "6808e6503dcfd182f9e22e06",
          "senderId": {
            "_id": "67f7da0d79ceba70f2af6a05",
            "email": "<EMAIL>",
            "role": "RECRUITER"
          },
          "senderType": "RECRUITER",
          "content": "this is message",
          "mediaFiles": [],
          "deletedBy": [],
          "deletedForEveryone": false,
          "read": false,
          "createdAt": "2025-04-23T13:14:11.408Z",
          "updatedAt": "2025-04-23T13:14:11.408Z",
          "__v": 0
        }
      ],
      "pagination": {
        "skip": 0,
        "limit": 10,
        "currentPage": 1,
        "pages": 1,
        "hasNextPage": false,
        "totalRecords": 1,
        "pageSize": 10
      }
    }
  }


/messages/{messageId} DELETE
Request body
  {
    "deleteForEveryone": false
  }

  OR 
  {
    "deleteForEveryone": true
  }

Response body
  { 
    "success": true,
    "message": "Message deleted successfully",
    "data": {
      "success": true
    }
  }

/messages/{messageId}/read PATCH
messageId: "6808e7a33dcfd182f9e22e1d" string
Response body
{
    "success": true,
    "message": "Message marked as read successfully",
    "data": {
        "success": true
    }
}



# Yes Jobs Messaging System Documentation

This documentation outlines the complete messaging flow, including socket events, API endpoints, and the required payloads for the Yes Jobs messaging system.

---
  
## Socket Connection

**Connection URL:**

```
ws://localhost:3031/messages
```

### Authentication

Authentication is required to connect to the socket. Include your authentication token in the handshake request:

```js
// Example using Socket.IO client
const socket = io('http://localhost:3031/messages', {
  extraHeaders: {
    Authorization: 'Bearer YOUR_JWT_TOKEN',
  },
});
```

---

## Complete Messaging Flow

### 1. Connect to Socket

```js
const socket = io('http://localhost:3031/messages', {
  extraHeaders: {
    Authorization: 'Bearer YOUR_JWT_TOKEN',
  },
});

socket.on('connect', () => {
  console.log('Connected to messaging socket');
});

socket.on('error', (error) => {
  console.error('Socket error:', error);
});
```

### 2. Auto-Join Conversations

```js
socket.on('join_conversation', (data) => {
  if (data.autoJoined) {
    console.log(`Automatically joined ${data.conversationCount} conversations`);
  } else {
    console.log(`Joined conversation: ${data.conversationId}`);
  }
});
```

### 3. Create a Conversation

#### For Recruiters with Job Application

```js
// API Endpoint: POST /conversations
const createConversationPayload = {
  type: 'application',
  jobApplicationId: 'job_application_id_here',
};

// After successful API call:
socket.on('create_conversation', (data) => {
  console.log(`Conversation created: ${data.conversationId}`);
});
```

#### For Recruiters without Job Application (Direct Message)

```js
// API Endpoint: POST /conversations
const createDirectConversationPayload = {
  type: 'direct',
  jobSeekerProfileId: 'job_seeker_profile_id_here',
};

// After successful API call:
socket.on('create_conversation', (data) => {
  console.log(`Direct conversation created: ${data.conversationId}`);
});
```

### 4. Upload Media (if needed)

```js
// API Endpoint: POST /upload/media/chat
// Content-Type: multipart/form-data
// Include files in the form data

// Response:
{
  "success": true,
  "message": "Media has been uploaded",
  "data": {
    "mediaFiles": [
      {
        "url": "https://s3-url.com/file1.jpg",
        "s3Key": "messages/user-123/file1.jpg",
        "fileName": "file1.jpg",
        "fileSize": 1024
      }
    ]
  }
}
```

### 5. Send a Message

```js
// Socket Event: send_message
const messagePayload = {
  conversationId: 'conversation_id_here',
  content: 'Hello, this is a message',
  mediaFiles: [
    {
      url: 'https://s3-url.com/file1.jpg',
      s3Key: 'messages/user-123/file1.jpg',
      fileName: 'file1.jpg',
      fileSize: 1024,
    },
  ],
};

socket.emit('send_message', messagePayload);

// Listen for new messages
socket.on('new_message', (message) => {
  console.log('New message received:', message);
});
```

### 6. Mark Messages as Read

```js
// Socket Event: mark_as_read
const markAsReadPayload = {
  messageId: 'message_id_here',
};

socket.emit('mark_as_read', markAsReadPayload);

socket.on('message_read', (data) => {
  console.log(
    `Message ${data.messageId} was read by ${data.readBy} at ${data.readAt}`,
  );
});
```

### 7. Typing Indicators

```js
// Socket Event: typing
const typingPayload = {
  conversationId: 'conversation_id_here',
  isTyping: true,
};

socket.emit('typing', typingPayload);

socket.on('typing', (data) => {
  console.log(`${data.userName} is typing...`);
});

socket.on('stop_typing', (data) => {
  console.log(`${data.userName} stopped typing`);
});
```

### 8. Delete a Conversation

```js
// API Endpoint: DELETE /conversations/:conversationId

socket.on('delete_conversation', (data) => {
  console.log(`Conversation deleted: ${data.conversationId}`);
});

socket.emit('delete_conversation', {
  conversationId: 'conversation_id_here',
});
```

### 9. Leave a Conversation

```js
// Socket Event: leave_conversation
const leavePayload = {
  conversationId: 'conversation_id_here',
};

socket.emit('leave_conversation', leavePayload);

socket.on('leave_conversation', (data) => {
  console.log(`${data.userName} left the conversation`);
});
```

---

## API Endpoints

### Conversations

- **Create Conversation**  
  `POST /conversations`
- **Get All Conversations**  
  `GET /conversations?page=1&limit=10&search=optional_search_term`
- **Get Conversation by ID**  
  `GET /conversations/:conversationId`
- **Delete Conversation**  
  `DELETE /conversations/:conversationId`

### Messages

- **Get Messages**  
  `GET /messages?conversationId=conversation_id_here&page=1&limit=20`
- **Delete Message**  
  `DELETE /messages/:messageId?deleteForEveryone=true`

### Media Upload

- **Upload Media**  
  `POST /upload/media/chat`  
  Content-Type: multipart/form-data

---

## Socket Events Summary

| Event               | Direction       | Payload                                               | Description                       |
| ------------------- | --------------- | ----------------------------------------------------- | --------------------------------- |
| connection          | Client → Server | Auth token in headers                                 | Connect to the socket             |
| disconnect          | Client → Server | None                                                  | Disconnect from the socket        |
| create_conversation | Server → Client | { conversationId, jobSeekerId, isDirectMessage }      | New conversation notification     |
| join_conversation   | Client → Server | { conversationId }                                    | Join a conversation room          |
| join_conversation   | Server → Client | { userId, userName, conversationId, isDirectMessage } | Join notification                 |
| leave_conversation  | Client → Server | { conversationId }                                    | Leave a conversation room         |
| leave_conversation  | Server → Client | { userId, userName, conversationId }                  | Leave notification                |
| delete_conversation | Client → Server | { conversationId }                                    | Delete a conversation             |
| delete_conversation | Server → Client | { conversationId, userId }                            | Conversation deleted notification |
| send_message        | Client → Server | { conversationId, content, mediaFiles }               | Send a message                    |
| new_message         | Server → Client | Message object                                        | New message received              |
| mark_as_read        | Client → Server | { messageId }                                         | Mark a message as read            |
| message_read        | Server → Client | { messageId, readBy, readAt }                         | Message read notification         |
| typing              | Client → Server | { conversationId, isTyping: true }                    | Typing indicator                  |
| typing              | Server → Client | { userId, userName }                                  | Typing notification               |
| stop_typing         | Server → Client | { userId, userName }                                  | Stop typing notification          |
| error               | Server → Client | { message }                                           | Error notification                |

---

## Important Notes

### Media Handling

- Always upload media files **before** sending a message
- Store the returned media file objects and include them in the message payload
- Media files are stored directly in the message object

### Conversation Deletion

- Deleting a conversation only removes it **for the current user**
- Other participants can still see and interact with the conversation
- Deleted conversations won't appear in your list

### Authentication

- All API requests and socket connections require a **valid JWT token**
- Include in the `Authorization` header: `Bearer YOUR_JWT_TOKEN`

### Error Handling

- Always listen for the `error` event on the socket
- Check API responses for error messages

### Duplicate Prevention

- Duplicate conversations between the same users are prevented
- Existing conversation will be returned if it already exists

---

## Example Implementation Flow

1. Connect to the socket with authentication
2. Create a conversation or use an existing one
3. If sending media, upload using the upload API
4. Send messages through the socket
5. Listen for `new_message` and update UI
6. Mark messages as read when viewed
7. Use typing indicators for user experience
8. Allow users to delete or leave conversations as needed
