"use client";

import { Search } from "lucide-react";
import { useSearchParams, useRouter } from "next/navigation";
import type React from "react";
import { useEffect, useState } from "react";
import CompanyDashboardTable from "@/components/Cards/CompanyDashboardTable";
import LoadingSpinner from "@/components/LoadingSpinner/LoadingSpinner";
import Pagination from "@/components/Pagination/Pagination";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { SearchableCategorySelect } from "@/components/ui/searchable-category-select";
import { useGetAllApplicants } from "@/hooks/useQuery";
import type { IAllApplicantsSearchParams } from "@/types/query.types";

export default function AllApplicantsListing() {
  const searchParams = useSearchParams();
  const router = useRouter();
  // State for URL parameters
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCategory, setSelectedCategory] = useState<string | undefined>(undefined);
  const [currentPage, setCurrentPage] = useState(1);

  // Temporary state for form inputs (only applied when search button is clicked)
  const [tempSearchTerm, setTempSearchTerm] = useState("");
  const limit = 10;

  // Initialize search params from URL
  useEffect(() => {
    const search = searchParams.get("search") || "";
    const page = searchParams.get("page") || "1";
    const job_category = searchParams.get("job_category") || undefined;

    // Set both actual and temporary search values
    setSearchTerm(search);
    setTempSearchTerm(search);

    setCurrentPage(Number.parseInt(page, 10));
    setSelectedCategory(job_category);
  }, [searchParams]);

  // Prepare search params for API call
  const getSearchParams = (): IAllApplicantsSearchParams => {
    const params: IAllApplicantsSearchParams = {
      page: currentPage,
      limit,
    };

    if (searchTerm) params.search = searchTerm;
    if (selectedCategory) params.job_category = selectedCategory;

    return params;
  };

  // Fetch all applicants
  const {
    data: applicantsData,
    isLoading,
    isError,
    error,
  } = useGetAllApplicants(getSearchParams());

  // Update URL with search params
  const updateSearchParams = () => {
    const params = new URLSearchParams();
    if (searchTerm) params.set("search", searchTerm);
    if (currentPage > 1) params.set("page", currentPage.toString());
    if (selectedCategory) params.set("job_category", selectedCategory);

    router.push(`?${params.toString()}`, { scroll: false });
  };

  // Handle search form submission
  const handleSearch = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    // Apply temporary values to actual state
    setSearchTerm(tempSearchTerm);
    setCurrentPage(1); // Reset to first page on new search

    // Update URL with new search parameters
    const params = new URLSearchParams(searchParams.toString());

    // Update search term in URL
    if (tempSearchTerm) {
      params.set("search", tempSearchTerm);
    } else {
      params.delete("search");
    }

    // Reset to page 1
    params.set("page", "1");

    router.push(`?${params.toString()}`, { scroll: false });
  };

  // Handle pagination
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    setTimeout(updateSearchParams, 0);
  };

  // Format data for the table
  const formatTableData = () => {
    if (!applicantsData?.data?.applications) return [];

    return applicantsData.data.applications.map((application) => {
      // If job is null, only return minimal information
      if (application?.job === null) {
        return {
          id: application._id,
          name: `${application.jobSeeker.userProfile.firstName} ${application.jobSeeker.userProfile.lastName}`,
          imageUrl: application.jobSeeker.userProfile.profilePicture,
          status: application.status,
          cvUrl: application.jobSeeker.cvAttachments?.[0]?.cvUrl,
          jobSeekerId: application.jobSeeker._id,
        };
      }

      // Return full information when job exists
      return {
        id: application._id,
        name: `${application.jobSeeker.userProfile.firstName} ${application.jobSeeker.userProfile.lastName}`,
        jobTitle: application?.job?.jobTitle,
        appliedDate: new Date(application.appliedDate).toLocaleDateString(),
        imageUrl: application.jobSeeker.userProfile.profilePicture,
        status: application.status,
        cvUrl: application.jobSeeker.cvAttachments?.[0]?.cvUrl,
        jobSeekerId: application.jobSeeker._id,
      };
    });
  };

  // Show error state
  if (isError) {
    return (
      <div className="flex flex-col items-center justify-center p-8">
        <h2 className="text-red-500 text-xl font-bold mb-4">Error loading applicants</h2>
        <p className="text-gray-600">{error?.message || "An unexpected error occurred"}</p>
        <Button
          onClick={() => router.refresh()}
          className="mt-4 bg-orange-100 hover:bg-orange-200 text-white"
        >
          Try Again
        </Button>
      </div>
    );
  }

  return (
    <div>
      <div className="flex flex-wrap items-center gap-3 justify-between p-6">
        <div>
          <h2 className="text-black-100 text-2xl font-bold">All Applicants</h2>
        </div>
        <div className="flex flex-wrap gap-2 ml-auto items-center">
          <form onSubmit={handleSearch} className="flex gap-2">
            {/* Search input */}
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4" />
              <Input
                type="search"
                placeholder="Search"
                className="pl-10 bg-white border border-gray-100 text-base rounded-full"
                value={tempSearchTerm}
                onChange={(e) => setTempSearchTerm(e.target.value)}
              />
            </div>

            <div className="flex gap-2">
              <Button
                type="submit"
                className="bg-orange-100 hover:bg-orange-200 text-white rounded-full"
              >
                Search
              </Button>

              <Button
                type="button"
                variant="outline"
                className="border-gray-200 rounded-full"
                onClick={() => {
                  // Clear temporary search values
                  setTempSearchTerm("");

                  // If there are search params in the URL, clear them and trigger a new search
                  if (searchParams.has("search")) {
                    const params = new URLSearchParams(searchParams.toString());
                    params.delete("search");
                    router.push(`?${params.toString()}`, { scroll: false });
                  }
                }}
              >
                Clear Search
              </Button>
            </div>
          </form>

          {/* Job category filter */}
          <div className="w-64">
            <SearchableCategorySelect
              value={selectedCategory || ""}
              onValueChange={(value) => {
                if (value === "" || value === selectedCategory) {
                  setSelectedCategory(undefined);
                } else {
                  setSelectedCategory(value);
                }
                setCurrentPage(1); // Reset to first page on filter change

                // Update URL immediately with the new category
                const params = new URLSearchParams(searchParams.toString());
                if (value === "" || value === selectedCategory) {
                  params.delete("job_category");
                } else {
                  params.set("job_category", value);
                }
                params.set("page", "1"); // Reset to first page
                router.push(`?${params.toString()}`, { scroll: false });
              }}
              placeholder="Select job category"
              className="text-sm"
            />
          </div>
        </div>
      </div>

      {isLoading ? (
        <div className="flex justify-center items-center p-12">
          <LoadingSpinner />
        </div>
      ) : applicantsData?.data?.applications?.length === 0 ||
        applicantsData?.data?.applications.some((app) => app?.job === null) ? (
        <div className="flex flex-col items-center justify-center p-12">
          <h3 className="text-xl font-medium text-gray-600 mb-2">No applicants found</h3>
          <p className="text-gray-500">Try adjusting your search criteria</p>
        </div>
      ) : (
        <>
          <CompanyDashboardTable data={formatTableData()} />

          {/* Pagination */}
          {applicantsData?.data?.pagination && (
            <div className="flex justify-center my-6">
              <Pagination
                currentPage={currentPage}
                totalPages={applicantsData.data.pagination.pages}
                onPageChange={handlePageChange}
              />
            </div>
          )}
        </>
      )}
    </div>
  );
}
