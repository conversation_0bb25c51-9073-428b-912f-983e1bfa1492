"use client";

import Image from "next/image";
import React from "react";
import PrimaryButton from "@/components/Buttons/PrimaryButton";
import { PrimaryHeading } from "@/components/Headings/PrimaryHeading";

const ProfileCompletedComp = () => {
  return (
    <div className="text-center pb-10">
      <div>
        <Image
          src={"/images/completed-image.gif"}
          alt=""
          width={500}
          height={500}
          className="mx-auto"
        />
        <PrimaryHeading>
          Your Profile Is <span>Complete!</span>
        </PrimaryHeading>
        <p className="text-2xl font-normal text-gray-100 mt-4 leading-9">
          If you missed anything or want to update your details, head to Settings or My <br />{" "}
          Resume to make changes anytime.
        </p>
        <div className="mt-5 space-x-3">
          <PrimaryButton link="/dashboard/applied-jobs" text="Go to dashboard" />
          <PrimaryButton link="/for-candidates" text="Jobs" />
        </div>
      </div>
    </div>
  );
};

export default ProfileCompletedComp;
