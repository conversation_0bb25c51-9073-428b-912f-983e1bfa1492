@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  font-family: Arial, Helvetica, sans-serif;
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 240 10% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;
    --primary: 240 5.9% 10%;
    --primary-foreground: 0 0% 98%;
    --secondary: 240 4.8% 95.9%;
    --secondary-foreground: 240 5.9% 10%;
    --muted: 240 4.8% 95.9%;
    --muted-foreground: 240 3.8% 46.1%;
    --accent: 240 4.8% 95.9%;
    --accent-foreground: 240 5.9% 10%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 5.9% 90%;
    --input: 240 5.9% 90%;
    --ring: 240 10% 3.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
    --orange-100: #ec761e;
  }
  .dark {
    --background: 240 10% 3.9%;
    --foreground: 0 0% 98%;
    --card: 240 10% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 240 10% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 240 5.9% 10%;
    --secondary: 240 3.7% 15.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 240 3.7% 15.9%;
    --muted-foreground: 240 5% 64.9%;
    --accent: 240 3.7% 15.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 3.7% 15.9%;
    --input: 240 3.7% 15.9%;
    --ring: 240 4.9% 83.9%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
    --orange-100: #ec761e;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

.how-it-works-card:hover {
  background-color: var(--orange-100);
  color: #fff;
}

.how-it-works-card:hover h3,
.how-it-works-card:hover p {
  color: #fff;
}

.li-after:not(:last-child)::after {
  position: absolute;
  content: "";
  top: 35px;
  left: 16px;
  width: 2px;
  height: 100%;
  border: 2px dashed currentColor;
}

.modal-overflow::-webkit-scrollbar {
  width: 3px;
}

.modal-overflow::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
  border-radius: 10px;
}

.modal-overflow::-webkit-scrollbar-thumb {
  border-radius: 10px;
  -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.5);
}

.profile-link-after::after {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  background-color: var(--orange-100);
  width: 2px;
  height: 100%;
}

.setting-navigation::after {
  content: "";
  position: absolute;
  width: 10px;
  height: 100%;
  background-color: #e7e7e7;
  top: 0;
  left: 0;
  border-radius: 30px;
}
.setting-navigation-item::after {
  content: "";
  position: absolute;
  width: 10px;
  height: 24px;
  background-color: var(--orange-100);
  top: 0;
  left: 0;
  border-radius: 30px;
  z-index: 2;
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}

.overflow-wrap-anywhere {
  overflow-wrap: anywhere;
}

video::-webkit-media-controls-enclosure {
  overflow: hidden;
}

video::-webkit-media-controls-panel {
  width: calc(100% + 30px);
}

video::-webkit-media-controls-fullscreen-button,
video::-webkit-media-controls-download-button {
  display: none;
}

.text-line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

@media (max-width: 1280px) {
  .container {
    max-width: 90% !important;
  }
}

@media (max-width: 1023px) {
  .li-after:not(:last-child)::after {
    position: absolute;
    content: "";
    top: 35px;
    left: 16px;
    width: 0;
    height: 0;
    border: 0 dashed currentColor;
  }
}
