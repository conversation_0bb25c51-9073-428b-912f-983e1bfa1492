"use client";
import { useQueryClient } from "@tanstack/react-query";
import React, { useState } from "react";
import { useForm, type SubmitHandler } from "react-hook-form";
import { toast } from "sonner";
import { Input } from "../../ui/input";
import { Label } from "../../ui/label";
import { useUpdateCompanyProfile } from "@/hooks/useMutation";
import { useGetCompanyProfile } from "@/hooks/useQuery";
import { ICompany, ISocialNetwork } from "@/types/query.types";

const inputClasses = "h-12 px-4 rounded-full border border-gray-300 w-full";

const SocialNetworkEditCompany = () => {
  const queryClient = useQueryClient();
  const { data: companyData } = useGetCompanyProfile();
  const companyProfile = companyData?.data;
  const [networks, setNetworks] = useState<ISocialNetwork[]>(companyProfile?.socialNetworks || []);

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<Omit<ISocialNetwork, "_id">>({
    defaultValues: {
      networkName: "",
      networkUrl: "",
    },
  });

  const { mutate: updateProfile, isPending } = useUpdateCompanyProfile({
    onSuccess: () => {
      toast.success("Social networks updated successfully");
      queryClient.invalidateQueries({ queryKey: ["get-company-profile"] });
    },
    onError: (error) => {
      console.log({ error });
      toast.error(error.response?.data?.message || "Failed to update social networks");
    },
  });

  const onSubmit: SubmitHandler<Omit<ISocialNetwork, "_id">> = async (formData) => {
    if (!companyProfile?._id) {
      toast.error("Company profile not found");
      return;
    }

    const newNetwork: ISocialNetwork = {
      ...formData,
      _id: Date.now().toString(), // Temporary ID for new networks
    };

    const updatedNetworks = [...networks, newNetwork];
    setNetworks(updatedNetworks);

    const updatedProfile: ICompany = {
      ...companyProfile,
      socialNetworks: updatedNetworks,
    };

    updateProfile(updatedProfile);
  };

  const handleDelete = (networkId: string) => {
    if (!companyProfile?._id) {
      toast.error("Company profile not found");
      return;
    }

    const updatedNetworks = networks.filter((network) => network._id !== networkId);
    setNetworks(updatedNetworks);

    const updatedProfile: ICompany = {
      ...companyProfile,
      socialNetworks: updatedNetworks,
    };

    updateProfile(updatedProfile);
  };

  return (
    <div>
      <h2 className="text-blue-200 text-2xl font-bold">Company</h2>
      <h3 className="text-4xl font-medium text-black-100 mt-3 mb-6">Social Network</h3>
      <div className="lg:w-[80%] mt-8">
        <form onSubmit={handleSubmit(onSubmit)}>
          <div className="grid grid-cols-2 gap-7">
            <div>
              <Label htmlFor="networkName" className="mb-3 block">
                Network Name
              </Label>
              <Input
                className={inputClasses}
                id="networkName"
                {...register("networkName", {
                  required: "Network name is required",
                })}
              />
              {errors.networkName && (
                <p className="text-red-500 text-sm">{errors.networkName.message}</p>
              )}
            </div>
            <div>
              <Label htmlFor="networkUrl" className="mb-3 block">
                URL
              </Label>
              <Input
                className={inputClasses}
                id="networkUrl"
                type="url"
                {...register("networkUrl", {
                  required: "URL is required",
                  pattern: {
                    value: /^(https?:\/\/)?([\da-z.-]+)\.([a-z.]{2,6})([/\w .-]*)*\/?$/,
                    message: "Please enter a valid URL",
                  },
                })}
              />
              {errors.networkUrl && (
                <p className="text-red-500 text-sm">{errors.networkUrl.message}</p>
              )}
            </div>
          </div>

          <div className="mt-6">
            <button
              disabled={isPending}
              type="submit"
              className="text-blue-100 font-bold text-lg inline-flex gap-x-3 items-center"
            >
              <span>
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  fill="none"
                  viewBox="0 0 24 24"
                >
                  <path
                    fill="#007AB5"
                    fillRule="evenodd"
                    d="M12 0C5.373 0 0 5.373 0 12s5.373 12 12 12 12-5.373 12-12S18.627 0 12 0m1.09 16.364a1.09 1.09 0 1 1-2.18 0V13.09H7.635a1.09 1.09 0 1 1 0-2.182h3.273V7.636a1.09 1.09 0 1 1 2.182 0v3.273h3.273a1.09 1.09 0 1 1 0 2.182H13.09z"
                    clipRule="evenodd"
                  ></path>
                </svg>
              </span>
              <span>Add Network</span>
            </button>
          </div>
        </form>

        <div className="mt-8 space-y-4">
          {networks.map((network) => (
            <div
              key={network._id}
              className="flex items-center justify-between p-4 border rounded-lg"
            >
              <div>
                <h4 className="font-medium">{network.networkName}</h4>
                <a
                  href={network.networkUrl}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-blue-500 hover:underline"
                >
                  {network.networkUrl}
                </a>
              </div>
              <button
                onClick={() => handleDelete(network._id)}
                className="text-red-500 hover:text-red-700"
              >
                Delete
              </button>
            </div>
          ))}
        </div>

        <div className="mt-6">
          <button
            disabled={isPending}
            type="submit"
            className="bg-orange-100 text-white rounded-full px-10 py-3"
          >
            Update Social Network
          </button>
        </div>
      </div>
    </div>
  );
};

export default SocialNetworkEditCompany;
