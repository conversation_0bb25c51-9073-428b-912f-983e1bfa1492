"use client";

import { Loader2 } from "lucide-react";
import React, { useState } from "react";

interface UploadPortfolioProps {
  image?: string;
  onDelete?: () => void;
  onUpload?: (file: File, previewUrl: string) => void;
  onFilesSelected?: (files: FileList) => void; // New prop for multiple files
  multiple?: boolean; // Enable multiple file selection
  isPreview?: boolean;
  isDeleting?: boolean;
  isUploading?: boolean; // Whether the image is currently uploading
  uploadProgress?: number; // Upload progress percentage
  error?: string; // Error message if upload failed
  maxFiles?: number; // Maximum allowed files
}

const UploadPortfolio = ({
  image,
  onDelete,
  onUpload,
  onFilesSelected,
  multiple = false,
  isDeleting = false,
  isUploading = false,
  uploadProgress = 0,
  error,
  isPreview = false,
  maxFiles = 8,
}: UploadPortfolioProps) => {
  const [isDragging, setIsDragging] = useState(false);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (!files || files.length === 0) return;

    if (multiple && onFilesSelected) {
      if (files.length > maxFiles) {
        alert(`You can upload a maximum of ${maxFiles} files`);
        return;
      }
      onFilesSelected(files);
    } else if (onUpload) {
      // Handle multiple files in edit mode
      if (multiple) {
        Array.from(files).forEach((file) => {
          processSingleFile(file);
        });
      } else if (files[0]) {
        processSingleFile(files[0]);
      }
    }

    // Reset the input value to allow selecting the same file again
    e.target.value = "";
  };

  const processSingleFile = (file: File) => {
    // Validate file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      alert("File size should be less than 5MB");
      return;
    }

    // Validate file type
    if (!file.type.startsWith("image/")) {
      alert("Please upload an image file");
      return;
    }

    // Create temporary preview URL
    const previewUrl = URL.createObjectURL(file);
    onUpload?.(file, previewUrl);
  };

  const handleDragOver = (e: React.DragEvent<HTMLLabelElement>) => {
    e.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = () => {
    setIsDragging(false);
  };

  const handleDrop = (e: React.DragEvent<HTMLLabelElement>) => {
    e.preventDefault();
    setIsDragging(false);

    const files = e.dataTransfer.files;
    if (files.length > 0) {
      handleFileChange({
        target: { files },
      } as React.ChangeEvent<HTMLInputElement>);
    }
  };

  return (
    <div className="relative">
      {image ? (
        <div className="relative group">
          <img
            src={image}
            alt="Uploaded"
            className={`w-full h-[200px] object-cover rounded-lg ${
              isDeleting || isUploading ? "opacity-70" : ""
            }`}
          />
          {onDelete && (
            <button
              onClick={(e) => {
                e.stopPropagation();
                onDelete();
              }}
              disabled={isDeleting || isUploading}
              className={`absolute top-2 left-2 bg-white/80 hover:bg-red-500 text-gray-700 hover:text-white p-2 rounded-full transition-all duration-200 z-10 ${
                isDeleting
                  ? "opacity-100 cursor-not-allowed"
                  : isUploading
                    ? "opacity-50 cursor-not-allowed"
                    : "group-hover:opacity-100 opacity-0"
              }`}
              aria-label="Delete image"
            >
              {isDeleting ? (
                <Loader2 className="h-5 w-5 animate-spin" />
              ) : (
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-5 w-5"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                >
                  <path
                    fillRule="evenodd"
                    d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                    clipRule="evenodd"
                  />
                </svg>
              )}
            </button>
          )}
          {/* Upload progress indicator */}
          {isUploading && uploadProgress < 100 && (
            <div className="absolute bottom-0 left-0 right-0 bg-white/80 p-2">
              <div className="flex items-center justify-between mb-1">
                <span className="text-xs font-medium">Uploading...</span>
                <span className="text-xs font-medium">{Math.round(uploadProgress)}%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-1.5">
                <div
                  className="bg-orange-100 h-1.5 rounded-full transition-all duration-300"
                  style={{ width: `${uploadProgress}%` }}
                ></div>
              </div>
            </div>
          )}

          {/* Error message */}
          {error && (
            <div className="absolute bottom-0 left-0 right-0 bg-red-50 p-2">
              <span className="text-xs text-red-500">{error}</span>
            </div>
          )}

          {/* Preview indicator */}
          {isPreview && (
            <div className="absolute bottom-2 left-2 bg-white/80 px-3 py-1 rounded-full text-xs text-gray-700">
              Preview
            </div>
          )}
        </div>
      ) : (
        <label
          className={`cursor-pointer block ${isDragging ? "border-orange-100 bg-orange-50" : ""} ${isUploading ? "opacity-70 cursor-not-allowed" : ""}`}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
        >
          <div
            className={`w-full h-[200px] border-2 border-dashed ${
              isDragging
                ? "border-orange-100 bg-orange-50"
                : isUploading
                  ? "border-orange-100 bg-orange-50"
                  : "border-gray-300"
            } rounded-lg flex items-center justify-center transition-colors duration-200`}
          >
            <div className="text-center p-4">
              {isUploading ? (
                <div className="flex flex-col items-center justify-center">
                  <Loader2 className="h-12 w-12 text-orange-500 animate-spin" />
                  <p className="mt-4 text-sm font-medium text-gray-600">Uploading images...</p>
                  <div className="w-full mt-4 bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-orange-100 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${uploadProgress}%` }}
                    ></div>
                  </div>
                  <p className="mt-1 text-xs text-gray-500">
                    {Math.round(uploadProgress)}% complete
                  </p>
                </div>
              ) : (
                <>
                  <svg
                    className={`mx-auto h-12 w-12 ${isDragging ? "text-orange-100" : "text-gray-400"}`}
                    stroke="currentColor"
                    fill="none"
                    viewBox="0 0 48 48"
                    aria-hidden="true"
                  >
                    <path
                      d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02"
                      strokeWidth={2}
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                  </svg>
                  <div className="mt-4 flex flex-col text-sm text-gray-600 justify-center">
                    <span
                      className={`relative bg-white rounded-md font-medium ${
                        isDragging ? "text-orange-100" : "text-orange-100 hover:text-orange-200"
                      } focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-orange-100`}
                    >
                      {multiple ? "Upload files" : "Upload a file"}
                    </span>
                    <p className="pl-1">or drag and drop</p>
                  </div>
                  <p className="mt-1 text-xs text-gray-500">
                    {multiple ? `PNG, JPG up to 5MB` : "PNG, JPG up to 5MB"}
                  </p>
                </>
              )}
            </div>
          </div>
          <input
            type="file"
            accept="image/*"
            onChange={handleFileChange}
            className="hidden"
            multiple={multiple}
            disabled={isUploading}
          />
        </label>
      )}
    </div>
  );
};

export default UploadPortfolio;
