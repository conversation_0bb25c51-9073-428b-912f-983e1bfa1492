import { IUser, UserRole } from "./common.types";

// Job Application Types
export interface IJobApplication {
  _id: string;
  job: {
    _id: string;
    recruiterProfile: {
      _id: string;
      companyProfile: {
        companyName: string;
        profilePicture: string;
        foundedDate: string;
        companySize: string;
        websiteUrl: string;
        abn: string;
        location: Ilocation;
      };
    };
    isDeleted: boolean;
    jobTitle: string;
    isBoosted: boolean;
    isPremium: boolean;
    isSaved: boolean;
    applicationDeadline: string;
    jobCategory: string;
    jobType: string;
    jobMode: string;
    salaryType: string;
    salaryRangeStart: number;
    salaryRangeEnd: number;
    location: Ilocation;
  } | null;
  jobSeeker: string;
  status: "PENDING" | "SHORTLISTED" | "REJECTED";
  appliedDate: string;
  createdAt: string;
  updatedAt: string;

  __v: number;
}

export interface IGetJobApplicationsResponseDto {
  success: boolean;
  message: string;
  data: {
    applications: IJobApplication[];
    pagination: IPagination;
  };
}

export interface ITalentSection {
  _id: string;
  __v: number;
  description: string;
  heading: string;
  image: string;
  steps: IStep[];
}

export interface IGetTalentSectionResponseDto {
  success: boolean;
  message: string;
  data: {
    section: ITalentSection;
  };
}

export interface IDiscoverSection {
  _id: string;
  __v: number;
  description: string;
  heading: string;
  subheading: string;
}

export interface IGetDiscoverSectionResponseDto {
  success: boolean;
  message: string;
  data: {
    section: IDiscoverSection;
  };
}

export interface ICompaniesSection {
  _id: string;
  __v: number;
  description: string;
  heading: string;
  subheading: string;
}

export interface IGetCompaniesSectionResponseDto {
  success: boolean;
  message: string;
  data: {
    section: ICompaniesSection;
  };
}

// Saved Job Types
export interface ISavedJob {
  _id: string;
  jobSeeker: string;
  job: IAllJob;
  savedAt: string;
  createdAt: string;
  updatedAt: string;
  alreadyApplied: boolean;
  __v: number;
}

export interface IGetSavedJobsResponseDto {
  success: boolean;
  message: string;
  data: {
    savedJobs: ISavedJob[];
    pagination: IPagination;
  };
}

// Saved Candidate Types
export interface ISavedCandidate {
  _id: string;
  recruiter: string;
  candidate: {
    _id: string;
    userId: {
      _id: string;
      email: string;
      isActive: boolean;
      isProMember?: boolean;
    };
    userProfile: {
      firstName: string;
      lastName: string;
      phoneNo?: string;
      dob?: string;
      location?: Ilocation;
      websiteUrl?: string;
      portfolioUrl?: string;
      shortBio?: string;
      profilePicture?: string;
      designation?: string;
    };
  };
  savedAt: string;
  createdAt: string;
  updatedAt: string;
  __v: number;
}

export interface IGetSavedCandidatesResponseDto {
  success: boolean;
  message: string;
  data: {
    savedCandidates: ISavedCandidate[];
    pagination: IPagination;
  };
}

// Type for Job Applicant
export interface IJobApplicant {
  _id: string;
  job: {
    jobTitle: string;
    applicationDeadline: string;
    jobCategory: string;
    jobType: string;
    jobMode: string;
    salaryType: string;
    salaryRangeStart: number;
    salaryRangeEnd: number;
    location: IJobLocation;
  };
  jobSeeker: {
    _id: string;
    userProfile: {
      firstName: string;
      lastName: string;
      phoneNo: string;
      dob: string;
      location: IJobLocation;
      websiteUrl: string;
      portfolioUrl: string;
      shortBio: string;
      profilePicture: string;
      designation?: string;
    };
    cvAttachments: {
      cvUrl: string;
      cvName: string;
      uploadedDate: string;
      s3Key: string;
      isActive: boolean;
    };
  };
  status: string;
  appliedDate: string;
}

// Type for Get Job Applicants Response
export interface IGetJobApplicantsResponseDto {
  success: boolean;
  message: string;
  data: {
    applications: IJobApplicant[];
  };
}

// Shortlisted Applicants Types
export interface IShortlistedApplicant {
  _id: string;
  job: {
    _id: string;
    recruiterProfile: string;
    jobTitle: string;
    jobCategory: string;
  };
  jobSeeker: {
    _id: string;
    cvAttachments: {
      cvUrl: string;
      cvName: string;
      uploadedDate: string;
      s3Key: string;
      isActive: boolean;
    }[];
    userProfile: {
      designation: string;
      firstName: string;
      lastName: string;
      phoneNo: string;
      dob: string;
      location: Ilocation;
      websiteUrl: string;
      portfolioUrl: string;
      shortBio: string;
      profilePicture: string;
    };
  };
  status: string;
  appliedDate: string;
  createdAt: string;
  updatedAt: string;
  __v: number;
}

export interface IShortlistedApplicantsSearchParams {
  search?: string;
  coordinates?: [number, number];
  maxDistance?: string;
  page?: number;
  limit?: number;
  job_category?: string;
}

export interface IGetShortlistedApplicantsResponseDto {
  success: boolean;
  message: string;
  data: {
    applications: IShortlistedApplicant[];
    pagination: IPagination;
  };
}

export interface IAllApplicantsSearchParams {
  search?: string;
  coordinates?: [number, number];
  maxDistance?: string;
  page?: number;
  limit?: number;
  job_category?: string;
}

export interface IGetAllApplicantsResponseDto {
  success: boolean;
  message: string;
  data: {
    applications: IShortlistedApplicant[];
    pagination: IPagination;
  };
}

export interface IGetCurrentUserResponse {
  success: boolean;
  message: string;
  data: CurrentCompanyType | CurrentJobSeekerType;
}

export interface IGetJobSeekerProfileResponseDto {
  success: boolean;
  message: string;
  data: IProfileData;
}

export interface IGetProfileByIdResponseDto {
  success: boolean;
  message: string;
  data: IProfileData;
}

export interface IProfileData {
  user: IUser;
  _id: string;
  skills: string[];
  jobPreferences: IJobPreferences;
  userProfile: IUserProfile;
  notificationSettings: INotificationSettings;
  experiences: IExperience[];
  certificates: ICertificate[];
  academicExperiences: IAcademicExperience[];
  achievements: IAchievement[];
  portFolioImages: IPortfolioImage[];
  cvAttachments: ICvAttachment[];
  socialNetworks: ISocialNetwork[];
  createdAt: string;
  updatedAt: string;
  billingAndPaymentDetails: IBillingAndPaymentDetails;
  isSaved: boolean;
  proMembershipEndsAt?: string;
  proTrialEndsAt?: string;
}

export interface IJobPreferences {
  jobType: string; // Example: "FULL_TIME"
  jobCategory: string[]; // Example: ["SOFTWARE_DEVELOPMENT"]
  salaryRangeStart: number; // Example: 0
  salaryRangeEnd: number; // Example: 0
  location: IJobLocation;
}

export interface Ilocation {
  type: "Point";
  coordinates: [number, number];
  formattedAddress: string;
  city: string;
  state: string;
  country: string;
}

export interface IUserProfile {
  firstName: string;
  lastName: string;
  phoneNo: string;
  dob: string;
  location: {
    type: "Point";
    coordinates: [number, number];
    formattedAddress: string;
    city: string;
    state: string;
    country: string;
  };
  websiteUrl: string;
  portfolioUrl: string;
  shortBio: string;
  profilePicture: string;
  designation: string;
  proTrialEndsAt?: string;
  proMembershipEndsAt?: string;
}

export interface INotificationSettings {
  desktopNotification: boolean;
  emailNotification: boolean;
  jobAlerts: boolean;
  applicationStatusUpdates: boolean;
  announcementsAndUpdates: boolean;
}

export interface IExperience {
  _id: string;
  organizationName: string;
  designation: string;
  startDate: string;
  endDate?: string;
  jobType: string;
  jobDetails: string;
  isPresent: boolean;
}

export interface ICertificate {
  _id: string;
  instituteName: string;
  startDate: string;
  endDate: string;
  grade: number;
  certificate: string;
  certificateUrl: string;
}

export interface IAcademicExperience {
  _id: string;
  instituteName: string;
  startDate: string;
  endDate: string;
  grade: number;
  degree: string;
}

export interface IAchievement {
  _id: string;
  title: string;
  instituteName: string;
  details: string;
  date: string;
}

export interface IPortfolioImage {
  url: string;
  s3Key: string;
  uploadedAt: string;
}

export interface ICvAttachment {
  cvUrl: string;
  cvName: string;
  uploadedDate: string;
  s3Key: string;
  isActive: boolean;
}

export interface ISocialNetwork {
  _id: string;
  networkName: string;
  networkUrl: string;
}

export interface IBillingAndPaymentDetails {
  cardNumber: string;
  expiryDate: string;
  cvc: string;
}

export interface CurrentJobSeekerType {
  _id: string;
  email: string;
  role: UserRole;
  phoneNo: string;
  dob: string;
  friendlyAddress: string;
  city: string;
  country: string;
  websiteUrl: string;
  portfolioUrl: string;
  shortBio: string;
  profilePicture: string;
  firstName: string;
  lastName: string;
  proMembershipEndsAt?: string;
}
export interface CurrentCompanyType {
  _id: string;
  email: string;
  role: UserRole.RECRUITER;
  companyName: string;
  profilePicture: string;
  companyPhoneNo: string;
  foundedDate: string;
  companySize: string;
  websiteUrl: string;
  friendlyAddress: string;
  city: string;
  country: string;
}

// Type for Company Photos
export interface ICompanyPhoto {
  _id: string;
  url: string;
  s3Key: string;
  uploadedAt: string;
}

// Type for Perks and Benefits
export interface IPerkBenefit {
  _id: string;
  benefitName: string;
  benefitDescription: string;
}

// Type for Company Achievements
export interface ICompanyAchievement {
  _id: string;
  title: string;
  date?: string;
  eventOrInstitute: string;
  detail: string;
}

// Type for Email Notifications
export interface IEmailNotifications {
  newApplications: boolean;
  applicationUpdates: boolean;
  marketingEmails: boolean;
}

// Type for Job Preferences
export interface ICompanyJobPreferences {
  autoPublish: boolean;
  defaultJobDuration: number;
  defaultApplicationDeadline: number;
}

// Type for Privacy Settings
interface IPrivacySettings {
  showCompanySize: boolean;
  showFoundedDate: boolean;
  showLocation: boolean;
}

// Type for About Company
interface IAboutCompany {
  description: string;
  companyVideo: {
    url: string;
    s3Key: string;
  };
}

// Type for Company Profile
export interface ICompanyProfile {
  companyName: string;
  profilePicture: string;
  companyABN: number;
  foundedDate: string;
  companySize: string;
  websiteUrl: string;
  abn: number; // Added missing field
  companyEmail: string; // Added missing field
  location?: Ilocation; // Added location field
}

// Type for Main Company Data Object
export interface ICompany {
  _id: string;
  companyProfile: ICompanyProfile;
  emailNotifications: IEmailNotifications;
  jobPreferences: ICompanyJobPreferences;
  privacySettings: IPrivacySettings;
  socialNetworks: ISocialNetwork[];
  companyPhotos: ICompanyPhoto[];
  perksAndBenefits: IPerkBenefit[];
  companyAchievements: ICompanyAchievement[];
  createdAt: string;
  updatedAt: string;
  aboutCompany: IAboutCompany;
  user: IUser;
  openJobs: number;
  activeJobs: number;
}

// Type for API Response
export interface IGetCompanyProfileResponseDto {
  success: boolean;
  message: string;
  data: ICompany;
}

// DeletedBy interface for conversation deletion tracking
export interface IDeletedBy {
  userId: string;
  deletionDate: string;
}

// Conversation Types
export interface IConversation {
  _id: string;
  jobApplication?: {
    _id: string;
    job: {
      _id: string;
      jobTitle: string;
      jobDescription: string;
      applicationDeadline: string;
    };
    status: "PENDING" | "SHORTLISTED" | "REJECTED";
    createdAt: string;
  };
  recruiterProfileId: {
    companyProfile: {
      companyName: string;
      profilePicture: string;
    };
    _id: string;
  };
  jobSeekerProfileId: {
    userProfile: {
      firstName: string;
      lastName: string;
      profilePicture: string;
    };
    _id: string;
  };
  isDirectMessage: boolean;
  lastMessage?: IMessage;
  unreadCount?: number;
  deletedBy?: IDeletedBy[]; // Array of users who have deleted this conversation
  createdAt: string;
  updatedAt: string;
  __v: number;
}

export interface IGetConversationsResponseDto {
  success: boolean;
  message: string;
  data: {
    conversations: IConversation[];
    pagination: IPagination;
  };
}

export interface IGetConversationResponseDto {
  success: boolean;
  message: string;
  data: IConversation;
}

// Message Types
export interface IMediaFile {
  url: string;
  s3Key: string;
  fileName: string;
  fileSize: number;
}

export interface IMessage {
  _id: string;
  conversationId: string;
  senderId: string; // Changed to string to match API response
  senderType: string; // This is the key field for determining message sender
  content: string;
  mediaFiles: IMediaFile[];
  deletedBy: string[];
  deletedForEveryone: boolean;
  read: boolean;
  createdAt: string;
  updatedAt: string;
  status?: "sending" | "sent" | "delivered" | "read" | "error"; // For message status
  readAt?: string; // For read messages
}

export interface IGetMessagesResponseDto {
  success: boolean;
  message: string;
  data: {
    messages: IMessage[];
    pagination: IPagination;
  };
}

export interface IGetEnumsResponseDto {
  success: boolean;
  message: string;
  data: {
    ALL_ENUMS: string[];
    CAREER_LEVEL_ENUM: {
      INTERN: string;
      JUNIOR: string;
      MID_LEVEL: string;
      SENIOR: string;
      LEAD: string;
      DIRECTOR: string;
      EXECUTIVE: string;
    };
    EXPERIENCE_RANGE_ENUM: {
      YEAR_0_1: string;
      YEAR_1_2: string;
      YEAR_2_5: string;
      YEAR_5_10: string;
      YEAR_10_PLUS: string;
    };
    JOB_CATEGORIES_ENUM: {
      SOFTWARE_DEVELOPMENT: string;
      DATA_SCIENCE: string;
      DESIGN: string;
      MARKETING: string;
      HUMAN_RESOURCES: string;
      CUSTOMER_SERVICE: string;
      PROJECT_MANAGEMENT: string;
      CONTENT_WRITING: string;
    };
    JOB_MODE_ENUM: {
      REMOTE: string;
      HYBRID: string;
      ONSITE: string;
    };
    JOB_TYPE_ENUM: {
      FULL_TIME: string;
      PART_TIME: string;
      CONTRACT: string;
      FREELANCE: string;
      INTERNSHIP: string;
    };
    QUALIFICATION_ENUM: {
      DIPLOMA: string;
      BACHELOR: string;
      MASTER: string;
      PHD: string;
      CERTIFICATION: string;
    };
    ROLE_ENUM: {
      JOBSEEKER: string;
      RECRUITER: string;
      ADMIN: string;
    };
    SALARY_TYPE_ENUM: {
      MONTHLY: string;
      HOURLY: string;
      ANNUAL: string;
      PROJECT_BASED: string;
    };
    SKILLS_ENUM: {
      SOFTWARE_DEVELOPMENT: string[];
      DATA_SCIENCE: string[];
      DESIGN: string[];
      MARKETING: string[];
      HUMAN_RESOURCES: string[];
      CUSTOMER_SERVICE: string[];
      PROJECT_MANAGEMENT: string[];
      CONTENT_WRITING: string[];
    };
    SOCIAL_ACCOUNT_ENUM: {
      GOOGLE: string;
      APPLE: string;
    };
  };
}

export interface IJobLocation {
  type: "Point"; // The type of location (always "Point")
  coordinates: [number, number]; // Latitude and Longitude
  formattedAddress: string; // Full formatted address
  city: string; // City name
  state: string; // State name
  country: string; // Country name
}

export interface ICreateJobRequestDto {
  jobTitle: string; // Title of the job
  jobDescription: string; // Description of the job
  applicationDeadline: string; // Deadline for applications (ISO 8601 format)
  jobCategory: string; // Job category (e.g., SOFTWARE_DEVELOPMENT)
  jobType: string; // Job type (e.g., FULL_TIME, PART_TIME)
  jobMode: string; // Job mode (e.g., REMOTE, ONSITE)
  salaryType: string; // Salary type (e.g., MONTHLY, HOURLY)
  salaryRangeStart: number; // Starting salary range
  salaryRangeEnd: number; // Ending salary range
  experienceLevel: string; // Experience level (e.g., YEAR_0_1, YEAR_2_3)
  qualification: string; // Qualification required (e.g., DIPLOMA, BACHELORS)
  careerLevel: string; // Career level (e.g., INTERN, ENTRY_LEVEL)
  location: IJobLocation; // Location details
  keyResponsibilities: string; // Key responsibilities for the job
  skillsAndExperience: string; // Skills and experience required
  skillsTag: string[]; // Array of skill tags (e.g., ["JavaScript", "React"])
  isJobActive?: boolean; // Whether the job is active
  shortlistedApplicantsCount?: number; // Whether the job is active
  applicantsCount?: number; // Whether the job is active
  shortlistedApplicants?: number; // Whether the job is active
  totalApplicants?: number; // Whether the job is active
  isDeleted?: boolean;
  isSaved?: boolean;
  premiumExpireAt?: string;
}

export interface IJobData extends ICreateJobRequestDto {
  recruiterProfile: ICompanyProfile; // ID of the recruiter profile
  _id: string; // Unique ID of the job
  createdAt: string; // Job creation timestamp (ISO 8601 format)
  updatedAt: string; // Job update timestamp (ISO 8601 format)
  __v: number; // Version key
}

export interface ICreateJobResponseDto {
  success: boolean; // Indicates if the job creation was successful
  message: string; // Message from the server (e.g., "Job posting created successfully")
  data: IJobData; // The created job data
}

// Type for Recruiter Profile in Job Postings
export interface IRecruiterProfile {
  abn: string; // Australian Business Number
  companyName: string; // Name of the company
  companyPhoneNo: string; // Phone number of the company
  foundedDate: string; // Date the company was founded (ISO 8601 format)
  companySize: string; // Size of the company
  websiteUrl: string; // Website URL of the company
  friendlyAddress: string; // Friendly address of the company
  city: string; // City where the company is located
  country: string; // Country where the company is located
}

// Type for Job Posting
export interface IRecruiterJobPosting {
  _id: string; // Unique ID of the job posting
  recruiterProfile: ICompanyProfile; // Recruiter profile details
  jobTitle: string; // Title of the job
  jobDescription: string; // Description of the job
  applicationDeadline: string; // Deadline for applications (ISO 8601 format)
  isJobActive: boolean; // Whether the job is active
  jobCategory: string; // Job category (e.g., SOFTWARE_DEVELOPMENT)
  jobType: string; // Job type (e.g., FULL_TIME, PART_TIME)
  jobMode: string; // Job mode (e.g., REMOTE, ONSITE)
  salaryType: string; // Salary type (e.g., MONTHLY, HOURLY)
  salaryRangeStart: number; // Starting salary range
  salaryRangeEnd: number; // Ending salary range
  experienceLevel: string; // Experience level (e.g., YEAR_0_1, YEAR_2_3)
  qualification: string; // Qualification required (e.g., DIPLOMA, BACHELORS)
  careerLevel: string; // Career level (e.g., INTERN, ENTRY_LEVEL)
  location: IJobLocation; // Location details
  keyResponsibilities: string; // Key responsibilities for the job
  skillsAndExperience: string; // Skills and experience required
  skillsTag: string[]; // Array of skill tags (e.g., ["JavaScript", "React"])
  createdAt: string; // Job creation timestamp (ISO 8601 format)
  updatedAt: string; // Job update timestamp (ISO 8601 format)
  applicantsCount: number; // Job update timestamp (ISO 8601 format)
  shortlistedApplicantsCount: number; // Job update timestamp (ISO 8601 format)
  premiumExpireAt?: string; // Job update timestamp (ISO 8601 format)
  __v: number; // Version key
}

// Type for Get Recruiter Job Response
export interface IGetRecruiterJobResponseDto {
  success: boolean; // Indicates if the API call was successful
  message: string; // Message from the server (e.g., "Recruiter job postings retrieved successfully")
  data: {
    jobs: IRecruiterJobPosting[]; // Array of job postings
    pagination: IPagination;
  };
}

// Type for Recent Job
export interface IRecentJob {
  _id: string; // Unique ID of the job posting
  recruiterProfile: ICompanyProfile; // ID of the recruiter profile
  jobTitle: string; // Title of the job
  jobDescription: string; // Description of the job
  applicationDeadline: string; // Deadline for applications (ISO 8601 format)
  isJobActive: boolean; // Whether the job is active
  jobCategory: string; // Job category (e.g., SOFTWARE_DEVELOPMENT)
  jobType: string; // Job type (e.g., FULL_TIME, PART_TIME)
  jobMode: string; // Job mode (e.g., REMOTE, ONSITE)
  salaryType: string; // Salary type (e.g., MONTHLY, HOURLY)
  salaryRangeStart: number; // Starting salary range
  salaryRangeEnd: number; // Ending salary range
  experienceLevel: string; // Experience level (e.g., YEAR_0_1, YEAR_2_3)
  qualification: string; // Qualification required (e.g., DIPLOMA, BACHELORS)
  careerLevel: string; // Career level (e.g., INTERN, ENTRY_LEVEL)
  location: IJobLocation; // Location details
  keyResponsibilities: string; // Key responsibilities for the job
  skillsAndExperience: string; // Skills and experience required
  skillsTag: string[]; // Array of skill tags (e.g., ["JavaScript", "React"])
  createdAt: string; // Job creation timestamp (ISO 8601 format)
  updatedAt: string; // Job update timestamp (ISO 8601 format)
  premiumExpireAt: string;
  shortlistedApplicantsCount: number; // Job update timestamp (ISO 8601 format)
  applicantsCount: number; // Job update timestamp (ISO 8601 format)
  isDeleted: boolean;
  __v: number; // Version key
}

// Type for Get Recent Jobs Response
export interface IGetRecentJobsResponseDto {
  success: boolean; // Indicates if the API call was successful
  message: string; // Message from the server (e.g., "Recent jobs retrieved successfully")
  data: {
    jobs: IRecentJob[]; // Array of recent job postings
    pagination: IPagination;
  };
}

// New types for All Jobs API

export interface IJobSearchParams {
  search?: string;
  page?: number;
  limit?: number;
  coordinates?: [number, number]; // This will ensure coordinates are always in [long, lat] format
  maxDistance?: number;
  jobType?: string;
  experienceLevel?: string;
  qualification?: string;
  careerLevel?: string;
  salaryType?: string;
}

export interface IJobFilterState extends IJobSearchParams {
  jobId?: string; // Separate jobId from search params
}

export interface IAllJob {
  _id: string;
  recruiterProfile: {
    companyProfile: ICompanyProfile;
    _id: string;
  };
  isSaved?: boolean;
  isDeleted?: boolean;
  premiumExpireAt?: string;
  alreadyApplied?: boolean;
  jobTitle: string;
  jobDescription: string;
  applicationDeadline: string;
  isJobActive: boolean;
  jobCategory: string;
  jobType: string;
  jobMode: string;
  salaryType: string;
  salaryRangeStart: number;
  salaryRangeEnd: number;
  experienceLevel: string;
  qualification: string;
  careerLevel: string;
  location: IJobLocation;
  keyResponsibilities: string;
  skillsAndExperience: string;
  skillsTag: string[];
  createdAt: string;
  updatedAt: string;
  __v: number;
}

export interface IPagination {
  skip: number;
  limit: number;
  currentPage: number;
  pages: number;
  hasNextPage: boolean;
  totalRecords: number;
  pageSize: number;
}

export interface IGetAllJobsResponseDto {
  success: boolean;
  message: string;
  data: {
    allJobs: IAllJob[];
    pagination: IPagination;
  };
}

// Types for All JobSeekers API
export interface IJobSeekerSearchParams {
  search?: string;
  page?: number;
  limit?: number;
  coordinates?: [number, number]; // This will ensure coordinates are always in [long, lat] format
  maxDistance?: number;
  jobType?: string;
  jobCategory?: string;
}

export interface IJobSeekerFilterState extends IJobSeekerSearchParams {
  jobSeekerId?: string; // Separate jobSeekerId from search params
}

export interface IJobSeekerLocation {
  type: "Point";
  coordinates: [number, number];
  formattedAddress: string;
  city: string;
  state: string;
  country: string;
}

export interface IJobPreference {
  location: IJobSeekerLocation;
  jobType: string;
  jobCategory: string[];
  salaryRangeStart: number;
  salaryRangeEnd: number;
}

export interface IUserProfile {
  firstName: string;
  lastName: string;
  phoneNo: string;
  dob: string;
  location: IJobSeekerLocation;
  websiteUrl: string;
  portfolioUrl: string;
  shortBio: string;
  profilePicture: string;
  proMembershipEndsAt?: string;
}

export interface IJobSeeker {
  _id: string;
  jobPreferences: IJobPreference;
  userProfile: IUserProfile;
  skills: string[];
  isSaved: boolean;
}

export interface IGetAllJobSeekersResponseDto {
  success: boolean;
  message: string;
  data: {
    allJobSeekers: IJobSeeker[];
    pagination: IPagination;
  };
}

// Types for Related Companies API
export interface IRelatedCompaniesParams {
  page?: string | number;
  limit?: string | number;
  includeLocation?: string;
  includeCompanySize?: string;
  includeDescription?: string;
  maxDistance?: string | number;
}

export interface IRelatedCompany {
  _id: string;
  userId: string;
  activeJobs: number;
  companyProfile: {
    abn: string;
    companyName: string;
    foundedDate: string;
    companySize: string;
    websiteUrl: string;
    profilePicture: string;
    location?: {
      type: string;
      coordinates: number[];
      formattedAddress: string;
      city: string;
      state: string;
      country: string;
    };
  };
  emailNotifications: {
    newApplications: boolean;
    applicationUpdates: boolean;
    marketingEmails: boolean;
  };
  jobPreferences: {
    autoPublish: boolean;
    defaultJobDuration: number;
    defaultApplicationDeadline: number;
  };
  socialNetworks: ISocialNetwork[];
  companyPhotos: ICompanyPhoto[];
  perksAndBenefits: IPerkBenefit[];
  companyAchievements: ICompanyAchievement[];
  createdAt: string;
  updatedAt: string;
  __v: number;
  aboutCompany?: {
    description: string;
    companyVideo?: {
      url: string;
      s3Key: string;
      uploadedAt: string;
    };
  };
}

export interface IGetRelatedCompaniesResponseDto {
  success: boolean;
  message: string;
  data: {
    success: boolean;
    data: IRelatedCompany[];
  };
}

// Types for Related Candidates API
export interface IRelatedCandidatesParams {
  page?: string | number;
  limit?: string | number;
  includeSkills?: string | boolean;
  includeJobType?: string | boolean;
  includeJobCategory?: string | boolean;
  includeSalaryRange?: string | boolean;
  includeLocation?: string | boolean;
  maxDistance?: string | number;
}

export interface IRelatedCandidate {
  _id: string;
  userId: string;
  skills: string[];
  jobPreferences: {
    jobType: string;
    jobCategory: string[];
    salaryRangeStart: number;
    salaryRangeEnd: number;
    location?: {
      type: string;
      coordinates: number[];
      formattedAddress: string;
      city: string;
      state: string;
      country: string;
    };
  };
  userProfile: {
    firstName: string;
    lastName: string;
    phoneNo?: string;
    shortBio?: string;
    profilePicture?: string;
    designation?: string;
    proMembershipEndsAt?: string;
    proTrialEndsAt?: string;
  };
  notificationSettings: INotificationSettings;
  experiences: IExperience[];
  certificates: ICertificate[];
  academicExperiences: IAcademicExperience[];
  achievements: IAchievement[];
  portFolioImages: IPortfolioImage[];
  cvAttachments: ICvAttachment[];
  socialNetworks: ISocialNetwork[];
  createdAt: string;
  updatedAt: string;
  __v: number;
  isSaved?: boolean;
  isProMember?: boolean;
}

export interface IGetRelatedCandidatesResponseDto {
  success: boolean;
  message: string;
  data: {
    success: boolean;
    data: IRelatedCandidate[];
    pagination: IPagination;
  };
}

export interface ICompanyVideoListing {
  url: string;
  s3Key: string;
  uploadedAt: string;
}

export interface ICompanyProfileListing {
  companyName: string;
  profilePicture?: string;
  foundedDate: string;
  companySize: string;
  websiteUrl: string;
  abn: string;
  location?: IJobLocation;
}

export interface IAboutCompanyListing {
  companyVideo?: ICompanyVideoListing;
  description: string;
}

export interface ICompanyListing {
  _id: string;
  companyProfile: ICompanyProfileListing;
  aboutCompany: IAboutCompanyListing;
  activeJobs: number;
}

export interface IGetAllCompaniesResponseDto {
  success: boolean;
  message: string;
  data: {
    allCompanies: ICompanyListing[];
    pagination: IPagination;
  };
}

// Payment Types
export interface IPayment {
  _id: string;
  userId: string;
  amount: number;
  paymentIntentId: string;
  paymentStatus: string;
  details: {
    expiresAt: string;
    type: string;
  };
  createdAt: string;
  updatedAt: string;
  __v: number;
}

export interface IGetPaymentsResponseDto {
  success: boolean;
  message: string;
  data: {
    payments: IPayment[];
    pagination: IPagination;
  };
}

export interface IMetadata {
  jobId?: string;
  applicationId?: string;
}

// Notification Types
export interface INotification {
  _id: string;
  userId: string;
  title: string;
  message: string;
  type: string; // e.g., "JOB_APPLICATION"
  isRead: boolean;
  metadata?: IMetadata; // Flexible for additional data
  expiresAt?: string;
  createdAt: string;
  updatedAt: string;
  __v: number;
}

export interface IGetNotificationsResponseDto {
  success: boolean;
  message: string;
  data: {
    notifications: INotification[];
    pagination: IPagination;
  };
}

export interface IGetNotificationsParams {
  page?: number | string;
  limit?: number | string;
  unreadOnly?: boolean | string;
}

export interface IGetUnreadNotificationsCountResponseDto {
  success: boolean;
  message: string;
  data: {
    count: number;
  };
}

export interface IStep {
  heading: string;
  description: string;
  _id: string;
}

export interface IOpportunitySection {
  _id: string;
  __v: number;
  description: string;
  heading: string;
  image: string;
  steps: IStep[];
}

export interface IGetOpportunitySectionResponseDto {
  success: boolean;
  message: string;
  data: {
    section: IOpportunitySection;
  };
}

export interface IPartner {
  name: string;
  imageURL: string;
  _id: string;
}

export interface IPartnersSection {
  _id: string;
  __v: number;
  description: string;
  heading: string;
  partners: IPartner[];
}

export interface IGetPartnersSectionResponseDto {
  success: boolean;
  message: string;
  data: {
    section: IPartnersSection;
  };
}

export interface ICtaSection {
  _id: string;
  __v: number;
  description: string;
  heading: string;
  image: string;
}

export interface IGetCtaSectionResponseDto {
  success: boolean;
  message: string;
  data: {
    section: ICtaSection;
  };
}

export interface IGetFirebaseTokenResponseDto {
  success: boolean;
  message: string;
  data: {
    firebaseTokens: string[];
  };
}

export interface IAppSection {
  _id: string;
  __v: number;
  appStoreURL: string;
  description: string;
  heading: string;
  playStoreURL: string;
}

export interface IGetAppSectionResponseDto {
  success: boolean;
  message: string;
  data: {
    section: IAppSection;
  };
}

// About Us Page Types
export interface IOurMission {
  heading: string;
  description: string;
}

export interface IAboutUs {
  heading: string;
  description: string;
  image: string;
}

export interface IOurVision {
  heading: string;
  description: string;
}

export interface IValue {
  name: string;
  description: string;
  iconImage: string;
  _id: string;
}

export interface IOurValues {
  heading: string;
  description: string;
  values: IValue[];
}

export interface IAboutUsPage {
  _id: string;
  __v: number;
  ourMission: IOurMission;
  aboutUs: IAboutUs;
  ourVision: IOurVision;
  ourValues: IOurValues;
}

export interface IGetAboutUsPageResponseDto {
  success: boolean;
  message: string;
  data: {
    page: IAboutUsPage;
  };
}

// How It Works Page Types
export interface IHowItWorksSection {
  heading: string;
  description: string;
  steps: IStep[];
}

export interface IBenefit {
  heading: string;
  description: string;
  iconImage: string;
  _id: string;
}

export interface IHowItWorksPage {
  _id: string;
  __v: number;
  forCandidates: IHowItWorksSection;
  forRecruiters: IHowItWorksSection;
  benefits: IBenefit[];
  description: string;
  heading: string;
  image: string;
}

export interface IGetHowItWorksPageResponseDto {
  success: boolean;
  message: string;
  data: {
    page: IHowItWorksPage;
  };
}

// Contact Us Page Types
export interface IContactUsPage {
  _id: string;
  __v: number;
  description: string;
  heading: string;
  image: string;
}

// Terms and Conditions Page Types
export interface ITermsAndConditionsPage {
  _id: string;
  HTMLContent: string;
  __v: number;
  heading: string;
  lastUpdated: string;
}

export interface IGetTermsAndConditionsPageResponseDto {
  success: boolean;
  message: string;
  data: {
    page: ITermsAndConditionsPage;
  };
}

// Privacy Policy Page Types
export interface IPrivacyPolicyPage {
  _id: string;
  HTMLContent: string;
  __v: number;
  heading: string;
  lastUpdated: string;
}

export interface IGetPrivacyPolicyPageResponseDto {
  success: boolean;
  message: string;
  data: {
    page: IPrivacyPolicyPage;
  };
}

export interface IGetContactUsPageResponseDto {
  success: boolean;
  message: string;
  data: {
    page: IContactUsPage;
  };
}

// Skill Types
export interface ISkill {
  _id: string;
  skill: string;
  __v: number;
}

export interface IGetSkillsResponseDto {
  success: boolean;
  message: string;
  data: {
    skills: ISkill[];
    pagination: IPagination;
  };
}

export interface IGetSkillsParams {
  search?: string;
  page?: string | number;
  limit?: string | number;
}

// Category Types
export interface ICategory {
  _id: string;
  category: string;
  __v: number;
}

export interface IGetCategoriesResponseDto {
  success: boolean;
  message: string;
  data: {
    categories: ICategory[];
    pagination: IPagination;
  };
}

export interface IGetCategoriesParams {
  search?: string;
  page?: string | number;
  limit?: string | number;
}
