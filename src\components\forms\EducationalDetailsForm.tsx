"use client";

import React, { useEffect } from "react";
import { useForm, type SubmitHandler } from "react-hook-form";
import { Input } from "../ui/input";
import { Label } from "../ui/label";
import { useUpdateJobSeekerProfile } from "@/hooks/useMutation";
import { IAcademicExperience } from "@/types/query.types";

const inputClasses = "h-12 px-4 rounded-full border border-gray-300 w-full";

interface EducationalDetailsFormProps {
  goBack: () => void;
  onSuccess: (formData: IAcademicExperience[]) => void;
  onError: (error: unknown) => void;
  defaultValues?: IAcademicExperience[];
}

const EducationalDetailsForm = ({
  goBack,
  onSuccess,
  onError,
  defaultValues = [],
}: EducationalDetailsFormProps) => {
  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    watch,
  } = useForm<IAcademicExperience>({
    defaultValues: {
      instituteName: "",
      startDate: "",
      endDate: "",
      degree: "",
      grade: undefined,
    },
  });

  // Get current date in YYYY-MM-DD format for validation
  const today = new Date().toISOString().split("T")[0];

  // Format date from ISO string to YYYY-MM-DD for date inputs
  const formatDateForInput = (dateString: string | undefined): string => {
    if (!dateString) return "";
    const date = new Date(dateString);
    if (isNaN(date.getTime())) return "";
    return date.toISOString().split("T")[0] || "";
  };

  // Watch the start date to use in end date validation
  const startDate = watch("startDate");

  // Set form values when editing an existing academic experience
  useEffect(() => {
    if (defaultValues.length > 0 && defaultValues[0]) {
      const experience = defaultValues[0];
      reset({
        ...experience,
        startDate: formatDateForInput(experience.startDate),
        endDate: formatDateForInput(experience.endDate),
      });
    } else {
      // Clear form when adding a new academic experience
      reset({
        instituteName: "",
        startDate: "",
        endDate: "",
        degree: "",
        grade: undefined,
      });
    }
  }, [defaultValues, reset]);

  const { mutateAsync: updateProfile, isPending } = useUpdateJobSeekerProfile();

  const onSubmit: SubmitHandler<IAcademicExperience> = async (data) => {
    try {
      const formattedData = {
        academicExperiences: [
          {
            ...data,
            _id: defaultValues[0]?._id || "", // Include _id if editing existing entry
            startDate: new Date(data.startDate).toISOString(),
            endDate: new Date(data.endDate).toISOString(),
            grade: data.grade ? Number(data.grade) : 0,
          },
        ],
      };

      console.log(formattedData, "formattedDataformattedDataformattedData");

      await updateProfile({
        academicExperiences: [...defaultValues, ...formattedData.academicExperiences],
      });
      onSuccess(formattedData.academicExperiences);
    } catch (error: unknown) {
      onError(error);
    }
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      <div className="mb-6">
        <Label htmlFor="instituteName" className="mb-3 block">
          Institute / university name
        </Label>
        <Input
          className={inputClasses}
          id="instituteName"
          {...register("instituteName", {
            required: "Institute name is required",
          })}
        />
        {errors.instituteName && (
          <p className="text-red-500 text-sm">{errors.instituteName.message}</p>
        )}
      </div>

      <div className="grid sm:grid-cols-2 gap-6 mb-6">
        <div>
          <Label htmlFor="startDate" className="mb-3 block">
            Select Duration Starts
          </Label>
          <Input
            className={`${inputClasses} w-full block text-gray-100`}
            type="date"
            id="startDate"
            max={today}
            {...register("startDate", {
              required: "Start date is required",
              validate: (value) => {
                if (!value) return true;
                const selectedDate = new Date(value);
                const currentDate = new Date();
                currentDate.setHours(0, 0, 0, 0);

                if (selectedDate > currentDate) {
                  return "Date cannot be in the future";
                }

                return true;
              },
            })}
          />
          {errors.startDate && <p className="text-red-500 text-sm">{errors.startDate.message}</p>}
        </div>
        <div>
          <Label htmlFor="endDate" className="mb-3 block">
            Select Duration Ends
          </Label>
          <Input
            className={`${inputClasses} w-full block text-gray-100`}
            type="date"
            id="endDate"
            max={today}
            {...register("endDate", {
              required: "End date is required",
              validate: (value) => {
                if (!value) return true;

                // Check if end date is in the future
                const selectedDate = new Date(value);
                const currentDate = new Date();
                currentDate.setHours(0, 0, 0, 0);

                if (selectedDate > currentDate) {
                  return "Date cannot be in the future";
                }

                // Check if end date is after start date
                if (startDate && new Date(value) <= new Date(startDate)) {
                  return "End date must be after start date";
                }

                return true;
              },
            })}
          />
          {errors.endDate && <p className="text-red-500 text-sm">{errors.endDate.message}</p>}
        </div>
      </div>

      <div className="mb-6">
        <Label htmlFor="degree" className="mb-3 block">
          Degree/Program
        </Label>
        <Input
          className={inputClasses}
          id="degree"
          {...register("degree", { required: "Degree is required" })}
        />
        {errors.degree && <p className="text-red-500 text-sm">{errors.degree.message}</p>}
      </div>

      <div>
        <Label htmlFor="grade" className="mb-3 block">
          Mention Grade you achieved (optional)
        </Label>
        <Input
          className={inputClasses}
          id="grade"
          type="number"
          step="0.01"
          {...register("grade", {
            valueAsNumber: true,
            validate: (value) =>
              !value || (value >= 0 && value <= 4) || "Grade must be between 0 and 4",
          })}
        />
        {errors.grade && <p className="text-red-500 text-sm">{errors.grade.message}</p>}
      </div>

      <div className="flex flex-wrap gap-y-3 gap-x-5 mt-10">
        <button
          onClick={(e) => {
            e.preventDefault();
            goBack();
          }}
          type="button"
          className="font-bold py-4 px-10 font-base rounded-full inline-flex items-center justify-center space-x-2 bg-gray-300 text-gray-100"
        >
          Go Back
        </button>
        <button
          type="submit"
          disabled={isPending}
          className="font-bold py-4 px-10 font-base rounded-full inline-flex items-center justify-center space-x-2 bg-orange-100 text-white"
        >
          {isPending ? "Saving..." : "Save Details"}
        </button>
      </div>
    </form>
  );
};

export default EducationalDetailsForm;
