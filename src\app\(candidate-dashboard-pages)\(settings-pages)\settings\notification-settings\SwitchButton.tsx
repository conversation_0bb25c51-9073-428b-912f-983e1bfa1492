"use client";

import React from "react";
import { Switch } from "@/components/ui/switch";

interface SwitchButtonProps {
  isChecked: boolean;
  onChange: (checked: boolean) => void;
  disabled?: boolean;
}

const SwitchButton: React.FC<SwitchButtonProps> = ({ isChecked, onChange, disabled = false }) => {
  return (
    <Switch
      checked={isChecked}
      onCheckedChange={onChange}
      disabled={disabled}
      className={`${
        isChecked
          ? "bg-orange-100 data-[state=checked]:bg-orange-100"
          : "bg-gray-300 data-[state=unchecked]:bg-gray-300"
      }`}
    />
  );
};

export default SwitchButton;
