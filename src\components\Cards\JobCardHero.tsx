import Image from "next/image";
import React from "react";

interface JobCardHeroProps {
  imageUrl: string;
  jobTitle: string;
  companyName: string;
  jobTags: string[];
}

const JobCardHero: React.FC<JobCardHeroProps> = ({ imageUrl, jobTitle, companyName, jobTags }) => {
  return (
    <div className="shadow-2xl rounded-xl p-6">
      <Image src={imageUrl} alt={jobTitle} className="w-[40px] h-[40px]" width={40} height={40} />
      <h2 className="font-medium text-black-100 text-lg mt-6 mb-3">{jobTitle}</h2>
      <h3 className="text-gray-100 font-normal mb-6">{companyName}</h3>
      <div className="flex flex-wrap gap-2">
        {jobTags.map((tag, index) => (
          <span key={index} className="bg-gray-200 rounded-full px-6 py-2 text-gray-100 font-sm">
            {tag}
          </span>
        ))}
      </div>
    </div>
  );
};

export default JobCardHero;
