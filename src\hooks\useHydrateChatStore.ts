"use client";

import { useEffect } from "react";
import { useChatStore } from "@/store/useChatStore";

/**
 * Hook to hydrate the chat store on the client side
 * This is necessary to avoid hydration mismatches between server and client
 */
export function useHydrateChatStore() {
  const { hasHydrated, setHasHydrated } = useChatStore();

  // Only run once on the client side
  useEffect(() => {
    // Skip if already hydrated
    if (hasHydrated) return;

    // Mark the store as hydrated
    setHasHydrated(true);
  }, [hasHydrated, setHasHydrated]);

  return hasHydrated;
}
