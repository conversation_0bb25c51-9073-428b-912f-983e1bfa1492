import { API_ROUTES } from "@/constants/api.routes";
import axiosInstance from "@/lib/axios";
import { IAdminSettingsResponseDto } from "@/types/mutation.types";
import {
  IGetCompanyProfileResponseDto,
  IGetCurrentUserResponse,
  IGetEnumsResponseDto,
  IGetJobSeekerProfileResponseDto,
  IGetProfileByIdResponseDto,
  ICreateJobResponseDto,
  IGetRecruiterJobResponseDto,
  IGetRecentJobsResponseDto,
  IGetAllJobsResponseDto,
  IJobSearchParams,
  IJobSeekerSearchParams,
  IGetAllJobSeekersResponseDto,
  IGetSavedJobsResponseDto,
  IGetJobApplicationsResponseDto,
  IGetSavedCandidatesResponseDto,
  IGetJobApplicantsResponseDto,
  IGetAllCompaniesResponseDto,
  IShortlistedApplicantsSearchParams,
  IGetShortlistedApplicantsResponseDto,
  IAllApplicantsSearchParams,
  IGetAllApplicantsResponseDto,
  IGetConversationsResponseDto,
  IGetConversationResponseDto,
  IGetMessagesResponseDto,
  IRelatedCompaniesParams,
  IGetRelatedCompaniesResponseDto,
  IRelatedCandidatesParams,
  IGetRelatedCandidatesResponseDto,
  IGetPaymentsResponseDto,
  IGetNotificationsResponseDto,
  IGetNotificationsParams,
  IGetUnreadNotificationsCountResponseDto,
  IGetFirebaseTokenResponseDto,
  IGetOpportunitySectionResponseDto,
  IGetTalentSectionResponseDto,
  IGetDiscoverSectionResponseDto,
  IGetCompaniesSectionResponseDto,
  IGetPartnersSectionResponseDto,
  IGetCtaSectionResponseDto,
  IGetAppSectionResponseDto,
  IGetAboutUsPageResponseDto,
  IGetHowItWorksPageResponseDto,
  IGetContactUsPageResponseDto,
  IGetTermsAndConditionsPageResponseDto,
  IGetPrivacyPolicyPageResponseDto,
  IGetSkillsResponseDto,
  IGetSkillsParams,
  IGetCategoriesResponseDto,
  IGetCategoriesParams,
} from "@/types/query.types";

export const getCurrentUser = async (): Promise<IGetCurrentUserResponse> => {
  const { data } = await axiosInstance.get<IGetCurrentUserResponse>(
    API_ROUTES.AUTH.GET_CURRENT_USER
  );
  return data;
};

export const getCategories = async (
  params?: IGetCategoriesParams
): Promise<IGetCategoriesResponseDto> => {
  const axiosParams: Record<string, string | number> = {};

  if (params) {
    if (params.search) axiosParams.search = params.search;
    if (params.page) axiosParams.page = params.page;
    if (params.limit) axiosParams.limit = params.limit;
  }

  const { data } = await axiosInstance.get<IGetCategoriesResponseDto>(
    API_ROUTES.CATEGORY.GET_CATEGORIES,
    {
      params: axiosParams,
    }
  );
  return data;
};

export const getSkills = async (params?: IGetSkillsParams): Promise<IGetSkillsResponseDto> => {
  const axiosParams: Record<string, string | number> = {};

  if (params) {
    if (params.search) axiosParams.search = params.search;
    if (params.page) axiosParams.page = params.page;
    if (params.limit) axiosParams.limit = params.limit;
  }

  const { data } = await axiosInstance.get<IGetSkillsResponseDto>(API_ROUTES.SKILL.GET_SKILLS, {
    params: axiosParams,
  });
  return data;
};

export const getContactUsPage = async (): Promise<IGetContactUsPageResponseDto> => {
  const { data } = await axiosInstance.get<IGetContactUsPageResponseDto>(
    API_ROUTES.CMS.CONTACT_US_PAGE
  );
  return data;
};

export const getTermsAndConditionsPage =
  async (): Promise<IGetTermsAndConditionsPageResponseDto> => {
    const { data } = await axiosInstance.get<IGetTermsAndConditionsPageResponseDto>(
      API_ROUTES.CMS.TERMS_AND_CONDITIONS_PAGE
    );
    return data;
  };

export const getPrivacyPolicyPage = async (): Promise<IGetPrivacyPolicyPageResponseDto> => {
  const { data } = await axiosInstance.get<IGetPrivacyPolicyPageResponseDto>(
    API_ROUTES.CMS.PRIVACY_POLICY_PAGE
  );
  return data;
};

export const getAboutUsPage = async (): Promise<IGetAboutUsPageResponseDto> => {
  const { data } = await axiosInstance.get<IGetAboutUsPageResponseDto>(
    API_ROUTES.CMS.ABOUT_US_PAGE
  );
  return data;
};

export const getHowItWorksPage = async (): Promise<IGetHowItWorksPageResponseDto> => {
  const { data } = await axiosInstance.get<IGetHowItWorksPageResponseDto>(
    API_ROUTES.CMS.HOW_IT_WORKS_PAGE
  );
  return data;
};

export const getCtaSection = async (): Promise<IGetCtaSectionResponseDto> => {
  const { data } = await axiosInstance.get<IGetCtaSectionResponseDto>(
    API_ROUTES.CMS.HOME_CTA_SECTION
  );
  return data;
};

export const getAppSection = async (): Promise<IGetAppSectionResponseDto> => {
  const { data } = await axiosInstance.get<IGetAppSectionResponseDto>(
    API_ROUTES.CMS.HOME_APP_SECTION
  );
  return data;
};

export const getOpportunitySection = async (): Promise<IGetOpportunitySectionResponseDto> => {
  const { data } = await axiosInstance.get<IGetOpportunitySectionResponseDto>(
    API_ROUTES.CMS.HOME_OPPORTUNITY_SECTION
  );
  return data;
};

export const getTalentSection = async (): Promise<IGetTalentSectionResponseDto> => {
  const { data } = await axiosInstance.get<IGetTalentSectionResponseDto>(
    API_ROUTES.CMS.HOME_TALENT_SECTION
  );
  return data;
};

export const getDiscoverSection = async (): Promise<IGetDiscoverSectionResponseDto> => {
  const { data } = await axiosInstance.get<IGetDiscoverSectionResponseDto>(
    API_ROUTES.CMS.HOME_DISCOVER_SECTION
  );
  return data;
};

export const getCompaniesSection = async (): Promise<IGetCompaniesSectionResponseDto> => {
  const { data } = await axiosInstance.get<IGetCompaniesSectionResponseDto>(
    API_ROUTES.CMS.HOME_COMPANIES_SECTION
  );
  return data;
};

export const getPartnersSection = async (): Promise<IGetPartnersSectionResponseDto> => {
  const { data } = await axiosInstance.get<IGetPartnersSectionResponseDto>(
    API_ROUTES.CMS.HOME_PARTNERS_SECTION
  );
  return data;
};

export const getFirebaseTokens = async (): Promise<IGetFirebaseTokenResponseDto> => {
  const { data } = await axiosInstance.get<IGetFirebaseTokenResponseDto>(
    API_ROUTES.AUTH.FIREBASE_TOKEN
  );
  return data;
};

export const getUnreadNotificationsCount =
  async (): Promise<IGetUnreadNotificationsCountResponseDto> => {
    const { data } = await axiosInstance.get<IGetUnreadNotificationsCountResponseDto>(
      API_ROUTES.NOTIFICATIONS.UNREAD_COUNT
    );
    return data;
  };

export const getNotifications = async (
  params?: IGetNotificationsParams
): Promise<IGetNotificationsResponseDto> => {
  const axiosParams: Record<string, string | number | boolean> = {};

  if (params) {
    if (params.page) axiosParams.page = params.page;
    if (params.limit) axiosParams.limit = params.limit;
    if (params.unreadOnly !== undefined) axiosParams.unreadOnly = params.unreadOnly;
  }

  const { data } = await axiosInstance.get<IGetNotificationsResponseDto>(
    API_ROUTES.NOTIFICATIONS.BASE,
    {
      params: axiosParams,
    }
  );
  return data;
};

export const getPayments = async (params?: {
  page?: number | string;
  limit?: number | string;
}): Promise<IGetPaymentsResponseDto> => {
  const axiosParams: Record<string, string | number> = {};

  if (params) {
    if (params.page) axiosParams.page = params.page;
    if (params.limit) axiosParams.limit = params.limit;
  }

  const { data } = await axiosInstance.get<IGetPaymentsResponseDto>(
    API_ROUTES.PAYMENT.GET_PAYMENTS,
    {
      params: axiosParams,
    }
  );
  return data;
};

export const getAdminSettings = async (): Promise<IAdminSettingsResponseDto> => {
  const { data: response } = await axiosInstance.get<IAdminSettingsResponseDto>(
    API_ROUTES.ADMIN_SETTINGS.BASE
  );
  return response;
};

export const getJobSeekerProfile = async (): Promise<IGetJobSeekerProfileResponseDto> => {
  const { data } = await axiosInstance.get<IGetJobSeekerProfileResponseDto>(
    API_ROUTES.PROFILE.JOBSEEKER
  );
  return data;
};
export const getCompanyProfile = async (): Promise<IGetCompanyProfileResponseDto> => {
  const { data } = await axiosInstance.get<IGetCompanyProfileResponseDto>(
    API_ROUTES.PROFILE.COMPANY_PROFILE
  );
  return data;
};
export const getAllEnums = async (): Promise<IGetEnumsResponseDto> => {
  const { data } = await axiosInstance.get(API_ROUTES.DEV.ENUMS);
  return data;
};
export const getJobById = async (id: string): Promise<ICreateJobResponseDto> => {
  const route = API_ROUTES.JOBS.GET_JOB(id); // Use the dynamic route function
  const { data } = await axiosInstance.get<ICreateJobResponseDto>(route);
  return data;
};
export const getProfileById = async (id: string): Promise<IGetProfileByIdResponseDto> => {
  const route = API_ROUTES.PROFILE.GET_PROFILE_BY_ID(id); // Use the dynamic route function
  const { data } = await axiosInstance.get<IGetProfileByIdResponseDto>(route);
  return data;
};

export const getCompanyProfileById = async (id: string): Promise<IGetCompanyProfileResponseDto> => {
  const route = API_ROUTES.PROFILE.GET_COMPANY_PROFILE_BY_ID(id); // Use the dynamic route function
  const { data } = await axiosInstance.get<IGetCompanyProfileResponseDto>(route);
  return data;
};
export const getRecruitersJobs = async (params?: {
  page?: number;
  limit?: number;
}): Promise<IGetRecruiterJobResponseDto> => {
  const axiosParams: Record<string, string | number> = {};

  if (params) {
    if (params.page) axiosParams.page = params.page;
    if (params.limit) axiosParams.limit = params.limit;
  }

  const { data } = await axiosInstance.get<IGetRecruiterJobResponseDto>(
    API_ROUTES.JOBS.GET_RECRUITERS_JOBS,
    {
      params: axiosParams,
    }
  );
  return data;
};

export const getRecentJobs = async (params?: {
  page?: number;
  limit?: number;
}): Promise<IGetRecentJobsResponseDto> => {
  const axiosParams: Record<string, string | number> = {};

  if (params) {
    if (params.page) axiosParams.page = params.page;
    if (params.limit) axiosParams.limit = params.limit;
  }

  const { data } = await axiosInstance.get<IGetRecentJobsResponseDto>(
    API_ROUTES.JOBS.GET_RECENT_JOBS,
    {
      params: axiosParams,
    }
  );
  return data;
};

export const getAllJobs = async (params?: IJobSearchParams): Promise<IGetAllJobsResponseDto> => {
  // Create a new object for axios params instead of URLSearchParams
  const axiosParams: Record<string, string | number | [number, number]> = {};

  if (params) {
    if (params.search) axiosParams.search = params.search;
    if (params.page) axiosParams.page = params.page;
    if (params.limit) axiosParams.limit = params.limit;

    // Send coordinates as an array directly
    if (params.coordinates && Array.isArray(params.coordinates)) {
      axiosParams.coordinates = params.coordinates;
    }

    if (params.maxDistance) axiosParams.maxDistance = params.maxDistance;
    if (params.jobType) axiosParams.jobType = params.jobType;
    if (params.experienceLevel) axiosParams.experienceLevel = params.experienceLevel;
    if (params.qualification) axiosParams.qualification = params.qualification;
    if (params.careerLevel) axiosParams.careerLevel = params.careerLevel;
    if (params.salaryType) axiosParams.salaryType = params.salaryType;
  }

  // Use axios params option to send the parameters
  const { data } = await axiosInstance.get<IGetAllJobsResponseDto>(
    API_ROUTES.JOBS_APPLICATION.ALL_JOBS,
    {
      params: axiosParams,
      // This ensures arrays are serialized correctly
      paramsSerializer: {
        indexes: null, // This will serialize arrays as coordinates[]=[lng,lat] instead of coordinates[0]=lng&coordinates[1]=lat
      },
    }
  );
  return data;
};

export const getAllJobSeekers = async (
  params?: IJobSeekerSearchParams
): Promise<IGetAllJobSeekersResponseDto> => {
  // Create a new object for axios params instead of URLSearchParams
  const axiosParams: Record<string, string | number | [number, number]> = {};

  if (params) {
    if (params.search) axiosParams.search = params.search;
    if (params.page) axiosParams.page = params.page;
    if (params.limit) axiosParams.limit = params.limit;

    // Send coordinates as an array directly
    if (params.coordinates && Array.isArray(params.coordinates)) {
      axiosParams.coordinates = params.coordinates;
    }

    if (params.maxDistance) axiosParams.maxDistance = params.maxDistance;
    if (params.jobType) axiosParams.jobType = params.jobType;
    if (params.jobCategory) axiosParams.jobCategory = params.jobCategory;
  }

  // Use axios params option to send the parameters
  const { data } = await axiosInstance.get<IGetAllJobSeekersResponseDto>(
    API_ROUTES.JOBS_APPLICATION.ALL_JOBSEEKERS,
    {
      params: axiosParams,
      // This ensures arrays are serialized correctly
      paramsSerializer: {
        indexes: null, // This will serialize arrays as coordinates[]=[lng,lat] instead of coordinates[0]=lng&coordinates[1]=lat
      },
    }
  );
  return data;
};

// Get all saved jobs for the current user
export const getSavedJobs = async (params?: {
  page?: number;
  limit?: number;
}): Promise<IGetSavedJobsResponseDto> => {
  const axiosParams: Record<string, string | number> = {};

  if (params) {
    if (params.page) axiosParams.page = params.page;
    if (params.limit) axiosParams.limit = params.limit;
  }

  const { data } = await axiosInstance.get<IGetSavedJobsResponseDto>(
    API_ROUTES.SAVED_JOBS.GET_SAVED_JOBS,
    {
      params: axiosParams,
    }
  );
  return data;
};

// Get all job applications for the current user
export const getMyJobApplications = async (params?: {
  page?: number;
  limit?: number;
}): Promise<IGetJobApplicationsResponseDto> => {
  const axiosParams: Record<string, string | number> = {};

  if (params) {
    if (params.page) axiosParams.page = params.page;
    if (params.limit) axiosParams.limit = params.limit;
  }

  const { data } = await axiosInstance.get<IGetJobApplicationsResponseDto>(
    API_ROUTES.JOBS_APPLICATION.GET_MY_JOB_APPLICATIONS,
    {
      params: axiosParams,
    }
  );
  return data;
};

// Get shortlisted job applications for the current user
export const getMyShortlistedApplications = async (params?: {
  page?: number;
  limit?: number;
}): Promise<IGetJobApplicationsResponseDto> => {
  const axiosParams: Record<string, string | number> = {};

  if (params) {
    if (params.page) axiosParams.page = params.page;
    if (params.limit) axiosParams.limit = params.limit;
  }

  const { data } = await axiosInstance.get<IGetJobApplicationsResponseDto>(
    API_ROUTES.JOBS_APPLICATION.GET_MY_SHORTLISTED_APPLICATIONS,
    {
      params: axiosParams,
    }
  );
  return data;
};

// Get saved candidates for the current recruiter
export const getSavedCandidates = async (params?: {
  page?: number;
  limit?: number;
}): Promise<IGetSavedCandidatesResponseDto> => {
  const axiosParams: Record<string, string | number> = {};

  if (params) {
    if (params.page) axiosParams.page = params.page;
    if (params.limit) axiosParams.limit = params.limit;
  }

  const { data } = await axiosInstance.get<IGetSavedCandidatesResponseDto>(
    API_ROUTES.SAVED_CANDIDATES.GET_SAVED_CANDIDATES,
    {
      params: axiosParams,
    }
  );
  return data;
};

export const getJobApplicants = async (jobId: string): Promise<IGetJobApplicantsResponseDto> => {
  const route = API_ROUTES.JOBS_APPLICATION.GET_JOB_APPLICANTS(jobId);
  const { data } = await axiosInstance.get<IGetJobApplicantsResponseDto>(route);
  return data;
};

// Get all companies with search and location filtering
export const getAllCompanies = async (params?: {
  page?: number;
  limit?: number;
  search?: string;
  coordinates?: [number, number];
  maxDistance?: number;
}): Promise<IGetAllCompaniesResponseDto> => {
  const axiosParams: Record<string, string | number | [number, number]> = {};

  if (params) {
    if (params.page) axiosParams.page = params.page;
    if (params.limit) axiosParams.limit = params.limit;
    if (params.search) axiosParams.search = params.search;
    if (params.coordinates) axiosParams.coordinates = params.coordinates;
    if (params.maxDistance) axiosParams.maxDistance = params.maxDistance;
  }

  const { data } = await axiosInstance.get<IGetAllCompaniesResponseDto>(
    API_ROUTES.JOBS_APPLICATION.ALL_COMPANIES,
    {
      params: axiosParams,
      // This ensures arrays are serialized correctly
      paramsSerializer: {
        indexes: null, // This will serialize arrays as coordinates[]=[lng,lat] instead of coordinates[0]=lng&coordinates[1]=lat
      },
    }
  );
  return data;
};

// Get all shortlisted applicants for the current recruiter
export const getShortlistedApplicants = async (
  params?: IShortlistedApplicantsSearchParams
): Promise<IGetShortlistedApplicantsResponseDto> => {
  const axiosParams: Record<string, string | number | [number, number]> = {};

  if (params) {
    if (params.search) axiosParams.search = params.search;
    if (params.page) axiosParams.page = params.page;
    if (params.limit) axiosParams.limit = params.limit;
    if (params.job_category) axiosParams.job_category = params.job_category;

    // Send coordinates as an array directly
    if (params.coordinates && Array.isArray(params.coordinates)) {
      axiosParams.coordinates = params.coordinates;
    }

    if (params.maxDistance) axiosParams.maxDistance = params.maxDistance;
  }

  const { data } = await axiosInstance.get<IGetShortlistedApplicantsResponseDto>(
    API_ROUTES.JOBS_APPLICATION.GET_ALL_SHORTLISTED_APPLICANTS,
    {
      params: axiosParams,
      // This ensures arrays are serialized correctly
      paramsSerializer: {
        indexes: null, // This will serialize arrays as coordinates[]=[lng,lat] instead of coordinates[0]=lng&coordinates[1]=lat
      },
    }
  );
  return data;
};

// Get all applicants for the current recruiter
export const getAllApplicants = async (
  params?: IAllApplicantsSearchParams
): Promise<IGetAllApplicantsResponseDto> => {
  const axiosParams: Record<string, string | number | [number, number]> = {};

  if (params) {
    if (params.search) axiosParams.search = params.search;
    if (params.page) axiosParams.page = params.page;
    if (params.limit) axiosParams.limit = params.limit;
    if (params.job_category) axiosParams.job_category = params.job_category;

    // Send coordinates as an array directly
    if (params.coordinates && Array.isArray(params.coordinates)) {
      axiosParams.coordinates = params.coordinates;
    }

    if (params.maxDistance) axiosParams.maxDistance = params.maxDistance;
  }

  const { data } = await axiosInstance.get<IGetAllApplicantsResponseDto>(
    API_ROUTES.JOBS_APPLICATION.GET_ALL_APPLICANTS,
    {
      params: axiosParams,
      // This ensures arrays are serialized correctly
      paramsSerializer: {
        indexes: null, // This will serialize arrays as coordinates[]=[lng,lat] instead of coordinates[0]=lng&coordinates[1]=lat
      },
    }
  );
  return data;
};

// Get all conversations
export const getConversations = async (params?: {
  page?: number;
  limit?: number;
}): Promise<IGetConversationsResponseDto> => {
  const axiosParams: Record<string, string | number> = {};

  if (params) {
    if (params.page) axiosParams.page = params.page;
    if (params.limit) axiosParams.limit = params.limit;
  }

  const { data } = await axiosInstance.get<IGetConversationsResponseDto>(
    API_ROUTES.CONVERSATIONS.BASE,
    {
      params: axiosParams,
    }
  );
  return data;
};

// Get a specific conversation by ID
export const getConversationById = async (
  conversationId: string
): Promise<IGetConversationResponseDto> => {
  const route = API_ROUTES.CONVERSATIONS.GET_CONVERSATION(conversationId);
  const { data } = await axiosInstance.get<IGetConversationResponseDto>(route);
  return data;
};

// Get messages for a conversation with pagination
export const getMessages = async (params: {
  conversationId: string;
  page?: number;
  limit?: number;
}): Promise<IGetMessagesResponseDto> => {
  const { conversationId, ...paginationParams } = params;
  const axiosParams: Record<string, string | number> = {};

  if (paginationParams.page) axiosParams.page = paginationParams.page;
  if (paginationParams.limit) axiosParams.limit = paginationParams.limit;

  // Use the path parameter for conversationId as per API docs
  const route = API_ROUTES.MESSAGES.GET_MESSAGES(conversationId);
  const { data } = await axiosInstance.get<IGetMessagesResponseDto>(route, {
    params: axiosParams,
  });
  return data;
};

// Get related companies for a specific company profile
export const getRelatedCompanies = async (
  companyProfileId: string,
  params: IRelatedCompaniesParams = {}
): Promise<IGetRelatedCompaniesResponseDto> => {
  const route = API_ROUTES.RELATED.COMPANIES(companyProfileId);
  const axiosParams: Record<string, string | number> = {};

  // Add optional parameters if they exist
  if (params.page) axiosParams.page = params.page;
  if (params.limit) axiosParams.limit = params.limit;
  if (params.includeLocation) axiosParams.includeLocation = params.includeLocation;
  if (params.includeCompanySize) axiosParams.includeCompanySize = params.includeCompanySize;
  if (params.includeDescription) axiosParams.includeDescription = params.includeDescription;
  if (params.maxDistance) axiosParams.maxDistance = params.maxDistance;

  const { data } = await axiosInstance.get<IGetRelatedCompaniesResponseDto>(route, {
    params: axiosParams,
  });
  return data;
};

// Get related candidates for a specific job seeker profile
export const getRelatedCandidates = async (
  jobSeekerProfileId: string,
  params: IRelatedCandidatesParams = {}
): Promise<IGetRelatedCandidatesResponseDto> => {
  const route = API_ROUTES.RELATED.CANDIDATES(jobSeekerProfileId);
  const axiosParams: Record<string, string | number | boolean> = {};

  // Add optional parameters if they exist
  if (params.page) axiosParams.page = params.page;
  if (params.limit) axiosParams.limit = params.limit;
  if (params.includeSkills) axiosParams.includeSkills = params.includeSkills;
  if (params.includeJobType) axiosParams.includeJobType = params.includeJobType;
  if (params.includeJobCategory) axiosParams.includeJobCategory = params.includeJobCategory;
  if (params.includeSalaryRange) axiosParams.includeSalaryRange = params.includeSalaryRange;
  if (params.includeLocation) axiosParams.includeLocation = params.includeLocation;
  if (params.maxDistance) axiosParams.maxDistance = params.maxDistance;

  const { data } = await axiosInstance.get<IGetRelatedCandidatesResponseDto>(route, {
    params: axiosParams,
  });
  return data;
};
