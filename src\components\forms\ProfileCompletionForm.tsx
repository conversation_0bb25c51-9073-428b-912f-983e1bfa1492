// "use client";

// import { useState } from "react";

// import Sidebar from "../Sections/Sidebar";
// import EducationalDetails from "./EducationalDetails";
// import TellUsAboutYourself from "./TellUsAboutYourself";
// import ProfessionalExperience from "./ProfessionalExperience";
// import ProfessionalSkills from "./ProfessionalSkills";
// import AchievementsForms from "./AchievementsForms";
// import JobPreferences from "./JobPreferences";
// import CompleteYourCV from "./CompleteYourCV";
// import Certifications from "./CertificationsForms";
// import { useRouter } from "next/navigation";

// const steps = [
//   "Tell us about yourself",
//   "Complete Your CV",
//   "Educational Details",
//   "Certifications",
//   "Professional Experience",
//   "Professional Skills",
//   "Achievements",
//   "Job Preferences",
// ];

// export default function ProfileCompletionForm() {
//   const [currentStep, setCurrentStep] = useState(0);
//   const router = useRouter();

//   const handleNextClick = () => {
//     if (currentStep === steps.length - 1) {
//       router.push("/profile-completed");
//     } else {
//       setCurrentStep(Math.min(steps.length - 1, currentStep + 1));
//     }
//   };

//   return (
//     <div className="xl:p-6">
//       <div className="lg:flex gap-6">
//         {/* Step Navigation */}
//         <div className="xl:w-1/4 lg:w-[30%] mb-6 lg:mb-0">
//           <Sidebar steps={steps} currentStep={currentStep} />
//         </div>

//         {/* Form Content */}
//         <div className="xl:w-3/4 lg:w-[70%]">
//           <div className="bg-white lg:p-6 rounded-lg ">
//             {currentStep === 0 && <TellUsAboutYourself />}
//             {currentStep === 1 && <CompleteYourCV />}
//             {currentStep === 2 && <EducationalDetails />}
//             {currentStep === 3 && <Certifications />}
//             {currentStep === 4 && <ProfessionalExperience />}
//             {currentStep === 5 && <ProfessionalSkills />}
//             {currentStep === 6 && <AchievementsForms />}
//             {currentStep === 7 && <JobPreferences />}
//             <div className={`flex flex-wrap gap-y-3 gap-x-5 mt-10`}>
//               <button
//                 onClick={() => setCurrentStep(Math.max(0, currentStep - 1))}
//                 disabled={currentStep === 0}
//                 className="font-bold py-4 px-10 font-base rounded-full inline-flex items-center justify-center space-x-2 bg-gray-300 text-gray-100"
//               >
//                 Go Back
//               </button>
//               <button
//                 onClick={handleNextClick}
//                 // disabled={currentStep === steps.length - 1}
//                 className="font-bold py-4 px-10 font-base rounded-full inline-flex items-center justify-center space-x-2 bg-orange-100 text-white"
//               >
//                 {currentStep === steps.length - 1
//                   ? "Complete Profile"
//                   : "Next Step"}
//               </button>
//             </div>
//           </div>
//         </div>
//       </div>
//     </div>
//   );
// }
