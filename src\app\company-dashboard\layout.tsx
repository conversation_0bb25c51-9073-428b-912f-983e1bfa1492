import type { <PERSON>ada<PERSON> } from "next";
import type React from "react"; // Import React
import SidebarLayout from "./SidebarLayout";
import Header from "@/components/layout/Header";

export const metadata: Metadata = {
  title: "Authentication",
  description: "Login and register pages",
};

export default function CompanyDashboardLayout({ children }: { children: React.ReactNode }) {
  return (
    <>
      <Header dashboardPages={true} />
      <div className="container mx-auto py-14">
        <div className="lg:flex gap-x-10">
          <SidebarLayout />
          <div className="border border-gray-300 lg:w-[calc(100%-350px)] rounded-[18px] p-7">
            {children}
          </div>
        </div>
      </div>
    </>
  );
}
