import type { Metada<PERSON> } from "next";
import type React from "react"; // Import React
import CompanySettingsLayout from "./CompanySettingsLayout";
import Header from "@/components/layout/Header";

export const metadata: Metadata = {
  title: "Authentication",
  description: "Login and register pages",
};

export default function SettingPagesInnerLayout({ children }: { children: React.ReactNode }) {
  return (
    <>
      <Header dashboardPages={true} />
      <div className="container mx-auto py-14">
        <div className="lg:flex gap-x-10">
          <CompanySettingsLayout />
          <div className="lg:w-[calc(100%-350px)] rounded-[18px]">{children}</div>
        </div>
      </div>
    </>
  );
}
