import React, { ReactNode } from "react";

interface HeroCountCardProps {
  icon: ReactNode;
  title: string;
  text: string;
}

const HeroCountCard: React.FC<HeroCountCardProps> = ({ icon, title, text }) => {
  return (
    <div className="shadow-2xl rounded-xl p-6">
      <div className="flex">
        <div>{icon}</div>
        <div className="ml-4">
          <h2 className="text-2xl font-medium mb-2">{title}</h2>
          <p className="text-gray-100 text-sm">{text}</p>
        </div>
      </div>
    </div>
  );
};

export default HeroCountCard;
