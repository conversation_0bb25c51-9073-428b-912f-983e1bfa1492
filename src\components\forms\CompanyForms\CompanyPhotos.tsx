"use client";

import { useQueryClient } from "@tanstack/react-query";
import { useRouter } from "next/navigation";
import React, { useEffect, useState } from "react";
import { toast } from "sonner";
import UploadPortfolio from "@/components/Cards/UploadPortfolio";
import { useUploadCompanyPhotos, useDeletePhoto } from "@/hooks/useMutation";
import { useGetCompanyProfile } from "@/hooks/useQuery";

interface PhotoItem {
  id: string;
  file?: File;
  previewUrl: string;
  isUploaded?: boolean;
  s3Key?: string;
  uploadedAt?: string;
}

const CompanyPhotos = () => {
  const router = useRouter();
  const queryClient = useQueryClient();
  const { data: companyData, isLoading: isProfileLoading } = useGetCompanyProfile();
  const companyProfile = companyData?.data;

  const [photos, setPhotos] = useState<PhotoItem[]>([]);
  const [isUploading, setIsUploading] = useState(false);
  const { mutateAsync: uploadPhotos } = useUploadCompanyPhotos({
    onSuccess() {
      // queryClient.invalidateQueries({ queryKey: ["get-company-profile"] });

      router.push("/company-profile-completion?stepId=perks-benefits");
    },
  });
  const { mutate: deletePhoto } = useDeletePhoto();

  // Initialize with existing photos from profile
  useEffect(() => {
    if (companyProfile?.companyPhotos) {
      setPhotos(
        companyProfile.companyPhotos.map((photo) => ({
          id: photo.s3Key,
          previewUrl: photo.url,
          isUploaded: true,
          s3Key: photo.s3Key,
          uploadedAt: photo.uploadedAt,
        }))
      );
    }
  }, [companyProfile]);

  const handleFilesSelected = (files: FileList) => {
    const newPhotos = Array.from(files).map((file) => ({
      id: URL.createObjectURL(file), // Use object URL as temporary ID
      file,
      previewUrl: URL.createObjectURL(file),
      isUploaded: false,
    }));

    setPhotos((prev) => [...prev, ...newPhotos]);
  };

  const handleRemovePhoto = (id: string) => {
    setPhotos((prev) => prev.filter((photo) => photo.id !== id));
  };

  const handleUploadAll = async () => {
    if (photos.length === 0) return;

    const photosToUpload = photos.filter((photo) => !photo.isUploaded);
    if (photosToUpload.length === 0) {
      handleNext();
      return;
    }

    setIsUploading(true);
    try {
      const formData = new FormData();
      photosToUpload.forEach((photo) => {
        if (photo.file) {
          formData.append("photos", photo.file);
        }
      });

      await uploadPhotos(formData as unknown as File[]);
      toast.success("Photos uploaded successfully");
      queryClient.invalidateQueries({ queryKey: ["get-company-profile"] });
    } catch (error) {
      toast.error((error as Error).message || "Failed to upload photos");
    } finally {
      setIsUploading(false);
    }
  };

  const handleDeleteUploadedPhoto = (id: string, s3Key: string) => {
    deletePhoto(
      { s3Key },
      {
        onSuccess: () => {
          handleRemovePhoto(id);
          toast.success("Photo deleted successfully");
          queryClient.invalidateQueries({ queryKey: ["get-company-profile"] });
        },
        onError: (error) => {
          toast.error(error.response?.data?.message || "Failed to delete photo");
        },
      }
    );
  };

  const handleNext = () => {
    handleUploadAll();
  };

  if (isProfileLoading) {
    return (
      <div className="text-center py-8">
        <div className="animate-spin rounded-full h-10 w-10 border-b-2 border-gray-900 mx-auto"></div>
        <p className="text-gray-500 mt-4">Loading company photos...</p>
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto px-4">
      <h2 className="text-blue-600 text-2xl font-bold">Company</h2>
      <h3 className="text-3xl font-semibold text-gray-800 mt-2 mb-6">Company Photos</h3>

      <div className="mb-8">
        <p className="text-lg text-gray-700 mb-4">Upload Photos (Max 8)</p>
        <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4 mb-6">
          {photos.map((photo) => (
            <div key={photo.id} className="relative">
              <UploadPortfolio
                image={photo.previewUrl}
                onDelete={() =>
                  photo.isUploaded && photo.s3Key
                    ? handleDeleteUploadedPhoto(photo.id, photo.s3Key)
                    : handleRemovePhoto(photo.id)
                }
              />
              {!photo.isUploaded && (
                <div className="absolute top-2 left-2 bg-yellow-500 text-white text-xs px-2 py-1 rounded">
                  Pending
                </div>
              )}
            </div>
          ))}

          {photos.length < 8 && <UploadPortfolio onFilesSelected={handleFilesSelected} multiple />}
        </div>

        {/* {photos.some((photo) => !photo.isUploaded) && (
          <button
            onClick={}
            disabled={isUploading}
            className="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-6 rounded-lg disabled:opacity-70"
          >
            {isUploading ? "Uploading..." : "Upload All Photos"}
          </button>
        )} */}
      </div>

      <div className="flex gap-4 mt-10">
        <button
          onClick={() => router.back()}
          className="font-medium py-3 px-8 rounded-full bg-gray-200 hover:bg-gray-300 text-gray-700"
        >
          Go Back
        </button>
        <button
          onClick={handleNext}
          disabled={isUploading}
          className="font-medium py-3 px-8 rounded-full bg-orange-500 hover:bg-orange-600 text-white disabled:opacity-70"
        >
          {isUploading ? "Processing..." : "Next Step"}
        </button>
      </div>
    </div>
  );
};

export default CompanyPhotos;
