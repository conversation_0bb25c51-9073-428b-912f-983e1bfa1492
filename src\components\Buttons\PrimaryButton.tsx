import Link from "next/link";
import React from "react";

interface PrimaryButtonProps {
  link: string;
  text: string;
  icon?: React.ReactNode;
  variant?: "primary" | "secondary";
  props?: React.ComponentPropsWithoutRef<"a">;
}

const PrimaryButton: React.FC<PrimaryButtonProps> = ({
  link,
  text,
  icon,
  props,
  variant = "primary",
}) => {
  const baseClasses = "py-3 px-8 rounded-full inline-flex items-center justify-center space-x-2";
  const primaryClasses = "bg-orange-100 text-white";
  const secondaryClasses = "bg-[#000] text-white";

  const classes = variant === "primary" ? primaryClasses : secondaryClasses;

  return (
    <Link href={link} {...props} className={`${baseClasses} ${classes}`}>
      <span className="text">{text}</span>
      {icon && <span className="icon">{icon}</span>}
    </Link>
  );
};

export default PrimaryButton;
