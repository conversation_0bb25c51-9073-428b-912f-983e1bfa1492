"use client";

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import type { IPagination } from "@/types/query.types";

interface ResultsPerPageProps {
  pagination: IPagination;
  onLimitChange: (limit: number) => void;
}

export function ResultsPerPage({ pagination, onLimitChange }: ResultsPerPageProps) {
  const { totalRecords, skip, limit } = pagination;

  // Calculate the range of results being displayed
  const startItem = skip + 1;
  const endItem = Math.min(skip + limit, totalRecords);

  return (
    <div className="flex items-center justify-between mb-4">
      <div className="text-sm text-gray-500">
        Showing {startItem} to {endItem} of {totalRecords} results
      </div>
      <div className="flex items-center gap-2">
        <span className="text-sm text-gray-500">Results per page:</span>
        <Select
          value={limit.toString()}
          onValueChange={(value) => onLimitChange(Number.parseInt(value))}
        >
          <SelectTrigger className="w-[80px] h-8">
            <SelectValue placeholder={limit.toString()} />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="10">10</SelectItem>
            <SelectItem value="20">20</SelectItem>
            <SelectItem value="50">50</SelectItem>
          </SelectContent>
        </Select>
      </div>
    </div>
  );
}
