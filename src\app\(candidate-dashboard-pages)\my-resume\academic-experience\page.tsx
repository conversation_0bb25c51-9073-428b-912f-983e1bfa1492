"use client";

import { useQueryClient } from "@tanstack/react-query";
import React, { useState } from "react";
import { toast } from "sonner";
import CertificationInfo from "@/components/Cards/CertificationInfo";
import { MortarboardIcon } from "@/components/Icons";
import CertificationsDiplomaForm from "@/components/forms/CertificationsDiplomaForm";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";

// import CertificationInfo from "../Cards.tsx/CertificationInfo";
// import { MortarboardIcon } from "../Icons";
// import CertificationsDiplomaForm from "./CertificationsDiplomaForm";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { useUpdateJobSeekerProfile } from "@/hooks/useMutation";
import { useGetJobSeekerProfile } from "@/hooks/useQuery";
import { ApiError } from "@/types/common.types";
import { ICertificate } from "@/types/query.types";

export default function AcademicExperience() {
  const [open, setOpen] = useState(false);
  const [editingCertificate, setEditingCertificate] = useState<ICertificate | null>(null);
  const queryClient = useQueryClient();
  const { data: profileData } = useGetJobSeekerProfile();
  const { mutate: updateProfile } = useUpdateJobSeekerProfile({
    onSuccess: () => {
      toast.success("Profile updated successfully");
      queryClient.invalidateQueries({ queryKey: ["get-jobseeker-profile"] });
    },
    onError: (error) => {
      console.log({ error });
      toast.error(error.response?.data?.message || "Failed to update profile");
    },
  });

  const handleClose = () => {
    setOpen(false);
    setEditingCertificate(null);
  };

  const handleSuccess = async (formData: ICertificate[]) => {
    try {
      const existingCertificates = profileData?.data.certificates || [];
      const updatedCertificates = editingCertificate
        ? existingCertificates.map((cert) =>
            cert._id === editingCertificate._id ? formData[0] : cert
          )
        : [...existingCertificates, ...formData];

      updateProfile({
        certificates: updatedCertificates as ICertificate[],
      });
      handleClose();
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to save certification details";
      toast.error(errorMessage);
    }
  };

  const handleError = (error: unknown) => {
    const errorMessage =
      error instanceof Error ? error.message : "Failed to save certification details";
    toast.error(errorMessage);
  };

  const handleEdit = (certificate: ICertificate) => {
    setEditingCertificate(certificate);
    setOpen(true);
  };

  const handleDelete = async (certificateId: string) => {
    try {
      const existingCertificates = profileData?.data.certificates || [];
      const updatedCertificates = existingCertificates.filter((cert) => cert._id !== certificateId);

      updateProfile({
        certificates: updatedCertificates,
      });
      toast.success("Certificate deleted successfully");
    } catch (error) {
      toast.error((error as ApiError).response?.data.message || "Failed to delete certificate");
    }
  };

  const certificates = profileData?.data.certificates || [];
  return (
    <>
      <Breadcrumb className="mb-8">
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink href="/dashboard/applied-jobs">Dashboard</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>My Resume</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>
      <div>
        <div className="sm:flex items-center justify-between mb-10">
          <div className="mb-4 sm:mb-0">
            <h2 className="text-blue-200 text-2xl font-bold">Certifications</h2>
            <h3 className="text-4xl font-medium text-black-100 mt-3">Certifications & Diploma</h3>
          </div>
          <div>
            <Dialog open={open} onOpenChange={setOpen}>
              <DialogTrigger
                onClick={() => setEditingCertificate(null)}
                className="border-orange-100 border rounded-full px-3 py-3 flex items-center gap-x-3 font-medium text-orange-100"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="30"
                  height="30"
                  fill="none"
                  viewBox="0 0 30 30"
                >
                  <path
                    stroke="#EC761E"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M15 29c7.732 0 14-6.268 14-14S22.732 1 15 1 1 7.268 1 15s6.268 14 14 14M10.333 15h9.334M15 10.333v9.333"
                  ></path>
                </svg>
                Add Certification
              </DialogTrigger>
              <DialogContent className="md:max-w-[920px] max-h-[80vh] overflow-y-auto">
                <DialogHeader className="text-left">
                  <DialogTitle className="text-2xl text-orange-100 font-bold mb-5">
                    Certification / Diploma Details
                  </DialogTitle>
                  <CertificationsDiplomaForm
                    goBack={handleClose}
                    onSuccess={handleSuccess}
                    onError={handleError}
                    defaultValues={editingCertificate ? [editingCertificate] : []}
                  />
                </DialogHeader>
              </DialogContent>
            </Dialog>
          </div>
        </div>
        <div className="space-y-4">
          {certificates.map((certificate: ICertificate, index: number) => (
            <CertificationInfo
              key={certificate._id || index}
              icon={<MortarboardIcon />}
              date={`${new Date(certificate.startDate).toLocaleDateString()} - ${new Date(
                certificate.endDate
              ).toLocaleDateString()}`}
              text={certificate.certificate}
              title={certificate.instituteName}
              onEdit={() => handleEdit(certificate)}
              onDelete={() => handleDelete(certificate._id)}
            />
          ))}
          {certificates.length === 0 && (
            <div className="text-center text-gray-500 py-8">
              No certifications added yet. Click &quot;Add Certification&quot; to get started.
            </div>
          )}
        </div>
      </div>
    </>
  );
}
