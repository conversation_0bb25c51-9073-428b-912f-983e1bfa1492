"use client";
import React, { useState, useEffect } from "react";
// import CompanyJobCard from "@/components/Cards/CompanyJobCard";
import DeleteJobModal from "./DeleteJobModal";
import CompanyJobCardtwo from "@/components/Cards/CompanyJobCardtwo";
import Pagination from "@/components/Pagination/Pagination";
import { DEFAULT_IMAGE } from "@/constants/app.constants";
import { useDeleteJob, useUpdateJobStatus } from "@/hooks/useMutation";
import { useGetRecruiterJobs } from "@/hooks/useQuery";
import { formatDate } from "@/lib/utils";

const ActiveJobsList: React.FC = () => {
  const [page, setPage] = useState(1);
  const [limit] = useState(10);

  const { data, isLoading, isError, refetch } = useGetRecruiterJobs({ page, limit });
  const { mutate: deleteJob } = useDeleteJob();
  const { mutate: updateJobStatus, isPending } = useUpdateJobStatus();
  const [jobToDelete, setJobToDelete] = useState<string | null>(null);

  const jobs = data?.data?.jobs.filter((job) => job.isJobActive) || [];
  const pagination = data?.data?.pagination;

  useEffect(() => {
    refetch();
  }, [page, refetch]);

  const handlePageChange = (newPage: number) => {
    setPage(newPage);
  };

  const toggleJobStatus = (jobId: string, isJobActive: boolean) => {
    updateJobStatus({ jobId, isJobActive });
  };

  const handleDelete = (jobId: string) => {
    setJobToDelete(jobId); // Open confirmation modal
  };

  const confirmDelete = () => {
    if (jobToDelete) {
      deleteJob(jobToDelete, {
        onSuccess: () => {
          setJobToDelete(null); // Close modal after successful deletion
        },
      });
    }
  };

  const cancelDelete = () => {
    setJobToDelete(null); // Close modal without deleting
  };

  if (isLoading) {
    return <div>Loading...</div>;
  }

  if (isError) {
    return <div>Failed to load jobs.</div>;
  }

  if (!jobs.length) {
    return <div>No jobs found</div>;
  }

  return (
    <>
      {jobs.map((job) => (
        <CompanyJobCardtwo
          key={job._id}
          imageUrl={DEFAULT_IMAGE}
          jobTitle={job.jobTitle}
          companyName={job.recruiterProfile?.companyProfile?.companyName || "Company"}
          cityName={job.location?.city || ""}
          salaryRange={`$${job.salaryRangeStart} - $${job.salaryRangeEnd}`}
          // viewJob={job.isJobActive}
          jobType={job.jobType}
          jobClosed={!job.isJobActive}
          handleDelete={() => handleDelete(job._id)}
          toggleJobStatus={() => toggleJobStatus(job._id, !job.isJobActive)}
          editJobLink={`/company-dashboard/post-job/${job._id}`}
          viewJobLink={`/company-dashboard/all-jobs/${job._id}`}
          category={job.jobCategory}
          dateCreated={formatDate(job.createdAt)}
          expireDate={formatDate(job.applicationDeadline)}
          shortlistedApplicants={job.shortlistedApplicantsCount || 0}
          totalApplicants={job.applicantsCount || 0}
          isLoading={isPending} // Pass the loading state to the card
        />
      ))}

      {pagination && pagination.pages > 1 && (
        <div className="flex justify-center mt-8">
          <Pagination
            currentPage={page}
            totalPages={pagination.pages}
            onPageChange={handlePageChange}
          />
        </div>
      )}

      {jobToDelete && <DeleteJobModal cancelDelete={cancelDelete} confirmDelete={confirmDelete} />}
    </>
  );
};

export default ActiveJobsList;
