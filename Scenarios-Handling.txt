🧩 Scenarios & Handling

Scenario	Approach
Message Sent	Add to local state immediately with status: 'sending', update to sent/delivered/read from socket events
Read Trigger	On window focus or scroll-to-bottom in open chat, emit read event for visible messages
Deleted Message	Replace message content with "Message deleted" in chat and conversation preview
Attachments	Show file icon/preview with fallback text "Attachment" in last message
Typing Indicator	Set isTyping = true in Zustand keyed by conversation ID, auto-clear with debounce
Typing Stop	Socket emits typing:stop, remove from Zustand
Sync Last Message	Update conversation's lastMessage in both React Query + Zustand store on new/deleted message



✅ Store (useChatStore.ts with Zustand)
import { create } from 'zustand';

type MessageStatus = 'sending' | 'sent' | 'delivered' | 'read';

interface TypingStatus {
  [conversationId: string]: boolean;
}

interface ChatStore {
  activeConversationId: string | null;
  typingStatus: TypingStatus;
  setActiveConversation: (id: string | null) => void;
  setTyping: (id: string, status: boolean) => void;
}

export const useChatStore = create<ChatStore>((set) => ({
  activeConversationId: null,
  typingStatus: {},
  setActiveConversation: (id) => set({ activeConversationId: id }),
  setTyping: (id, status) =>
    set((state) => ({
      typingStatus: { ...state.typingStatus, [id]: status },
    })),
}));


💬 Sending Message (Optimistic Update)

const sendMessage = async (msg: Message) => {
  queryClient.setQueryData(['messages', msg.conversationId], (old: Message[] | undefined) => [
    ...(old || []),
    { ...msg, _id: uuid(), status: 'sending' },
  ]);
  socket.emit('send:message', msg);
};  

Socket Event Handlers (Setup in useEffect)

useEffect(() => {
  socket.on('message:delivered', ({ messageId }) => {
    queryClient.setQueryData(['messages', convId], (msgs) =>
      msgs.map((m) => (m._id === messageId ? { ...m, status: 'delivered' } : m))
    );
  });

  socket.on('message:read', ({ messageId }) => {
    queryClient.setQueryData(['messages', convId], (msgs) =>
      msgs.map((m) => (m._id === messageId ? { ...m, status: 'read' } : m))
    );
  });

  socket.on('message:new', (message) => {
    queryClient.setQueryData(['messages', message.conversationId], (old: Message[] = []) => [
      ...old,
      message,
    ]);
    queryClient.setQueryData(['conversations'], (convs) =>
      convs.map((c) =>
        c._id === message.conversationId ? { ...c, lastMessage: message } : c
      )
    );
  });

  socket.on('message:deleted', ({ conversationId, messageId }) => {
    queryClient.setQueryData(['messages', conversationId], (msgs) =>
      msgs.map((msg) => (msg._id === messageId ? { ...msg, content: 'Message deleted' } : msg))
    );
  });

  socket.on('typing', ({ conversationId }) => {
    useChatStore.getState().setTyping(conversationId, true);
  });

  socket.on('typing:stop', ({ conversationId }) => {
    useChatStore.getState().setTyping(conversationId, false);
  });
}, []);


🧼 Cleanup & Bonus UX
Emit read on scroll-end or conversation focus

Debounce typing emit (500ms inactivity triggers typing:stop)

Scroll to bottom on new message if viewing

Cache-first fetching (React-Query handles retries, stale times)