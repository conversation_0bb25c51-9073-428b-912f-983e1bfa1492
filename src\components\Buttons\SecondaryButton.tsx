import Link from "next/link";
import React from "react";

interface SecondaryButtonProps {
  variant: "bordered" | "borderless";
  children: React.ReactNode;
  href?: string;
}

const SecondaryButton: React.FC<SecondaryButtonProps> = ({
  variant,
  children,
  href,
}) => {
  const baseClasses =
    "rounded-full py-2 px-6 inline-block cursor-pointer transition-colors duration-300";
  const borderedClasses =
    "bg-white text-orange-100 border border-orange-100 hover:bg-orange-100 hover:text-white";
  const borderlessClasses =
    "bg-orange-100 text-white border-none hover:bg-white hover:text-orange-100 hover:shadow-lg";

  return (
    <Link
      href={href || "#"}
      className={`${baseClasses} ${
        variant === "bordered" ? borderedClasses : borderlessClasses
      }`}
    >
      {children}
    </Link>
  );
};

export default SecondaryButton;
