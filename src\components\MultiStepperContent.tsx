"use client";

import { useSearchParams } from "next/navigation";
import { Suspense } from "react";

interface Step {
  id: string;
  title: string;
  component: React.ComponentType<unknown>;
}

interface MultiStepperContentProps {
  steps: Step[];
}

export default function MultiStepperContent({ steps }: MultiStepperContentProps) {
  const searchParams = useSearchParams();
  const currentStepId = searchParams.get("stepId") || steps[0]?.id;
  const currentStepIndex = steps.findIndex((step) => step.id === currentStepId);
  const currentStep = steps[currentStepIndex];
  const CurrentComponent = currentStep?.component;

  if (!currentStep || !CurrentComponent) {
    console.log("[MultiStepper] No valid step found. Redirecting to the first step.");
    return null;
  }

  return (
    <Suspense fallback={<div>Loading...</div>}>
      <div className="max-w-4xl mx-auto">
        <div className="bg-white rounded-lg shadow-sm p-6">
          <CurrentComponent />
        </div>
      </div>
    </Suspense>
  );
}
