import { ChevronRight } from "lucide-react";
import Link from "next/link";
import React from "react";

const SidebarNavCompany = () => {
  return (
    <>
      <ul className="space-y-4">
        <li>
          <Link
            href={"/company-dashboard/active-jobs"}
            className="flex justify-between text-blue-100 font-bold"
          >
            <span>Active Jobs</span>
            {/* <span>1</span> */}
          </Link>
        </li>
        <li>
          <Link
            href={"/company-dashboard/all-shortlisted-applicants"}
            className="flex justify-between text-blue-100 font-bold"
          >
            <span>Shortlisted Candidate</span>
            {/* <span>1</span> */}
          </Link>
        </li>
      </ul>
      <hr className="my-5" />
      <ul className="space-y-4">
        <li>
          <Link
            href={"/company-dashboard/all-jobs"}
            className="flex justify-between text-black-100 font-medium"
          >
            <span>Dashboard</span>
            <span>
              {" "}
              <ChevronRight />{" "}
            </span>
          </Link>
        </li>
        <li>
          <Link
            href={"/company-dashboard/all-applicants"}
            className="flex justify-between text-black-100 font-medium"
          >
            <span>All Applicants</span>
            <span>
              {" "}
              <ChevronRight />{" "}
            </span>
          </Link>
        </li>
        <li>
          <Link
            href={"/company-dashboard/all-shortlisted-applicants"}
            className="flex justify-between text-black-100 font-medium"
          >
            <span>My Shortlisted Applicants</span>
            <span>
              {" "}
              <ChevronRight />{" "}
            </span>
          </Link>
        </li>
        <li>
          <Link
            href={"/company-settings/company-profile"}
            className="flex justify-between text-black-100 font-medium"
          >
            <span>Settings</span>
            <span>
              {" "}
              <ChevronRight />{" "}
            </span>
          </Link>
        </li>
      </ul>
    </>
  );
};

export default SidebarNavCompany;
