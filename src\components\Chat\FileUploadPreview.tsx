"use client";

import { X, FileText, ImageIcon, Video } from "lucide-react";
import { memo } from "react";
import type { IMediaFile } from "@/types/query.types";

interface FileUploadPreviewProps {
  files: (File | IMediaFile)[];
  onRemove?: (index: number) => void;
  isUploaded?: boolean;
}

const FileUploadPreview = memo(function FileUploadPreview({
  files,
  onRemove,
  isUploaded = false,
}: FileUploadPreviewProps) {
  if (files.length === 0) return null;

  return (
    <div className="px-4 pt-2">
      <div className="flex flex-wrap gap-2">
        {files.map((file, index) => {
          const fileName = "fileName" in file ? file.fileName : file.name;
          const fileSize = "fileSize" in file ? file.fileSize : file.size;
          const fileType = fileName.split(".").pop()?.toLowerCase() || "";
          const isImage = ["jpg", "jpeg", "png", "gif", "webp"].includes(fileType);
          const isVideo = ["mp4", "webm", "ogg"].includes(fileType);

          return (
            <div key={index} className="relative bg-gray-100 rounded-md p-2 mb-2 flex items-center">
              {isImage ? (
                <ImageIcon className="h-5 w-5 mr-2" />
              ) : isVideo ? (
                <Video className="h-5 w-5 mr-2" />
              ) : (
                <FileText className="h-5 w-5 mr-2" />
              )}
              <div className="flex-1 truncate">
                <p className="text-sm font-medium truncate">{fileName}</p>
                <p className="text-xs text-gray-500">{(fileSize / 1024).toFixed(1)} KB</p>
              </div>
              {!isUploaded && onRemove && (
                <button
                  type="button"
                  onClick={() => onRemove(index)}
                  className="text-red-500 hover:text-red-700"
                >
                  <X className="h-4 w-4" />
                </button>
              )}
            </div>
          );
        })}
      </div>
    </div>
  );
});

export default FileUploadPreview;
