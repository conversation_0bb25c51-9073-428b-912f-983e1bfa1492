import React, { ReactNode } from "react";
interface ContactCardProps {
  icon: ReactNode;
  title: string;
  description: string;
}

const ContactCard: React.FC<ContactCardProps> = ({ icon, title, description }) => {
  return (
    <div className="p-8 rounded-2xl border border-orange-100 text-orange-100">
      <div>{icon}</div>
      <div className="font-bold text-2xl text-black-100 mb-2">{title}</div>
      <div className="text-gray-100 text-lg leading-7 font-normal">{description}</div>
    </div>
  );
};

export default ContactCard;
