import React from "react";
import PrimaryButton from "../Buttons/PrimaryButton";

interface HowItInfoCardProps {
  mainTitle: string;
  smallTitle: string;
  description: string;
  link: string;
  buttonText: string;
}

const HowItInfoCard: React.FC<HowItInfoCardProps> = ({
  mainTitle,
  smallTitle,
  description,
  link,
  buttonText,
}) => {
  return (
    <div>
      <h3 className="text-orange-100 font-medium text-base mb-5">{smallTitle}</h3>
      <h1 className="text-black-100 font-bold text-5xl mb-5">{mainTitle}</h1>
      <p className="text-gray-100 text-lg leading-7 mb-7">{description}</p>
      <PrimaryButton link={link} text={buttonText} />
    </div>
  );
};

export default HowItInfoCard;
