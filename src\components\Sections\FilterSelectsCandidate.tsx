"use client";

import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useGetAllEnums } from "@/hooks/useQuery";
import type { IJobSeekerSearchParams, IJobSeekerFilterState } from "@/types/query.types";

interface FilterSelectsCandidateProps {
  filters: IJobSeekerFilterState;
  onFilterChange: (key: keyof IJobSeekerSearchParams, value: string | undefined) => void;
}

export function FilterSelectsCandidate({ filters, onFilterChange }: FilterSelectsCandidateProps) {
  const { data: enumsData } = useGetAllEnums();

  const handleValueChange = (key: keyof IJobSeekerSearchParams, value: string) => {
    onFilterChange(key, value === "all" ? undefined : value);
  };

  const jobTypes = enumsData?.data.JOB_TYPE_ENUM
    ? Object.entries(enumsData.data.JOB_TYPE_ENUM).map(([key, _value]) => ({
        label: key.replace(/_/g, " "),
        value: key,
      }))
    : [];

  const jobCategories = enumsData?.data.JOB_CATEGORY_ENUM
    ? Object.entries(enumsData.data.JOB_CATEGORY_ENUM).map(([key, _value]) => ({
        label: key.replace(/_/g, " "),
        value: key,
      }))
    : [];

  return (
    <div className="flex overflow-x-auto gap-4 lg:p-4 w-full max-w-5xl">
      <Select
        value={filters.jobType || "all"}
        onValueChange={(value) => handleValueChange("jobType", value)}
      >
        <SelectTrigger className="w-full bg-white rounded-full h-[56px] px-6 text-gray-100 text-base">
          <SelectValue placeholder="Job Type" />
        </SelectTrigger>
        <SelectContent>
          <SelectGroup>
            <SelectItem value="all">All Job Types</SelectItem>
            {jobTypes.map(({ label, value }) => (
              <SelectItem key={value} value={value}>
                {label}
              </SelectItem>
            ))}
          </SelectGroup>
        </SelectContent>
      </Select>

      <Select
        value={filters.jobCategory || "all"}
        onValueChange={(value) => handleValueChange("jobCategory", value)}
      >
        <SelectTrigger className="w-full bg-white rounded-full h-[56px] px-6 text-gray-100 text-base">
          <SelectValue placeholder="Job Category" />
        </SelectTrigger>
        <SelectContent>
          <SelectGroup>
            <SelectItem value="all">All Job Categories</SelectItem>
            {jobCategories.map(({ label, value }) => (
              <SelectItem key={value} value={value}>
                {label}
              </SelectItem>
            ))}
          </SelectGroup>
        </SelectContent>
      </Select>
    </div>
  );
}
