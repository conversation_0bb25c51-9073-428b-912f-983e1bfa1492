import type { <PERSON>ada<PERSON> } from "next";
import Image from "next/image";
import Link from "next/link";
import type React from "react";

export const metadata: Metadata = {
  title: "Authentication - YesJobs",
  description: "Login, register, and manage your account",
};

export default function AuthLayout({ children }: { children: React.ReactNode }) {
  return (
    <div className="min-h-screen bg-white">
      <div className="fixed right-0 aspect-[723/942] h-screen hidden lg:block">
        <Link href={"/"}>
          <Image
            src={"/images/auth-layout-right.png"}
            alt="Authentication background"
            width={1012}
            height={1106}
            className="ml-auto h-screen object-cover"
            priority
          />
        </Link>
      </div>
      <div className="container mx-auto relative">
        <div className="absolute bottom-0 left-0 w-[329px] h-[329px] flex-shrink-0 rounded-full bg-[#EC761E] blur-[222px] z-[-1] opacity-50"></div>
        <div className="absolute bottom-0 right-0 w-[329px] h-[329px] flex-shrink-0 rounded-full bg-[#EC761E] blur-[222px] z-[-1] opacity-50"></div>
        <div className="flex flex-col justify-between min-h-screen py-10 gap-10">
          <Link href={"/"} className="block">
            <Image src={"/images/logo.png"} alt="YesJobs Logo" width={140} height={50} priority />
          </Link>
          <div className="sm:w-[480px] lg:pl-10 mx-auto sm:mx-0 bg-white/80 backdrop-blur-sm p-8 rounded-2xl shadow-lg">
            {children}
          </div>
          <div>
            <p className="font-medium text-sm text-black-100">
              © {new Date().getFullYear()} YesJobs. All Rights Reserved.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
