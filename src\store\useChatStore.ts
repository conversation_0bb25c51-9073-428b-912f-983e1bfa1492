import { create } from "zustand";
import { persist } from "zustand/middleware";
import { API_ROUTES } from "@/constants/api.routes";
import axiosInstance from "@/lib/axios";
import { debugLog } from "@/lib/debug";
import { SocketConnectionStatus } from "@/service/socket.service";
import {
  ICreateConversationRequestDto,
  ICreateConversationResponseDto,
  IDeleteConversationResponseDto,
  IDeleteMessageRequestDto,
  IDeleteMessageResponseDto,
  ISendMessageRequestDto,
  ISendMessageResponseDto,
  IUploadMediaResponseDto,
} from "@/types/mutation.types";
import { IMessage } from "@/types/query.types";

/**
 * Message status types
 */
type MessageStatus = "sending" | "sent" | "delivered" | "read" | "error";

/**
 * Typing status interface
 * Maps conversation IDs to boolean typing status
 */
interface TypingStatus {
  [conversationId: string]: boolean;
}

/**
 * Pending message interface
 * Represents a message that has been sent but not yet confirmed by the server
 */
interface PendingMessage {
  id: string;
  conversationId: string;
  content: string;
  mediaFiles: Array<{
    url: string;
    s3Key: string;
    fileName: string;
    fileSize: number;
  }>;
  status: MessageStatus;
  createdAt: string;
}

/**
 * Chat store interface
 * Manages the state of the chat functionality
 */
interface ChatStore {
  // State
  activeConversationId: string | null;
  typingStatus: TypingStatus;
  pendingMessages: PendingMessage[];
  socketStatus: SocketConnectionStatus;
  hasHydrated: boolean;
  lastMessages: { [conversationId: string]: IMessage | null };
  messages: { [conversationId: string]: IMessage[] };
  deletedMessages: { [messageId: string]: boolean };
  deletedConversations: { [conversationId: string]: boolean };

  // Actions
  setActiveConversation: (id: string | null) => void;
  setTyping: (id: string, status: boolean) => void;
  addPendingMessage: (message: PendingMessage) => void;
  updatePendingMessageStatus: (id: string, status: MessageStatus) => void;
  updatePendingMessageId: (oldId: string, newId: string) => void;
  removePendingMessage: (id: string) => void;
  setSocketStatus: (status: SocketConnectionStatus) => void;
  setHasHydrated: (hydrated: boolean) => void;
  updateLastMessage: (conversationId: string, message: IMessage | null) => void;
  addMessage: (conversationId: string, message: IMessage) => void;
  updateMessage: (conversationId: string, messageId: string, updates: Partial<IMessage>) => void;
  deleteMessage: (
    conversationId: string,
    messageId: string,
    deleteForEveryone?: boolean,
    userId?: string
  ) => void;
  deleteConversation: (conversationId: string) => void;
  clearMessages: (conversationId: string) => void;
}

/**
 * Chat store implementation using Zustand
 * Uses persist middleware to save state to localStorage
 */
export const useChatStore = create<ChatStore>()(
  persist(
    (set) => ({
      // Initial state
      activeConversationId: null,
      typingStatus: {},
      pendingMessages: [],
      socketStatus: SocketConnectionStatus.DISCONNECTED,
      hasHydrated: false,
      lastMessages: {},
      messages: {},
      deletedMessages: {},
      deletedConversations: {},

      // Actions
      setActiveConversation: (id) => {
        debugLog(`Setting active conversation: ${id}`);
        set({ activeConversationId: id });
      },

      setTyping: (id, status) => {
        set((state) => {
          // Only update if the status has changed
          if (state.typingStatus[id] !== status) {
            debugLog(
              `Setting typing status for conversation ${id}: ${status ? "typing" : "not typing"}`
            );
            return {
              typingStatus: { ...state.typingStatus, [id]: status },
            };
          }
          return state;
        });
      },

      addPendingMessage: (message) => {
        debugLog(`Adding pending message: ${message.id}`);
        set((state) => ({
          pendingMessages: [...state.pendingMessages, message],
        }));
      },

      updatePendingMessageStatus: (id, status) => {
        debugLog(`Updating pending message status: ${id} -> ${status}`);
        set((state) => ({
          pendingMessages: state.pendingMessages.map((msg) =>
            msg.id === id ? { ...msg, status } : msg
          ),
        }));
      },

      updatePendingMessageId: (oldId, newId) => {
        debugLog(`Updating pending message ID: ${oldId} -> ${newId}`);
        set((state) => ({
          pendingMessages: state.pendingMessages.map((msg) =>
            msg.id === oldId ? { ...msg, id: newId, status: "sent" } : msg
          ),
        }));
      },

      removePendingMessage: (id) => {
        debugLog(`Removing pending message: ${id}`);
        set((state) => ({
          pendingMessages: state.pendingMessages.filter((msg) => msg.id !== id),
        }));
      },

      setSocketStatus: (status) => {
        debugLog(`Setting socket status: ${status}`);
        set({ socketStatus: status });
      },

      setHasHydrated: (hydrated) => {
        set({ hasHydrated: hydrated });
      },

      updateLastMessage: (conversationId, message) => {
        debugLog(`Updating last message for conversation ${conversationId}`);
        set((state) => ({
          lastMessages: {
            ...state.lastMessages,
            [conversationId]: message,
          },
        }));
      },

      addMessage: (conversationId, message) => {
        debugLog(`Adding message to conversation ${conversationId}`);
        set((state) => ({
          messages: {
            ...state.messages,
            [conversationId]: [...(state.messages[conversationId] || []), message],
          },
        }));
      },

      updateMessage: (conversationId, messageId, updates) => {
        debugLog(`Updating message ${messageId} in conversation ${conversationId}`);
        set((state) => ({
          messages: {
            ...state.messages,
            [conversationId]:
              state.messages[conversationId]?.map((msg) =>
                msg._id === messageId ? { ...msg, ...updates } : msg
              ) || [],
          },
        }));
      },

      deleteMessage: (conversationId, messageId, deleteForEveryone = false, userId = "") => {
        debugLog(
          `Deleting message ${messageId} in conversation ${conversationId}, deleteForEveryone: ${deleteForEveryone}`
        );
        set((state) => {
          const messages = state.messages[conversationId] || [];
          const messageIndex = messages.findIndex((msg) => msg._id === messageId);

          if (messageIndex === -1 || !messages[messageIndex]) {
            return state;
          }

          const message = messages[messageIndex];
          const updatedMessages = [...messages];

          if (deleteForEveryone) {
            updatedMessages[messageIndex] = {
              ...message,
              content: "This message has been deleted",
              deletedForEveryone: true,
              updatedAt: new Date().toISOString(),
            };
          } else if (userId) {
            const deletedBy = [...(message.deletedBy || [])];
            if (!deletedBy.includes(userId)) {
              deletedBy.push(userId);
            }
            updatedMessages[messageIndex] = {
              ...message,
              deletedBy,
            };
          }

          // Check if this is the last message in the conversation
          const lastMessage = state.lastMessages[conversationId];
          const updatedLastMessages = { ...state.lastMessages };

          if (lastMessage && lastMessage._id === messageId) {
            // Ensure we're assigning a non-undefined value
            const updatedMessage = updatedMessages[messageIndex];
            if (updatedMessage) {
              updatedLastMessages[conversationId] = updatedMessage;
            }
          }

          return {
            messages: {
              ...state.messages,
              [conversationId]: updatedMessages,
            },
            lastMessages: updatedLastMessages,
            deletedMessages: {
              ...state.deletedMessages,
              [messageId]: true,
            },
          };
        });
      },

      deleteConversation: (conversationId) => {
        debugLog(`Deleting conversation ${conversationId}`);
        set((state) => ({
          deletedConversations: {
            ...state.deletedConversations,
            [conversationId]: true,
          },
          messages: {
            ...state.messages,
            [conversationId]: [],
          },
          lastMessages: {
            ...state.lastMessages,
            [conversationId]: null,
          },
        }));
      },

      clearMessages: (conversationId) => {
        debugLog(`Clearing messages for conversation ${conversationId}`);
        set((state) => ({
          messages: {
            ...state.messages,
            [conversationId]: [],
          },
        }));
      },
    }),
    {
      name: "chat-store",
      skipHydration: true,
    }
  )
);

/**
 * Helper function to create a pending message
 * @param conversationId The ID of the conversation
 * @param content The message content
 * @param mediaFiles Optional media files to attach
 * @returns A new pending message object
 */
export const createPendingMessage = (
  conversationId: string,
  content: string,
  mediaFiles: Array<{
    url: string;
    s3Key: string;
    fileName: string;
    fileSize: number;
  }> = []
): PendingMessage => {
  return {
    id: `pending-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
    conversationId,
    content,
    mediaFiles,
    status: "sending",
    createdAt: new Date().toISOString(),
  };
};

/**
 * Helper function to convert a pending message to an IMessage
 * @param pendingMessage The pending message to convert
 * @param currentUserId The current user's ID
 * @param userRole The current user's role
 * @returns An IMessage object
 */
export const pendingMessageToIMessage = (
  pendingMessage: PendingMessage,
  currentUserId: string,
  userRole: string
): IMessage => {
  return {
    _id: pendingMessage.id,
    conversationId: pendingMessage.conversationId,
    senderId: currentUserId, // Use string ID to match API response format
    senderType: userRole,
    content: pendingMessage.content,
    mediaFiles: pendingMessage.mediaFiles,
    deletedBy: [],
    deletedForEveryone: false,
    read: false,
    createdAt: pendingMessage.createdAt,
    updatedAt: pendingMessage.createdAt,
    status: pendingMessage.status, // Include the status from the pending message
  };
};

/**
 * Create a new conversation
 * @param conversationData The conversation data
 * @returns Promise that resolves to the created conversation
 */
export const createConversation = async (
  conversationData: ICreateConversationRequestDto
): Promise<ICreateConversationResponseDto> => {
  const { data } = await axiosInstance.post<ICreateConversationResponseDto>(
    API_ROUTES.CONVERSATIONS.BASE,
    conversationData
  );
  return data;
};

/**
 * Delete a conversation
 * @param conversationId The ID of the conversation to delete
 * @returns Promise that resolves when the conversation is deleted
 */
export const deleteConversation = async (
  conversationId: string
): Promise<IDeleteConversationResponseDto> => {
  const { data } = await axiosInstance.delete<IDeleteConversationResponseDto>(
    API_ROUTES.CONVERSATIONS.DELETE_CONVERSATION(conversationId)
  );
  return data;
};

/**
 * Send a message via API
 * @param messageData The message data
 * @returns Promise that resolves to the sent message
 */
export const sendMessage = async (
  messageData: ISendMessageRequestDto
): Promise<ISendMessageResponseDto> => {
  const { data } = await axiosInstance.post<ISendMessageResponseDto>(
    API_ROUTES.MESSAGES.BASE,
    messageData
  );
  return data;
};

/**
 * Delete a message
 * @param messageId The ID of the message to delete
 * @param deleteForEveryone Whether to delete the message for everyone
 * @returns Promise that resolves when the message is deleted
 */
export const deleteMessage = async ({
  messageId,
  deleteForEveryone = false,
}: IDeleteMessageRequestDto): Promise<IDeleteMessageResponseDto> => {
  const { data } = await axiosInstance.delete<IDeleteMessageResponseDto>(
    `${API_ROUTES.MESSAGES.DELETE_MESSAGE(messageId)}${
      deleteForEveryone ? "?deleteForEveryone=true" : ""
    }`
  );
  return data;
};

/**
 * Upload media for chat and send message
 * @param formData The form data containing the media
 * @param conversationId The conversation ID
 * @param content The message content
 * @param onProgress Optional progress callback
 * @returns Promise that resolves to the uploaded media and sent message
 */
export const uploadMediaForChat = async (
  formData: FormData,
  conversationId: string,
  content: string,
  onProgress?: (progress: number) => void
): Promise<IUploadMediaResponseDto> => {
  const { data } = await axiosInstance.post<IUploadMediaResponseDto>(
    API_ROUTES.UPLOAD.MEDIA_CHAT,
    formData,
    {
      headers: {
        "Content-Type": "multipart/form-data",
      },
      onUploadProgress: (progressEvent) => {
        if (onProgress && progressEvent.total) {
          const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total);
          onProgress(progress);
        }
      },
    }
  );

  // If media upload is successful, send the message with media
  if (data.success && data.data) {
    const messageData: ISendMessageRequestDto = {
      conversationId,
      content,
      mediaFiles: data.data.mediaFiles.map((file) => ({
        url: file.url,
        s3Key: file.s3Key,
        fileName: file.fileName,
        fileSize: file.fileSize,
      })),
    };

    // Send the message with media
    await axiosInstance.post<ISendMessageResponseDto>(API_ROUTES.MESSAGES.BASE, messageData);
  }

  return data;
};

/**
 * Check if a message is visible to the user
 * @param message The message to check
 * @param userId The ID of the current user
 * @returns True if the message is visible, false otherwise
 */
export const isMessageVisible = (message: IMessage, userId: string): boolean => {
  // Message should be visible if:
  // 1. Message is deleted for everyone (but we'll show it as "deleted")
  // 2. Message is NOT in the deletedBy array for current user
  return message.deletedForEveryone || !message.deletedBy?.includes(userId);
};
