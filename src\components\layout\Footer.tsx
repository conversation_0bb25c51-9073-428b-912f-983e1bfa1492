import Image from "next/image";
import Link from "next/link";
import React from "react";
import FooterHeading from "../Headings/FooterHeading";
import { Button } from "../ui/button";
import { Input } from "../ui/input";
import { NavLink } from "./NavLink";

const Footer = () => {
  return (
    <footer className=" bg-offWhite-100">
      <div>
        <div className="container grid gap-8 grid-cols-1 md:grid-cols-2 lg:grid-cols-5 mx-auto pb-12 pt-20">
          <div className="space-y-6 lg:col-span-2">
            <Link href="/" className="inline-block">
              <Image
                src="/images/logo.png"
                alt="Jobs Logo"
                width={100}
                height={40}
                className="h-10 w-auto"
              />
            </Link>
            <p className="text-gray-100 max-w-[250px]">
              Join forces with tech talent to help build your dream team with ease
            </p>
            <div className="flex space-x-4">
              <Link href="#" className="text-gray-600 hover:text-gray-900">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="32"
                  height="32"
                  fill="none"
                  viewBox="0 0 32 32"
                >
                  <path
                    fill="#EC761E"
                    d="M24.448 24.452h-3.554v-5.57c0-1.327-.027-3.036-1.852-3.036-1.853 0-2.136 1.445-2.136 2.939v5.667h-3.554V13h3.414v1.56h.046c.477-.9 1.637-1.849 3.37-1.849 3.601 0 4.267 2.37 4.267 5.455zM9.338 11.433a2.06 2.06 0 0 1-2.063-2.065 2.064 2.064 0 1 1 2.063 2.065m1.782 13.019H7.556V13h3.564zM26.226 4H5.772c-.979 0-1.77.774-1.77 1.729v20.542c0 .956.791 1.729 1.77 1.729h20.451c.978 0 1.778-.773 1.778-1.729V5.73c0-.955-.8-1.729-1.778-1.729z"
                  ></path>
                </svg>
              </Link>
              <Link href="#" className="text-gray-600 hover:text-gray-900">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="32"
                  height="32"
                  fill="none"
                  viewBox="0 0 32 32"
                >
                  <path
                    fill="#EC761E"
                    d="M17.37 2.667c1.501.003 2.262.01 2.919.03l.259.009c.299.01.594.024.95.04 1.418.066 2.386.29 3.236.62a6.5 6.5 0 0 1 2.362 1.538 6.54 6.54 0 0 1 1.538 2.363c.328.848.553 1.817.62 3.236.016.356.028.65.039.95l.008.258c.02.657.029 1.418.032 2.919v2.74c-.003 1.5-.01 2.261-.03 2.918l-.009.259c-.01.3-.024.594-.04.95-.066 1.418-.292 2.386-.62 3.236a6.5 6.5 0 0 1-1.538 2.362 6.55 6.55 0 0 1-2.362 1.538c-.85.33-1.818.553-3.237.62-.355.016-.65.029-.95.04l-.258.008c-.657.02-1.418.028-2.918.031l-.994.001H14.63c-1.5-.003-2.261-.011-2.918-.03l-.259-.01a84 84 0 0 1-.95-.04c-1.418-.065-2.385-.29-3.236-.62a6.5 6.5 0 0 1-2.362-1.538 6.5 6.5 0 0 1-1.538-2.362c-.33-.85-.553-1.818-.62-3.236-.016-.356-.029-.65-.04-.95l-.008-.259c-.02-.657-.028-1.417-.03-2.918l-.001-2.74c.002-1.501.01-2.262.03-2.919l.008-.258c.011-.3.024-.594.041-.95.066-1.42.29-2.386.62-3.236a6.5 6.5 0 0 1 1.538-2.363 6.5 6.5 0 0 1 2.362-1.537c.85-.33 1.817-.554 3.237-.62.355-.016.65-.03.95-.04l.258-.008c.657-.02 1.418-.028 2.918-.031zM16 9.333a6.666 6.666 0 1 0 0 13.333 6.666 6.666 0 1 0 0-13.333M16 12a4 4 0 1 1 .001 7.999 4 4 0 0 1 0-8m7-4.667c-.919 0-1.666.747-1.666 1.666a1.668 1.668 0 0 0 3.333 0c0-.92-.749-1.667-1.667-1.666"
                  ></path>
                </svg>
              </Link>
              <Link href="#" className="text-gray-600 hover:text-gray-900">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="32"
                  height="32"
                  fill="none"
                  viewBox="0 0 32 32"
                >
                  <path
                    fill="#EC761E"
                    d="M13.984 19.535 20.334 28h9.333L19.189 14.03 27.908 4h-3.534l-6.822 7.848L11.667 4H2.333l10.015 13.353L3.092 28h3.533zm7.683 5.798-14-18.666h2.666l14 18.666z"
                  ></path>
                </svg>
              </Link>
              <Link href="#" className="text-gray-600 hover:text-gray-900">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="32"
                  height="32"
                  fill="none"
                  viewBox="0 0 32 32"
                >
                  <path
                    fill="#EC761E"
                    d="M16.325 5.334c.712.003 2.494.02 4.387.096l.671.03c1.906.09 3.81.244 4.755.508 1.26.353 2.25 1.386 2.585 2.695.533 2.079.6 6.136.608 7.118l.001.204v.028l-.001.204c-.008.982-.075 5.039-.608 7.118-.34 1.314-1.33 2.346-2.585 2.696-.944.263-2.849.417-4.755.507l-.67.03c-1.894.075-3.676.093-4.388.097h-.652c-1.508-.009-7.81-.077-9.814-.634-1.26-.354-2.25-1.387-2.584-2.696-.533-2.079-.6-6.136-.608-7.118v-.436c.008-.982.075-5.039.608-7.118.339-1.314 1.33-2.346 2.584-2.695 2.005-.558 8.307-.626 9.814-.635zm-2.993 5.998v9.334l8-4.667z"
                  ></path>
                </svg>
              </Link>
            </div>
          </div>
          <div>
            {/* <h3 className="font-semibold mb-4">Company</h3> */}
            <FooterHeading text="Company" />
            <ul className="space-y-3">
              <li>
                <NavLink href="/">Home</NavLink>
              </li>
              <li>
                <NavLink href="/for-candidates">Browse Jobs</NavLink>
              </li>
              <li>
                <NavLink href="/about-us">About Us</NavLink>
              </li>
              <li>
                <NavLink href="/contact">Contact Us</NavLink>
              </li>
            </ul>
          </div>
          <div>
            <FooterHeading text="Information" />
            <ul className="space-y-3">
              {/* <li>
                <NavLink href="">Our Blog</NavLink>
              </li> */}
              <li>
                <NavLink href="/privacy-policy">Privacy Policy</NavLink>
              </li>
              <li>
                <NavLink href="/terms-and-conditions">Terms of Service</NavLink>
              </li>
              {/* <li>
                <NavLink href="">Hiring Events</NavLink>
              </li> */}
            </ul>
          </div>
          <div>
            <FooterHeading text="Newsletter" />
            <p className=" text-gray-100 mb-4">
              Subscribe to our newsletter for the latest job updates and career tips
            </p>
            <div className="flex gap-2 bg-white rounded-full py-3 px-3">
              <Input
                type="email"
                placeholder="Enter Your Email"
                className="border-none shadow-none outline-none"
              />
              <Button className="bg-[#FF6634] hover:bg-[#FF6634]/90 border-none rounded-full">
                Subscribe
              </Button>
            </div>
          </div>
        </div>
        <div className="text-center bg-black-100 text-white py-4 ">
          <p>© 2025 YesJobs. All Rights Reserved.</p>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
